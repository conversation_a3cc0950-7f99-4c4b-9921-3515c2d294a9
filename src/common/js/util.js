var SIGN_REGEXP = /([yMdHhsm])(\1*)/g;
var DEFAULT_PATTERN = 'yyyy-MM-dd';
function padding(s, len) {
    var len = len - (s + '').length;
    for (var i = 0; i < len; i++) { s = '0' + s; }
    return s;
};
// 字符转日期（yyyyMMdd -> yyyy-MM-dd）
var yyyyMMdd2DateRegex = /^(\d{4})(\d{2})(\d{2})$/;


import Store from '../../vuex/store';
import emEnum from './emEnum';
// 导出Execl文件用
import FileSaver from 'file-saver';
import XLSX from 'xlsx';
import { export_json_to_excel } from '@/vendor/Export2Excel';
import { export_json_to_excel_mult } from '@/vendor/Export2Excel';
import { export_json_to_excel_multth } from '@/vendor/Export2Excel';

export default {
    // 公用正则表达式（不能内部使用，需考虑怎么内部使用）
    Regex: {
        // 日期字符串转时间
        yyyyMMdd2DateRegex: yyyyMMdd2DateRegex,
        longDateRegex: /^(\d{1,4})(-|\/)(\d{1,2})\2(\d{1,2}) (\d{1,2}):(\d{1,2}):(\d{1,2})$/,
    },
    // 获取查询字符串
    getQueryStringByName: function (name) {
        var reg = new RegExp("(^|&)" + name + "=([^&]*)(&|$)", "i");
        var r = window.location.search.substr(1).match(reg);
        var context = "";
        if (r != null)
            context = r[2];
        reg = null;
        r = null;
        return context == null || context == "" || context == "undefined" ? "" : context;
    },
    // 日期格式化
    formatDate: {
        // 日期转字符串
        format: function (date, pattern) {
            pattern = pattern || DEFAULT_PATTERN;
            return pattern.replace(SIGN_REGEXP, function ($0) {
                switch ($0.charAt(0)) {
                    case 'y': return padding(date.getFullYear(), $0.length);
                    case 'M': return padding(date.getMonth() + 1, $0.length);
                    case 'd': return padding(date.getDate(), $0.length);
                    case 'w': return date.getDay() + 1;
                    case 'H': return padding(date.getHours(), $0.length);
                    case 'h': return padding(date.getHours(), $0.length);
                    case 'm': return padding(date.getMinutes(), $0.length);
                    case 's': return padding(date.getSeconds(), $0.length);
                }
            });
        },
        // 格式化yyyyMMdd格式的日期字符串
        yyyyMMdd2Caption: function (prefix, dateString) {
            // 把yyyyMMdd格式的日期字符串转为可用的时间字符串
            let dateStr = this.yyyyMMdd2Date(dateString);
            let week = this.date2Week(prefix, dateStr);
            let formatStr = this.format(new Date(dateStr), "M月d日");
            return week + ' ' + formatStr;
        },
        // 格式化日期字符串
        date2Caption: function (prefix, dateString) {
            // 获取周几
            let week = this.date2Week(prefix, dateString);
            let formatStr = this.format(new Date(dateString), "M月d日");
            return week + ' ' + formatStr;
        },
        // 判断开始时间是否在时间段内
        dateIsBlongRange: function (dateString, dateRange) {
            // 要判断的时间
            let date = this.format(new Date(dateString.replace(/-/g, '/')), "yyyy-MM-dd HH:mm:ss");
            let dateDate = new Date(date.replace(/-/g, '/'));
            // 获取时间段的开始和结束时间
            let begin = dateRange.substr(0, dateRange.indexOf("~"));
            let end = dateRange.substr(dateRange.indexOf("~") + 1);
            let dateStr = this.format(new Date(dateString.replace(/-/g, '/')), "yyyy-MM-dd");
            let beginDate = new Date(dateStr.replace(/-/g, '/') + " " + begin + ":00");
            let endDate = new Date(dateStr.replace(/-/g, '/') + " " + end + ":00");
            // 返回是否在范围内
            return !!dateDate && !! beginDate && !!endDate && beginDate <= dateDate && endDate > dateDate;
        },
        // 字符转日期（yyyyMMdd -> yyyy-MM-dd）
        yyyyMMdd2Date: function (dateString) {
            if (!!dateString) {
                return dateString.replace(yyyyMMdd2DateRegex, "$1-$2-$3");
            }
            return null;
        },
        // 根据日期获取周几
        date2Week: function (prefix, dateString) {
            if (!!dateString) {
                let date = new Date(dateString.replace(/-/g, '/'));
                return prefix + "日一二三四五六".charAt(date.getDay());
            }
            return prefix;
        },
        // 字符串转日期
        parse: function (dateString, pattern) {
            var matchs1 = pattern.match(SIGN_REGEXP);
            var matchs2 = dateString.match(/(\d)+/g);
            if (matchs1.length == matchs2.length) {
                var _date = new Date(1970, 0, 1);
                for (var i = 0; i < matchs1.length; i++) {
                    var _int = parseInt(matchs2[i]);
                    var sign = matchs1[i];
                    switch (sign.charAt(0)) {
                        case 'y': _date.setFullYear(_int); break;
                        case 'M': _date.setMonth(_int - 1); break;
                        case 'd': _date.setDate(_int); break;
                        case 'H': _date.setHours(_int); break;
                        case 'h': _date.setHours(_int); break;
                        case 'm': _date.setMinutes(_int); break;
                        case 's': _date.setSeconds(_int); break;
                    }
                }
                return _date;
            }
            return null;
        }
    },
    // 导入Excel
    importExcel: function (file) {
        // 暂不用
    },
    file2Xce: function (file) {
        return new Promise(function(resolve, reject) {
            const reader = new FileReader();
            reader.onload = function(e) {
                const data = e.target.result
                this.wb = XLSX.read(data, {
                    type: 'binary'
                });
                const result = []
                this.wb.SheetNames.forEach((sheetName) => {
                    result.push({
                        sheetName: sheetName,
                        sheet: XLSX.utils.sheet_to_json(this.wb.Sheets[sheetName], {defval:""})
                    });
                });
                resolve(result);
            }
            //reader.readAsBinaryString(file.raw);
            reader.readAsBinaryString(file);  // 传统input方法
        });
    },
    // 导出Execl
    exportExcel: function (fileName, outTable) {
        // 转换成excel时，使用原始的格式
        var xlsxParam = { raw: true };
        // 从Table中生成工作簿对象
        var wb = XLSX.utils.table_to_book(outTable, xlsxParam);
        // 获取作为输出的二进制字符串
        var wbout = XLSX.write(wb, { bookType: 'xlsx', bookSST: true, type: 'array' })
        try {
            FileSaver.saveAs(new Blob([wbout], { type: 'application/octet-stream' }), fileName + '.xlsx');
        } catch (e) {
            if (typeof console !== 'undefined') console.log(e, wbout);
        }
        return wbout;
    },
    // 导出Json数据到Execl
    exportJsonToExcel: function (fileName, outJson, tHeader, filterVal) {  // 正常导出
        outJson = JSON.parse(outJson);
        let data = this.formatJson(outJson, filterVal);
        export_json_to_excel(tHeader, data, fileName);
    },
    exportJsonToExcelMult: function (fileName, outJson, heads, filters, sheetName) {  // 多sheet导出
        let _this = this;
        let outData = [];
        outJson = JSON.parse(outJson);
        outJson.forEach((item, index) => {
            let data = _this.formatJson(item, filters[index]);
            outData.push(data);
        });
        export_json_to_excel_mult(heads, outData, sheetName, fileName);
    },
    exportJsonToExcelMultTh: function (fileName, outJson, tHeader, filterVal) {  // 多head导出（根据head拼成要的格式）
        outJson = JSON.parse(outJson);
        let data = this.formatJson(outJson, filterVal);
        export_json_to_excel_multth(tHeader, data, fileName);
    },
    // 根据导出列格式化Json数据
    formatJson: function (jsonData, filterVal) {
        return jsonData.map(v => filterVal.map(j => v[j]));
    },
    // 用户数据相关处理
    userData: {
        // 获取登录用户的指定字段值
        getLoginUserFieldValue: function (filed, _this) {
            // 如果Store中没有保存用户数据，则获取sessionStorage中的用户数据
            if (!Store.state.loginUser || Store.state.loginUser === undefined) {

                let user = sessionStorage.getItem('user');
                if (user) {
                    Store.state.loginUser = JSON.parse(user);
                } else {
                    if (!!_this && emEnum.unLoginPathArray.indexOf(_this.$route.path) < 0) {  // _this 为空时是为了获取token
                        this.reLogin(_this);
                    } else {
                        return '';
                    }
                }
            }
            // 获取值
            return Store.state.loginUser[filed];
        },
        // 失效用户重新登录
        reLogin: function (_this, tip = true, msg = '用户失效，请重新登录！') {
            if (tip) {
                _this.$message({
                    message: msg,
                    type: 'error'
                });
            }
            this.clearSessionStorage();
            // 转到登录页面
            _this.$router.push({ path: '/login' });
        },
        // 清除 sessionStorage 中保存的数据
        clearSessionStorage: function () {
            sessionStorage.removeItem('user');
            Store.state.loginUser = null;
        }
    },
    // 编辑存储
    editStore: {
        // 获取编辑对象
        GetEditObject: function () {
            return Store.state.editObject;
        },
        // 获取编辑后转到的页面
        GetEditedToPath: function () {
            return Store.state.editedToPath;
        },
        // 编辑赋值
        SetEditValue: function (obj, path) {
            Store.state.editObject = obj;
            Store.state.editedToPath = path;
        },
        // 清空编辑赋值
        ClearEditValue: function () {
            Store.state.editObject = null;
            Store.state.editedToPath = '/home';
        },
        // 保存不显示列
        SetUnShowCols: function (page, UnShowCols) {
            // {name: page, cols: UnShowCols}
            let bFind = false;
            Store.state.UnShowColObj.forEach(item => {
                if(item.name==page){
                    item.cols = UnShowCols;
                    bFind = true;
                    return true;
                }
            });
            if(!bFind) {
                Store.state.UnShowColObj.push({name: page, cols: UnShowCols});
            }
        },
        // 获取不显示列
        GetUnShowCols: function (page) {
            let UnShowCols = "none";
            if(!!Store.state.UnShowColObj)
            {
                Store.state.UnShowColObj.forEach(item => {
                    if(item.name==page){
                        UnShowCols = item.cols;
                        return UnShowCols;
                    }
                });
            }
            return UnShowCols;
        },
    },
    // 字符串转为Dom
    string2Dom: function (arg) {
    　　 var objE = document.createElement("div");
    　　 objE.innerHTML = arg;
    　　 return objE.childNodes;
    },
    // 数组处理
    array: {
        // 向数组中增加元素
        addElement: function(array, ele) {
            array.push(ele);
            return array;
        },
        // 从数组中移除元素
        removeElement: function(array, ele) {
            let index = -1;
            for (var i = 0; i < array.length; i++) {
                if (array[i] == ele) {
                    index = i;
                }
            }
            if (index > -1) {
                array.splice(index, 1);
            }
            return array;
        }
    },
    // 比较(时间)
    compareDateTime: function (propertyName) {
        return function (object1, object2) {
            var value1 = new Date(object1[propertyName]);
            var value2 = new Date(object2[propertyName]);
            if (value2 > value1) {
                return -1;
            } else if (value2 < value1) {
                return 1;
            } else {
                return 0;
            }
        }
    },
    // 比较(id)
    compareID: function (propertyName) {
        return function (object1, object2) {
            var value1 = parseInt(object1[propertyName]);
            var value2 = parseInt(object2[propertyName]);
            if (value2 > value1) {
                return -1;
            } else if (value2 < value1) {
                return 1;
            } else {
                return 0;
            }
        }
    }
};
