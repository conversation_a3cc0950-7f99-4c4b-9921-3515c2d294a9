export default {
    // 系统名称
    cnName: '管理平台',  // 中文名称
    enName: 'management platform',//'Hanxu Consmetice Administration Platform',  // 英文名称
    // 版本
    version: '24.10.22.01',  // 版本：年后两位.月.日.当日次数
    // 地址相关
    url: {
        // 接口地址
        baseURL: 'https://kzm.javaapi.hyfun.net/shhzp',  // 公共接口url（正式）
        baseURL2: 'https://kzm.api.hyfun.net/api/v2',  // 公共接口url（正式）
        // baseURL: process.env.NODE_ENV == 'development' ? 'http://*************:2222/shhzp' : 'https://kzm.javaapi.hyfun.net/shhzp',  // 公共接口url测试
        // baseURL2: process.env.NODE_ENV == 'development' ? 'http://localhost:8000/api/v2' : 'https://kzm.api.hyfun.net/api/v2',   // 公共接口url测试
        clientsTemplateURL: '',  // 客户导入模板
        logisticsTemplateURL: '',  // 物流导入模板
    },
    // 菜单
    rootMenuArray: [
        { key: 1, value: '广告管理' },
        { key: 2, value: '广告清单' },
        // { key: 3, value: '视频清单' },
        { key: 4, value: '客户管理' },
        { key: 8, value: '店铺管理' },
        { key: 20, value: '附近店铺' },
        { key: 5, value: '商品管理' },
        { key: 6, value: '商品分类' },
        { key: 7, value: '商品清单' },
        // { key: 8, value: '组合清单' },
        { key: 9, value: '订单管理' },
        // { key: 7, value: '订单清单' },
        // { key: 8, value: '查看订单' },
        { key: 10, value: '促销管理' },
        // { key: 8, value: '促销步骤' },
        // { key: 9, value: '促销标签' },
        // { key: 10, value: '编辑促销标签' },
        { key: 11, value: '系统管理' },
        { key: 12, value: '用户管理' },
        { key: 13, value: '区域管理' },
        { key: 14, value: '修改密码' },
        { key: 15, value: '退出登录' },
        { key: 16, value: '物流管理' },
        // { key: 17, value: '返单券管理' },
        // { key: 18, value: '返单券活动' },
        // { key: 19, value: '返单券明细' }
    ],  // 1：超管
    adminMenuArray: [
        // { key: 4, value: '客户管理' },
        { key: 9, value: '订单管理' },
        { key: 11, value: '系统管理' },
        { key: 14, value: '修改密码' },
        { key: 15, value: '退出登录' },
        { key: 16, value: '物流管理' }],  // 2：区域（大区）管理员   
    receptionMenuArray: [
        { key: 9, value: '订单管理' },
        { key: 11, value: '系统管理' },
        { key: 14, value: '修改密码' },
        { key: 15, value: '退出登录' }],  // 3：订单管理员
    logisticsMenuArray: [
        { key: 16, value: '物流管理' },
        { key: 11, value: '系统管理' },
        { key: 14, value: '修改密码' },
        { key: 15, value: '退出登录' }],  // 4：物流管理员
        
    allowLoginRole: [1, 2, 3, 4],  // 可以登录的角色

    // 可选项
    roleOptions: [
        { key: 1, value: "超级管理员" }, 
        { key: 2, value: "区域管理员" }, 
        { key: 3, value: "订单管理员" },
        { key: 4, value: "物流管理员" }],  // 角色可选项
    // 可选项
    agencyRoleOptions: [
        { key: 1, value: "培训公司" }, 
        { key: 2, value: "销售公司" }, 
        { key: 3, value: "代理1" }, 
        { key: 4, value: "代理2" }],  // 代理2角色可选项
    // 可选项
    statusOptions: [
        { key: 0, value: "启用" }, 
        { key: 1, value: "禁用" },
        { key: 2, value: "待审核" }],  // 状态可选项
    // 可选项
    stepTypeOptions: [
        { key: 4, value: "主选购" }, 
        { key: 1, value: "产品促销" }, 
        { key: 2, value: "其他促销" }, 
        { key: 3, value: "特别促销" }],  // 促销类型可选项
    // 可选项
    promotionTypeOptions: [
        // { key: 0, value: "VIP" }, 
        { key: 1, value: "普通促销" }],  // 促销类型可选项
    // 可选项
    useVoucherOptions: [
        { key: 1, value: "可用" },
        { key: 0, value: "不可用" }],      // 是否可用返单券 0=不可用 1=可用

    // 可选项
    orderTypeOptions: [
        { key: '1', value: "活动订单" }, 
        { key: '2', value: "促销订单" }],  // 订单类型可选项
    // 可选项
    orderStatusOptions: [
        { key: '0', value: "待支付" }, 
        { key: '1', value: "已付款" }, 
        { key: '2', value: "已发货" }, 
        { key: '3', value: "部分发货" },
        { key: '-3', value: "同步失败" }],  // 订单状态可选项

    // 可选项
    inventoryStatusOptions: [
        { key: '0', value: "启用" }, 
        { key: '1', value: "禁用" }],  // 商品状态可选项
    // 可选项
    isreportedOptions: [
        { key: '0', value: "已报备" }, 
        { key: '1', value: "未报备" }],  // 是否报备可选项

    // 可选项
    fuzzyFieldOptions: [
        { key: 'consignee', value: "收货人" }, 
        { key: 'ordercaretername', value: "下单人" }, 
        { key: 'pagencyname', value: "销售公司" }, 
        { key: 'cagencyname', value: "代理1" }, 
        { key: 'tagencyname', value: "代理2" }],  // 模糊字段可选项

    // 登录对象字段对应，如果字段有修改，只需修改后面值部分即可
    loginUserFileds: {
        "uid": "id",                 // 用户ID
        "username": "username",      // 用户账号
        "role": "role",              // 用户角色
        "areaid": "areaid",          // 区域ID
        //"areaName": "areaName",      // 区域名
        "creattime": "creattime",    // 创建时间
        "token": "token",            // 登录令牌
        "pwdOriginal": "pwdOriginal",// 原始密码标志
        "status": "status",          // 状态
        "wxaccount": "wxaccount",    // 微信手机号
    },

    // 返单券
    // 可选项
    voucherStatusOptions: [
        { key: 'on', value: "发布" }, 
        { key: 'off', value: "下架" }],  // 是否发布可选项

    // 可选项
    usedOptions: [
        { key: 0, value: "未消费" }, 
        { key: 1, value: "已消费" }],  // 是否已消费

    // 可选项
    voucherFuzzyFieldOptions: [
        { key: 'id', value: "返单券ID" }, 
        { key: 'ordercaretername', value: "客户姓名" }, 
        { key: 'franchisee', value: "加盟商姓名" }],  // 返单券模糊字段可选项

    // 定义商品列表是否为套盒的商品code
    spCodeOptions: ["C-KSYLT5"],
}