<template>
	<section class="test-container" ref="usercontainer">
		<!--列表-->
		<template>
			<el-table :data="tableData" :height="containerHeight - 30" style="width: 100%" id="out-table">
				<el-table-column :key="index" v-for="(item, index) in tableHead" :label="item.label" :property="item.property" align="center">
					<template slot-scope="scope">
						{{scope.row[scope.column.property]}}
					</template>
				</el-table-column>
			</el-table>
		</template>
		<el-button @click="exportExcel">导出</el-button>
		<!--分页-->
		<pagination :pageSize="pageSize" :pageNum="pageNum" :totalNum="totalNum" 
			@handlePageSizeChange="handlePageSizeChange" @handlePageCurrentChange="handlePageCurrentChange">
		</pagination>
	</section>
</template>
<script>
	import util from './common/js/util'
	import emEnum from './common/js/emEnum'
	// 分页组件
	import Pagination from './views/Pagination'

	export default {
		components: {
			Pagination
		},
		data() {
			return {
				courseSchedule: [
					{
						"days": "20190418",
						"data": [
							{
								"courseId": 1,
								"courseScheduleId": 1,
								"camId": 1,
								"courseName": "数学",
								"campusName": "普陀校区",
								"teacherName": "Hakim",
								"starTime": "2019-04-18 10:00:00",
								"endTime": "2019-04-18 11:00:00",
								"days": "20190418"
							}
						]
					},
					{
						"days": "20190422",
						"data": [
							{
								"courseId": 1,
								"courseScheduleId": 2,
								"camId": 1,
								"courseName": "数学",
								"campusName": "普陀校区",
								"teacherName": "Hakim",
								"starTime": "2019-04-22 19:49:29",
								"endTime": "2019-04-22 19:49:34",
								"days": "20190422"
							}
						]
					}
				],  // 课程集合
				// 表头
                tableHead: emEnum.courseScheduleHead,
				// 表数据
                tableData: emEnum.courseSchedule60min,
				// // 表头
                // tableHead:[
				// 	{
				// 		label:'时间',
				// 		property:'time'
				// 	},{
				// 		label:'周一（6.1）',
				// 		property:'col1'
				// 	},{
				// 		label:'周二（6.2）',
				// 		property:'col2'
				// 	},{
				// 		label:'周三（6.3）',
				// 		property:'col3'
				// 	},{
				// 		label:'周四（6.4）',
				// 		property:'col4'
				// 	},{
				// 		label:'周五（6.5）',
				// 		property:'col5'
				// 	}
				// ],
				// // 数据值
                // tableData: [{
                //     time: '07:00~08:45',
                //     col1: '数学/402室',
                //     col2: '',
                //     col3: '',
                //     col4: '',
                //     col5: '',
                // }, {
                //     time: '09:00~10:45',
                //     col1: '',
                //     col2: '语文/302室',
                //     col3: '',
                //     col4: '',
                //     col5: '',
                // }, {
                //     time: '13:00~14:45',
                //     col1: '',
                //     col2: '',
                //     col3: '',
                //     col4: '',
                //     col5: '数学/502室',
                // }, {
                //     time: '15:00~16:45',
                //     col1: '数学/402室',
                //     col2: '',
                //     col3: '',
                //     col4: '',
                //     col5: '',
                // }],
				containerHeight: 0,
				// 分页
				pageSize: 100,
				pageNum: 1,
        		totalNum: 0
			}
		},
		methods: {
			// 导出Execl
			exportExcel: function() {
				this.$router.push({ path: '/404'});
				// let dat = util.formatDate.yyyyMMdd2Date("20190615");
				// console.log(dat);
				// let week = util.formatDate.date2Week("周", dat);
				// console.log(week);

				// let res = util.formatDate.yyyyMMdd2Caption("周", "20190615");
				// console.log(res);

				// 前一天值
				// let preDate = null;
				// if(!!this.courseSchedule && this.courseSchedule.length > 0) {
				// 	preDate = util.formatDate.yyyyMMdd2Date(this.courseSchedule[0].days);  // 获取数据中的第一天
				// } else {
				// 	preDate = util.formatDate.format(new Date(), "yyyy-MM-dd");  // 如果数据不存在则取当天
				// }
				// // 设置表头标题
				// for(let i = 1; i < this.tableHead.length; i++) {
				// 	if(!!this.tableHead[i]) {
				// 		// 标题
				// 		let caption = util.formatDate.date2Caption("周", preDate);
				// 		this.tableHead[i].label = caption;
				// 		let date = new Date(preDate);
				// 		preDate = util.formatDate.format(new Date(date.setDate(date.getDate() + 1)), "yyyy-MM-dd");
				// 	}
				// }
				// // 设置排课值
				// this.courseSchedule.forEach(element => {
				// 	// 标题
				// 	let caption = util.formatDate.yyyyMMdd2Caption("周", element.days);
				// 	let head = this.tableHead.find(item=>{
				// 		return item.label === caption;
				// 	});
				// 	if(!!head) {
				// 		// 获取对应的列
				// 		let prop = head.property;
				// 		// 循环数据给排课赋值
				// 		let data = element.data;
				// 		data.forEach(d => {
				// 			// 给对应列赋值
				// 			this.tableData.forEach(item => {
				// 				// 判断开始时间是否在时间段内
				// 				let isBlong = util.formatDate.dateIsBlongRange(d.starTime, item.time);
				// 				// 如果在时间范围内，则写值
				// 				if(!!isBlong && isBlong) {
				// 					item[prop] = d.courseName;
				// 					return false;
				// 				}
				// 			});
				// 		});
				// 	}
				// });


				// let outTable = document.querySelector("#out-table");
				// if(!!outTable) {
				// 	// 获取文件名
				// 	let fileName = '张三_2019-06-01~2019-06-05_课程表';
				// 	// 导出Execl
				// 	util.exportExcel(fileName, outTable);
				// } else {
				// 	console.log("qqqwww");
				// 	console.log(outTable);
				// }
			},
			// 分页事件
			handlePageSizeChange(val) {
				this.pageSize = val;
			},
			handlePageCurrentChange(val) {
				this.pageNum = val;
			}
		},
		mounted() {
			this.containerHeight = this.$refs.usercontainer.offsetHeight;
			// 绑定窗口尺寸改变事件改变数据表的高度
			let _this = this
			window.onresize = () => {
				return (() => {
					// 为了避免频繁触发resize函数导致页面卡顿，使用定时器
					if (!this.timer && !!_this.$refs.usercontainer) {
						this.timer = true
						_this.containerHeight = _this.$refs.usercontainer.offsetHeight;
						let that = this
						setTimeout(function () {
							that.timer = false
						}, 100);
					}
				})();
			}
		}
	};
</script>

<style lang="scss">
.test-container {
	height: calc(100vh - 102px);
	overflow-y: auto;
	background: #fff;
	.el-table__body-wrapper {
		margin-top: 1px !important;
	}
	.edit-user {
		color: #3ca2ff;
		margin-left: 10px;
		margin-right: 10px;
		cursor: pointer;
	}
}
</style>