// 系统公用相关
import Login from './views/Login.vue';
import NotFound from './views/404.vue';
import Home from './views/Home.vue';
// 广告相关
import Banner from './views/bannerManagement/Banner.vue';
import Video from './views/bannerManagement/Video.vue';
// 客户相关
import Client from './views/clientManagement/Client.vue';
// 商品相关
import InventoryClass from './views/inventoryManagement/InventoryClass.vue';
import Inventory from './views/inventoryManagement/Inventory.vue';
import Combination from './views/inventoryManagement/Combination.vue';
import CombinationInventory from './views/inventoryManagement/CombinationInventory.vue';
// 订单相关
import Order from './views/orderManagement/Order.vue';
import OrderView from './views/orderManagement/OrderView.vue';
// 促销相关
import Promotion from './views/promotionManagement/Promotion.vue';
import PromotionStep from './views/promotionManagement/PromotionStep.vue';
import ProcessLabel from './views/promotionManagement/ProcessLabel.vue';
import ProcessLabelInventory from './views/promotionManagement/ProcessLabelInventory.vue';
// 用户相关
import User from './views/systemManagement/User.vue';
import ChangePwd from './views/systemManagement/ChangePwd.vue';
import Logout from './views/systemManagement/Logout.vue';
// 区域相关
import Area from './views/systemManagement/Area.vue';
// 物流相关
import Logistics from './views/logisticsManagement/Logistics.vue';
// 返单券管理
import VoucherActivity from './views/voucherManagement/VoucherActivity.vue';
import Voucher from './views/voucherManagement/Voucher.vue';
import ShopReport from './views/shopReportManagement/ShopReportManagement.vue';
import MapShopReport from './views/shopReportManagement/MapShopReportManagement.vue';
import SearchMapShop from './views/shopReportManagement/SearchMapShopManagement.vue';

let routes = [
    // 登录
    {
        path: '/login',
        component: Login,
        name: '',
        hidden: true
    },
    // 404页面
    {
        path: '/404',
        component: NotFound,
        name: '',
        hidden: true
    },
    // 主页
    {
        path: '/home',
        component: Home,
        name: '',
        hidden: true
    },
    // 修改密码
    // {
    //     path: '/',
    //     component: Home,
    //     name: '',
    //     hidden: true,
    //     leaf: true,  // 只有一个节点
    //     children: [
    //         { path: '/changePwd', component: ChangePwd, name: '修改密码' }
    //     ]
    // },
    // 广告管理
    {
        path: '/',
        component: Home,
        name: '广告管理',
        iconCls: 'iconfont icon-bell hzp-icon hzp-gg-icon-color',  //图标样式class
        children: [
            { path: '/banner', component: Banner, name: '广告清单' },
            // { path: '/video', component: Video, name: '视频清单' }
        ]
    },
    // 客户管理
    {
        path: '/',
        component: Home,
        name: '',
        iconCls: 'iconfont icon-user hzp-icon hzp-kh-icon-color',  //图标样式class
        leaf: true,  // 只有一个节点
        children: [
            { path: '/client', component: Client, name: '客户管理' }
        ]
    },
    // 店铺管理
    {
        path: '/',
        component: Home,
        name: '店铺管理',
        iconCls: 'iconfont icon-appstore hzp-icon hzp-sp-icon-color',  //图标样式class
        children: [
            { path: '/shopReport', component: ShopReport, name: '店铺管理' },
            { path: '/mapShopReport', component: MapShopReport, name: '店铺管理地图' },
            { path: '/searchMapShop', component: SearchMapShop, name: '附近店铺' }
        ]
    },
    // 商品管理
    {
        path: '/',
        component: Home,
        name: '商品管理',
        iconCls: 'iconfont icon-appstore hzp-icon hzp-sp-icon-color',  //图标样式class
        children: [
            { path: '/inventoryClass', component: InventoryClass, name: '商品分类' },
            { path: '/inventory', component: Inventory, name: '商品清单' },
            { path: '/combination', component: Combination, name: '组合清单' }
        ]
    },
    {
        path: '/',
        component: Home,
        name: '',
        hidden: true,
        leaf: true,  // 只有一个节点
        children: [
            { path: '/combinationInventory', component: CombinationInventory, name: '组合商品' }
        ]
    },
    // 订单管理
    {
        path: '/',
        component: Home,
        name: '',
        iconCls: 'iconfont icon-detail hzp-icon hzp-dd-icon-color',  //图标样式class
        leaf: true,  // 只有一个节点
        children: [
            { 
                path: '/order', 
                component: Order, 
                name: '订单管理',
                meta: {
                    title: "订单管理",
                    keepAlive: true,
                }
            }
        ]
    },
    {
        path: '/',
        component: Home,
        name: '',
        hidden: true,
        leaf: true,  // 只有一个节点
        children: [
            { 
                path: '/orderView', 
                component: OrderView, 
                name: '查看订单',
            }
        ]
    },
    // {
    //     path: '/',
    //     component: Home,
    //     name: '订单管理',
    //     iconCls: 'iconfont icon-detail hzp-icon hzp-dd-icon-color',  //图标样式class
    //     children: [
    //         { path: '/order', component: Order, name: '订单清单' },
    //         { path: '/orderView', component: OrderView, name: '查看订单' }
    //     ]
    // },
    // 促销管理
    {
        path: '/',
        component: Home,
        name: '',
        iconCls: 'iconfont icon-insertrowabove hzp-icon hzp-cx-icon-color',  //图标样式class
        leaf: true,  // 只有一个节点
        children: [
            { path: '/promotion', component: Promotion, name: '促销管理' }
        ]
    },
    {
        path: '/',
        component: Home,
        name: '',
        hidden: true,
        leaf: true,  // 只有一个节点
        children: [
            { path: '/promotionStep', component: PromotionStep, name: '促销步骤' }
        ]
    },
    {
        path: '/',
        component: Home,
        name: '',
        hidden: true,
        leaf: true,  // 只有一个节点
        children: [
            { path: '/processLabel', component: ProcessLabel, name: '促销标签' }
        ]
    },
    {
        path: '/',
        component: Home,
        name: '',
        hidden: true,
        leaf: true,  // 只有一个节点
        children: [
            { path: '/processLabelInventory', component: ProcessLabelInventory, name: '编辑促销标签' }
        ]
    },
    // {
    //     path: '/',
    //     component: Home,
    //     name: '促销管理',
    //     iconCls: 'iconfont icon-insertrowabove hzp-icon hzp-cx-icon-color',  //图标样式class
    //     children: [
    //         { path: '/promotionStep', component: PromotionStep, name: '促销步骤' },
    //         { path: '/processLabel', component: ProcessLabel, name: '促销标签' },
    //         { path: '/processLabelInventory', component: ProcessLabelInventory, name: '编辑促销标签' }
    //     ]
    // },
    // 系统管理
    {
        path: '/',
        component: Home,
        name: '系统管理',
        iconCls: 'iconfont icon-setting hzp-icon hzp-xt-icon-color',  //图标样式class
        children: [
            { path: '/user', component: User, name: '用户管理' },
            { path: '/area', component: Area, name: '区域管理' },
            { path: '/changePwd', component: ChangePwd, name: '修改密码' },
            { path: '/logout', component: Logout, name: '退出登录' }
        ]
    },
    // 物流管理
    {
        path: '/',
        component: Home,
        name: '',
        iconCls: 'el-icon-aim hzp-icon hzp-wl-icon-color',  //图标样式class
        leaf: true,  // 只有一个节点
        children: [
            { path: '/logistics', component: Logistics, name: '物流管理' }
        ]
    },
    // // 返单券管理
    // {
    //     path: '/',
    //     component: Home,
    //     name: '返单券管理',
    //     iconCls: 'el-icon-present hzp-icon hzp-fdq-icon-color',  //图标样式class
    //     children: [
    //         { path: '/VoucherActivity', component: VoucherActivity, name: '返单券活动' },
    //         { path: '/Voucher', component: Voucher, name: '返单券明细' }
    //     ]
    // },
    // 未找到则转到Home
    {
        path: '*',
        component: Home,
        hidden: true,
        redirect: { path: '/home' }
    }
];

export default routes;