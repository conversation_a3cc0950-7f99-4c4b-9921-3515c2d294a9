import Vue from 'vue'
import Vuex from 'vuex'
import * as actions from './actions'
import * as getters from './getters'

Vue.use(Vuex)

// 应用初始状态
const state = {
    count: 10,
    loginUser: null,  // 登录用户信息
    httpRequestList: [],  // 请求列表
    editObject: null,      // 编辑对象 
    editedToPath: '/home',  // 编辑后转到的页面
    UnShowColObj: [],       // 不显示列集合 {name: '', cols: []]}

    addCourseSchedule: [],  // 新增的课程列表
    delCourseSchedule: [],  // 删除的课程列表
}

// 定义所需的 mutations
const mutations = {
    INCREMENT(state) {
        state.count++
    },
    DECREMENT(state) {
        state.count--
    },
    // // 编辑赋值
    // SetEditValue(state, obj, path) {
    //     state.editObject = obj;
    //     state.editedToPath = path;
    // },
    // // 清空编辑赋值
    // ClearEditValue(state) {
    //     state.editObject = null;
    //     state.editedToPath = '/home';
    // }
}

// 创建 store 实例
export default new Vuex.Store({
    actions,
    getters,
    state,
    mutations
})