//test
export const increment = ({commit}) => {
    commit('INCREMENT')
}
export const decrement = ({commit}) => {
    commit('DECREMENT')
}

// 编辑赋值
// export const SetEditValue = ({commit}, obj, path) => {
//     console.log('ssssaaaaa');
//     console.log(obj);
//     console.log(path);
//     commit('SetEditValue', obj, path);
// }
// // 清空编辑赋值
// export const ClearEditValue = ({commit}) => {
//     commit('ClearEditValue');
// }