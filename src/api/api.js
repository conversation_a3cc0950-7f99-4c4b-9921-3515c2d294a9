import http from './http'
import http2 from './http2'

/**
 * 这里统一处理项目中所有的 api 接口
 */
// api列表（传入你要不要展示loading的参数）
// 登录
export const requestLogin = (params, _this) => http.post('/system/login', params, _this);
export const requestLoginOut = (params, _this) => http.post('/system/loginOut', params, _this);

// 用户管理 -- 获取用户列表
export const getUserList = (params, _this) => http.post('/system/userList', params, _this);
// 用户管理 -- 新增用户
export const addUser = (params, _this) => http.post('/system/addUser', params, _this);
// 用户管理 -- 编辑用户
export const editUser = (params, _this) => http.post('/system/editUser', params, _this);
// 用户管理 -- 禁启用用户
export const enableUser = (params, _this) => http.post('/system/enableUser', params, _this);
// 用户管理 -- 删除用户
export const deleteUser = (params, _this) => http.post('/system/deleteUser', params, _this);
// 用户管理 -- 修改密码
export const changePassword = (params, _this) => http.post('/system/changePassword', params, _this);
// 用户管理 -- 重置密码
export const resetUserPass = (params, _this) => http.post('/system/resetUserPass', params, _this);

// 获取所有区域的配置
export const getAreaConfig = (params, _this) => http.post('/system/areaConfig', params, _this);
// 区域管理 -- 新增区域
export const addArea = (params, _this) => http.post('/system/addArea', params, _this);
// 区域管理 -- 编辑区域
export const editArea = (params, _this) => http.post('/system/editArea', params, _this);
// 区域管理 -- 禁启用区域
export const enableArea = (params, _this) => http.post('/system/enableArea', params, _this);
// 区域管理 -- 删除区域
export const deleteArea = (params, _this) => http.post('/system/deleteArea', params, _this);

// 广告管理 -- 获取广告列表
export const getBannerList = (params, _this) => http.post('/system/bannerList', params, _this);
// 广告管理 -- 新增广告
export const addBanner = (params, _this) => http.post('/system/addBanner', params, _this);  // ???
// 广告管理 -- 编辑广告
export const editBanner = (params, _this) => http.post('/system/editBanner', params, _this);
// 广告管理 -- 禁启用广告
export const enableBanner = (params, _this) => http.post('/system/enableBanner', params, _this);
// 广告管理 -- 删除广告
export const deleteBanner = (params, _this) => http.post('/system/deleteBanner', params, _this);
// 广告管理 -- 广告排序
export const sortBanner = (params, _this) => http.post('/system/sortBanner', params, _this);

// 视频管理 -- 获取视频列表
export const getVideoList = (params, _this) => http.post('/video/videoList', params, _this);
// 视频管理 -- 编辑视频
export const editVideo = (params, _this) => http.post('/video/addVideo', params, _this);
// 视频管理 -- 禁启用视频数据
export const enableVideo = (params, _this) => http.post('/video/enableVideo', params, _this);
// 视频管理 -- 删除视频数据
export const deleteVideo = (params, _this) => http.post('/video/deleteVideo', params, _this);

// 客户管理 -- 模糊查询
export const clientVague = (params, _this) => http.post('/client/clientVague', params, _this);
// 客户管理 -- 获取客户列表
export const getClientList = (params, _this) => http.post('/client/clientList', params, _this);
// 客户管理 -- 新增客户
export const addClient = (params, _this) => http.post('/client/addClient', params, _this);
// 客户管理 -- 编辑客户
export const editClient = (params, _this) => http.post('/client/editClient', params, _this);
// 客户管理 -- 禁启用客户
export const enableClient = (params, _this) => http.post('/client/enableClient', params, _this);
// 客户管理 -- 删除客户
export const deleteClient = (params, _this) => http.post('/client/deleteClient', params, _this);
// 客户管理 -- 批量导入客户
export const batchAddClient = (params, _this) => http.post('/client/batchAddClient', params, _this);
// 客户管理 -- 查询培训公司及所有区域
export const areabyroleList = (params, _this) => http.post('/system/areabyroleList', params, _this);

// 商品管理 -- 库存类型清单
export const getInventoryClassList = (params, _this) => http.post('/inventory/inventoryclassList', params, _this);
// 商品管理 -- 库存清单搜索
export const inventoryVague = (params, _this) => http.post('/inventory/inventoryVague', params, _this);
// 商品管理 -- 库存清单
export const getInventoryList = (params, _this) => http.post('/inventory/inventoryList', params, _this);
// 商品管理 -- 新增库存图片
export const addInventoryimage = (params, _this) => http.post('/inventory/addInventoryimage', params, _this);
// 商品管理 -- 编辑库存图片
export const editInventoryimage = (params, _this) => http.post('/inventory/editInventoryimage', params, _this);
// 商品管理 -- 禁启用库存图片
export const enableInventoryimage = (params, _this) => http.post('/inventory/enableInventoryimage', params, _this);
// 商品管理 -- 禁启用库存
export const enableInventory = (params, _this) => http.post('/inventory/enableInventory', params, _this);
// 商品管理 -- 同步商品
export const updateInventory = (params, _this) => http.post('/inventory/updateInventory', params, _this);

// 商品管理（组合清单） -- 查询商品组合
export const combinationList = (params, _this) => http.post('/inventory/combinationList', params, _this);
// 商品管理（组合清单） -- 修改/新增商品组合
export const addCombination = (params, _this) => http.post('/inventory/addCombination', params, _this);
// 商品管理（组合清单） -- 禁启用商品组合
export const enableCombination = (params, _this) => http.post('/inventory/enableCombination', params, _this);

// 商品管理（编辑组合） -- 查询组合商品
export const combinationinventoryList = (params, _this) => http.post('/inventory/combinationinventoryList', params, _this);
// 商品管理（编辑组合） -- 修改/新增商品组合商品
export const addCombinationinventory = (params, _this) => http.post('/inventory/addCombinationinventory', params, _this);
// 商品管理（编辑组合） -- 禁启用商品组合商品
export const enablecombinationinventory = (params, _this) => http.post('/inventory/enablecombinationinventory', params, _this);

// 促销管理（促销活动） -- 查询促销活动（不分页）
export const getPromotionList = (params, _this) => http.post('/promotion/promotionList', params, _this);
// 促销管理（促销活动） -- 添加促销活动
export const addPromotion = (params, _this) => http.post('/promotion/addPromotion', params, _this);
// 促销管理（促销活动） -- 修改促销活动
export const editPromotion = (params, _this) => http.post('/promotion/editPromotion', params, _this);
// 促销管理（促销活动） -- 禁启用促销活动
export const enablePromotion = (params, _this) => http.post('/promotion/enablePromotion', params, _this);
// 促销管理（促销活动） -- 促销活动排序
export const sortPromotion = (params, _this) => http.post('/promotion/sortPromotion', params, _this);
// 促销管理（促销活动） -- 复制促销活动
export const promotionCopy = (params, _this) => http.post('/promotion/promotionCopy', params, _this);

// 促销管理（促销步骤） -- 查询促销步骤(不分页)
export const getPromotionstepList = (params, _this) => http.post('/promotion/promotionstepList', params, _this);
// 促销管理（促销步骤） -- 新增促销步骤
export const addPromotionstep = (params, _this) => http.post('/promotion/addPromotionstep', params, _this);
// 促销管理（促销步骤） -- 更新促销步骤
export const editPromotionstep = (params, _this) => http.post('/promotion/editPromotionstep', params, _this);
// 促销管理（促销步骤） -- 启用禁用促销步骤
export const enablePromotionstep = (params, _this) => http.post('/promotion/enablePromotionstep', params, _this);
// 促销管理（促销步骤） -- 促销步骤排序
export const sortPromotionstep = (params, _this) => http.post('/promotion/sortPromotionstep', params, _this);

// 促销管理（促销标签） -- 查询促销标签
export const getProcesslabelList = (params, _this) => http.post('/promotion/processlabelList', params, _this);
// 促销管理（促销标签） -- 新增促销标签
export const addProcesslabel = (params, _this) => http.post('/promotion/addProcesslabel', params, _this);
// 促销管理（促销标签） -- 更新促销标签
export const editProcesslabel = (params, _this) => http.post('/promotion/editProcesslabel', params, _this);
// 促销管理（促销标签） -- 启用禁用促销标签
export const enableProcesslabel = (params, _this) => http.post('/promotion/enableProcesslabel', params, _this);
// 促销管理（促销标签） -- 促销标签排序
export const sortProcesslabel = (params, _this) => http.post('/promotion/sortProcesslabel', params, _this);

// 促销管理（促销标签商品） -- 查询促销标签对应商品
export const getProcesslabelInventoryList = (params, _this) => http.post('/promotion/processlabelInventoryList', params, _this);
// 促销管理（促销标签商品） -- 新增促销标签商品
export const addProcesslabelInventory = (params, _this) => http.post('/promotion/addProcesslabelInventory', params, _this);
// 促销管理（促销标签商品） -- 更新促销标签商品
export const editProcesslabelInventory = (params, _this) => http.post('/promotion/editProcesslabelInventory', params, _this);
// 促销管理（促销标签商品） -- 启用禁用促销标签商品
export const enableProcesslabelInventory = (params, _this) => http.post('/promotion/enableProcesslabelInventory', params, _this);

// 订单管理（订单清单） -- 查询订单清单
//export const getOrderList = (params, _this) => http.post('/order/orderList', params, _this);
export const getOrderList = (params, _this) => http2.post('/admin/order_list', params, _this);
// 订单管理（订单清单） -- 修改订单
export const editOrder = (params, _this) => http.post('/order/editOrder', params, _this);
// // 订单管理（订单清单） -- 修改订单支付状态
// export const editPaystatus = (params, _this) => http.post('/order/editPaystatus', params, _this);
// // 订单管理（订单清单） -- 修改订单支付状态
// export const editPaystatus = (params, _this) => http.post('/order/editPaystatus_new', params, _this);
// 订单管理（订单清单） -- 修改订单支付状态
export const editPaystatus = (params, _this) => http.post('/order/editPaystatus_new_2', params, _this);
// 支付订单，同步wms
export const confirmPayOrder = (params, _this) => http2.post('/admin/confirm_pay_order', params, _this);
// 撤回订单，取消wms订单
export const cancelPayOrder = (params, _this) => http2.post('/admin/cancel_pay_order', params, _this);
// 订单管理（订单清单） -- 批量更新运单号
//export const editOrderWaybillcode = (params, _this) => http.post('/order/editOrderWaybillcode', params, _this);
// 订单管理（订单清单） -- 禁启用订单
export const enableOrder = (params, _this) => http.post('/order/enableOrder', params, _this);

// 订单管理（查看订单） -- 查询运单信息
export const findwaybill = (params, _this) => http.post('/order/waybill', params, _this);
// 订单管理（查看订单） -- 查询订单详情
export const orderDetail = (params, _this) => http.post('/order/orderDetail', params, _this);
// 订单管理（查看订单） -- 查询订单详情（分页）
export const orderDetailByPage = (params, _this) => http.post('/order/orderDetailByPage', params, _this);

// 删除 -- 删除促销活动
export const deletePromotion = (params, _this) => http.post('/promotion/deletePromotion', params, _this);
// 删除 -- 删除促销步骤
export const deletePromotionstep = (params, _this) => http.post('/promotion/deletePromotionstep', params, _this);
// 删除 -- 删除促销标签
export const deleteProcesslabel = (params, _this) => http.post('/promotion/deleteProcesslabel', params, _this);
// 删除 -- 删除促销标签商品
export const deleteProcesslabelinventory = (params, _this) => http.post('/promotion/deleteProcesslabelinventory', params, _this);

// 物流管理 -- 物流信息查询
export const getLogisticsList = (params, _this) => http.post('/logistics/logisticsList', params, _this);
// 物流管理 -- 新增/更新物流信息
export const editLogistics = (params, _this) => http.post('/logistics/addLogistics', params, _this);
// 物流管理 -- 禁启用物流信息
export const enableLogistics = (params, _this) => http.post('/logistics/enableLogistics', params, _this);
// 物流管理 -- 批量新增物流信息
export const editOrderWaybillcode = (params, _this) => http.post('/logistics/editOrderWaybillcode', params, _this);
// 物流管理 -- 批量删除物流信息
export const batchDelLogistics = (params, _this) => http.post('/logistics/batchDelLogistics', params, _this);

// 物流管理 -- 获取U8id的状态
export const u8orderidInter = (params, _this) => http.post('/order/u8orderid', params, _this);

// 物流管理 -- 获取顺丰的物流状态
export const queryWaybill = (params, _this) => http2.post('/admin/query_waybill', params, _this);

// 返单券活动 -- 获取活动列表
export const getVoucherActivityList = (params, _this) => http.post('/voucher/activity/list', params, _this);
// 返单券活动 -- 添加活动
export const addVoucherActivity = (params, _this) => http.post('/voucher/activity/add', params, _this);
// 返单券活动 -- 修改活动
export const updateVoucherActivity = (params, _this) => http.post('/voucher/activity/update', params, _this);
// 返单券活动 -- 删除活动
export const deleteVoucherActivity = (params, _this) => http.post('/voucher/activity/delete', params, _this);

// 返单券明细 -- 获取优惠券列表
export const getVoucherList = (params, _this) => http.post('/voucher/list', params, _this);
// 返单券明细 -- 新增优惠券
export const addVoucher = (params, _this) => http.post('/voucher/add', params, _this);
// 返单券明细 -- 修改优惠券
export const updateVoucher = (params, _this) => http.post('/voucher/update', params, _this);
// 返单券明细 -- 删除优惠券
export const deleteVoucher = (params, _this) => http.post('/voucher/delete', params, _this);
// 返单券明细 -- 根据促销id获取返单券金额
export const getPromotionAmount = (params, _this) => http.post('/promotion/get', params, _this);

//店铺管理
// 查询所有的客户店铺报备
export const getAdminShopReportList = (params, _this) => http2.post('/admin/shop_report_list', params, _this);
// 审核店铺报备
export const updateAuditShopReport = (params, _this) => http2.post('/admin/audit_shop_report', params, _this);
// 删除店铺报备
export const deleteAuditShopReport = (params, _this) => http2.post('/admin/delete_shop_report', params, _this);
//查看店铺报备信息
export const queryOrderShopReport = (params, _this) => http2.post('/admin/query_order_shop_report', params, _this);
//更新店铺报备位置信息
export const updateShopReportAddress = (params, _this) => http2.post('/admin/force_update_shop_address', params, _this);
//更新店铺报备
export const adminUpdateShopReport = (params, _this) => http2.post('/admin/admin_update_shop_report', params, _this);


//新增商品分类信息
export const addInventoryClass = (params, _this) => http2.post('/admin/add_inventory_clas', params, _this);
//更新商品分类信息
export const updateInventoryClass = (params, _this) => http2.post('/admin/update_inventory_clas', params, _this);

// 新增商品
export const addInventory = (params, _this) => http2.post('/admin/add_inventory', params, _this);
// 编辑商品
export const updateInventoryInfo = (params, _this) => http2.post('/admin/update_inventory', params, _this);
// 同步商品到云仓
export const syncInventory = (params, _this) => http2.post('/admin/sync_inventory', params, _this);
// 添加商品库存
export const addInventoryPlanQty = (params, _this) => http2.post('/admin/add_inventory_plan_qty', params, _this);
// 同步商品库存
export const queryInventoryPlanQty = (params, _this) => http2.post('/admin/query_inventory_plan_qty', params, _this);
// 下载用户导入模版
export const getClientImportTemplate = (params, _this) => http2.post('/admin/get_client_import_template', params, _this);
// 删除 -- 删除促销活动
export const deletePromotion_new = (params, _this) => http2.post('/admin/delete_promotion', params, _this);
// 删除 -- 删除促销步骤
export const deletePromotionstep_new = (params, _this) => http2.post('/admin/delete_promotion_step', params, _this);
// 删除 -- 删除促销标签
export const deleteProcesslabel_new = (params, _this) => http2.post('/admin/delete_promotion_label', params, _this);
// 删除 -- 删除促销标签商品
export const deleteProcesslabelinventory_new = (params, _this) => http2.post('/admin/delete_promotion_inventory', params, _this);
//获取订单发货状态
export const queryOrderInventoryDelivery = (params, _this) => http2.post('/admin/query_order_inventory_delivery', params, _this);
//订单详情添加发货按钮调用接口：非预售商品
export const orderPartSyncWms = (params, _this) => http2.post('/admin/order_part_sync_wms', params, _this);
// //订单详情添加发货按钮调用接口：预售商品
// export const orderYsSyncWms = (params, _this) => http2.post('/admin/order_ys_sync_wms', params, _this);
//套盒商品同步增加库存
export const addSetboxPlanQty = (params, _this) => http2.post('/admin/add_setbox_plan_qty', params, _this);
//查询某个位置500米内都有哪些店铺
export const selectDistanceReportedShop = (params, _this) => http2.post('/admin/select_distance_reported_shop', params, _this);
//
export const queryInvSalesVolume = (params, _this) => http2.post('/admin/inv_sales_volume', params, _this);
//
export const queryQtyDetail = (params, _this) => http2.post('/admin/qty_detail', params, _this);
