<!--
 * @Author: <EMAIL> <EMAIL>
 * @Date: 2024-01-21 23:39:58
 * @LastEditors: <EMAIL> <EMAIL>
 * @LastEditTime: 2024-10-25 22:17:35
 * @FilePath: /shhzp-admin/src/App.vue
 * @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
-->
<template>
	<div id="app">
		<!-- <transition name="fade" mode="out-in">
			<router-view></router-view>
		</transition> -->

		<keep-alive v-if="isLoggedIn">
			<router-view v-if="$route.meta.keepAlive" />
		</keep-alive>
		<router-view v-if="!$route.meta.keepAlive" />
	</div>
</template>

<script>
import util from './common/js/util';
import emEnum from './common/js/emEnum';
export default {
	name: 'app',
	components: {
	},
	data() {
		return {
			isLoggedIn: false
		}
	},

	watch: {
		$route(to, from) {
			let userName = util.userData.getLoginUserFieldValue(emEnum.loginUserFileds.username, this);
			if (userName) {
				this.isLoggedIn = true;
			} else {
				this.isLoggedIn = false;
			}
		}
	},
}

</script>

<style lang="scss">
body {
	margin: 0px;
	padding: 0px;
	font-family: Helvetica Neue, Helvetica, PingFang SC, Hiragino Sans GB, Microsoft YaHei, SimSun, sans-serif;
	font-size: 14px;
	-webkit-font-smoothing: antialiased;
}

#app {
	position: absolute;
	top: 0px;
	bottom: 0px;
	width: 100%;
	background: url(assets/images/bg.png);
	background-repeat: no-repeat;
	background-size: cover;
	-webkit-background-size: cover;
	-o-background-size: cover;
	background-position: center 0;
}

.el-submenu [class^=fa] {
	vertical-align: baseline;
	margin-right: 10px;
}

.el-menu-item [class^=fa] {
	vertical-align: baseline;
	margin-right: 10px;
}

.toolbar {
	background: #f2f2f2;
	padding: 10px;
	//border:1px solid #dfe6ec;
	margin: 10px 0px;
	.el-form-item {
		margin-bottom: 10px;
	}
}

.fade-enter-active,
.fade-leave-active {
	transition: all .2s ease;
}

.fade-enter,
.fade-leave-active {
	opacity: 0;
}
</style>