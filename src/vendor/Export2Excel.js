/* eslint-disable */
import { saveAs } from 'file-saver';
import XLSX from 'xlsx';
function generateArray(table) {
    var out = [];
    var rows = table.querySelectorAll('tr');
    var ranges = [];
    for (var R = 0; R < rows.length; ++R) {
        var outRow = [];
        var row = rows[R];
        var columns = row.querySelectorAll('td');
        for (var C = 0; C < columns.length; ++C) {
            var cell = columns[C];
            var colspan = cell.getAttribute('colspan');
            var rowspan = cell.getAttribute('rowspan');
            var cellValue = cell.innerText;
            if (cellValue !== "" && cellValue == +cellValue) cellValue = +cellValue;

            //Skip ranges
            ranges.forEach(function (range) {
                if (R >= range.s.r && R <= range.e.r && outRow.length >= range.s.c && outRow.length <= range.e.c) {
                    for (var i = 0; i <= range.e.c - range.s.c; ++i) outRow.push(null);
                }
            });

            //Handle Row Span
            if (rowspan || colspan) {
                rowspan = rowspan || 1;
                colspan = colspan || 1;
                ranges.push({s: {r: R, c: outRow.length}, e: {r: R + rowspan - 1, c: outRow.length + colspan - 1}});
            }
            ;

            //Handle Value
            outRow.push(cellValue !== "" ? cellValue : null);

            //Handle Colspan
            if (colspan) for (var k = 0; k < colspan - 1; ++k) outRow.push(null);
        }
        out.push(outRow);
    }
    return [out, ranges];
};

function datenum(v, date1904) {
    if (date1904) v += 1462;
    var epoch = Date.parse(v);
    return (epoch - new Date(Date.UTC(1899, 11, 30))) / (24 * 60 * 60 * 1000);
}

function sheet_from_array_of_arrays(data, opts) {
    var ws = {};
    var range = {s: {c: 10000000, r: 10000000}, e: {c: 0, r: 0}};
    for (var R = 0; R != data.length; ++R) {
        for (var C = 0; C != data[R].length; ++C) {
            if (range.s.r > R) range.s.r = R;
            if (range.s.c > C) range.s.c = C;
            if (range.e.r < R) range.e.r = R;
            if (range.e.c < C) range.e.c = C;
            var cell = {v: data[R][C]};
            if (cell.v == null) continue;
            var cell_ref = XLSX.utils.encode_cell({c: C, r: R});

            if (typeof cell.v === 'number') cell.t = 'n';
            else if (typeof cell.v === 'boolean') cell.t = 'b';
            else if (cell.v instanceof Date) {
                cell.t = 'n';
                cell.z = XLSX.SSF._table[14];
                cell.v = datenum(cell.v);
            }
            else cell.t = 's';

            ws[cell_ref] = cell;
        }
    }
    if (range.s.c < 10000000) ws['!ref'] = XLSX.utils.encode_range(range);
    return ws;
}

function Workbook() {
    if (!(this instanceof Workbook)) return new Workbook();
    this.SheetNames = [];
    this.Sheets = {};
}

function s2ab(s) {
    var buf = new ArrayBuffer(s.length);
    var view = new Uint8Array(buf);
    for (var i = 0; i != s.length; ++i) view[i] = s.charCodeAt(i) & 0xFF;
    return buf;
}

export function export_table_to_excel(id) {
    var theTable = document.getElementById(id);
    var oo = generateArray(theTable);
    var ranges = oo[1];

    /* original data */
    var data = oo[0];
    var ws_name = "SheetJS";

    var wb = new Workbook(), ws = sheet_from_array_of_arrays(data);

    /* add ranges to worksheet */
    // ws['!cols'] = ['apple', 'banan'];
    ws['!merges'] = ranges;

    /* add worksheet to workbook */
    wb.SheetNames.push(ws_name);
    wb.Sheets[ws_name] = ws;

    var wbout = XLSX.write(wb, {bookType: 'xlsx', bookSST: false, type: 'binary'});

    saveAs(new Blob([s2ab(wbout)], {type: "application/octet-stream"}), "test.xlsx")
}

function formatJson(jsonData) {
    console.log(jsonData)
}

export function export_json_to_excel(th, jsonData, defaultTitle) {
    /* original data */
    var data = jsonData;
    data.unshift(th);
    var ws_name = "SheetJS";

    var wb = new Workbook(), ws = sheet_from_array_of_arrays(data);
    /* add worksheet to workbook */
    wb.SheetNames.push(ws_name);
    wb.Sheets[ws_name] = ws;

    var wbout = XLSX.write(wb, {bookType: 'xlsx', bookSST: false, type: 'binary'});
    var title = defaultTitle || '列表'
    saveAs(new Blob([s2ab(wbout)], {type: "application/octet-stream"}), title + ".xlsx")
}

export function export_json_to_excel_mult(th, jsonData , sheetName , defaultTitle) {
    /* original data */
    var wb = new Workbook();
    jsonData.forEach((item, index) => {
        /* add worksheet to workbook */
        // sheet页名称
        let ws_name = sheetName[index] || ("SheetJS" + index);
        wb.SheetNames.push(ws_name);
        // 数据
        let data = item;
        data.unshift(th[index]);
        var ws = sheet_from_array_of_arrays(data);
        wb.Sheets[ws_name] = ws;
    });
    
    var wbout = XLSX.write(wb, {bookType: 'xlsx', bookSST: false, type: 'binary'});
    var title = defaultTitle || '列表'
    saveAs(new Blob([s2ab(wbout)], {type: "application/octet-stream"}), title + ".xlsx")
}

function getCharCol(n) {
    let s = '';
    let m = 0;
    while (n > 0) {
        m = n % 26 + 1
        s = String.fromCharCode(m + 64) + s
        n = (n - m) / 26
    }
    return s
}

// 判断是否是数字
function isRealNum(val) {
    // isNaN()函数 把空串 空格 以及NUll 按照0来处理 所以先去除
　　if(val === "" || val ==null) {
        return false;
　　}
    if(!isNaN(val)) {　　　　
    　　// 对于空数组和只有一个数值成员的数组或全是数字组成的字符串，isNaN返回false，例如：'123'、[]、[2]、['123'],isNaN返回false,
        // 所以如果不需要val包含这些特殊情况，则这个判断改写为if(!isNaN(val) && typeof val === 'number' )
　　　  return true; 
　　} else { 
　　　　    return false; 
　　}
}

function s2ab_th(s) {
    if (typeof ArrayBuffer !== 'undefined') {
        var buf = new ArrayBuffer(s.length);
        var view = new Uint8Array(buf);
        for (var i = 0; i != s.length; ++i) view[i] = s.charCodeAt(i) & 0xFF;
        return buf;
    } else {
        var buf = new Array(s.length);
        for (var i = 0; i != s.length; ++i) buf[i] = s.charCodeAt(i) & 0xFF;
        return buf;
    }
}

export function export_json_to_excel_multth(ths, jsonData, defaultTitle) {
    /* original data */
    var data = jsonData;
    ths.forEach(th => {
        data.unshift(th);
    });
    var keyMap = [];  // 获取keys
    for (var k in data) {
        keyMap.push(k);
    }
    var tmpdata = []; // 用来保存转换好的json
    data.map((v, i) => keyMap.map((k, j) => Object.assign({}, {
        v: v[k],
        position: (j > 25 ? getCharCol(j) : String.fromCharCode(65 + j)) + (i + 1)
    }))).reduce((prev, next) => prev.concat(next)).forEach((v, i) => tmpdata[v.position] = {
        v: v.v
    });
    var outputPos = Object.keys(tmpdata);  // 设置区域，比如表格从A1到D10
    // 合并单元格
    tmpdata["!merges"] = [{
        s: { c: 3, r: 0 },  // 开始 D1
        e: { c: 5, r: 0 }   // 结束 F1
    },{
        s: { c: 1, r: 1 },  // 开始 B2
        e: { c: 2, r: 1 }   // 结束 C2
    },{
        s: { c: 4, r: 1 },  // 开始 E2
        e: { c: 6, r: 1 }   // 结束 G2
    },{
        s: { c: 1, r: 2 },  // 开始 B3
        e: { c: 2, r: 2 }   // 结束 C3
    },{
        s: { c: 4, r: 2 },  // 开始 E3
        e: { c: 6, r: 2 }   // 结束 G3
    },{
        s: { c: 5, r: 3 },  // 开始 F4
        e: { c: 6, r: 3 }   // 结束 G4
    },{
        s: { c: 3, r: 4 },  // 开始 D5
        e: { c: 8, r: 4 }   // 结束 I5
    },{
        s: { c: 1, r: 5 },  // 开始 B6
        e: { c: 8, r: 5 }   // 结束 I6
    },{
        s: { c: 1, r: 6 },  // 开始 B7
        e: { c: 8, r: 6 }   // 结束 I7
    },{
        s: { c: 0, r: 7 },  // 开始 A8
        e: { c: 8, r: 7 }   // 结束 I8
    }]; 
    // // 加粗
    // let style0 = {
    //     alignment: {
    //         horizontal:'center',
    //         wrapText: true,
    //         vertical: "center"
    //     },
    //     font: { 
    //         sz: 18, 
    //         bold: true, 
    //         color: {
    //             rgb: "FF0000" 
    //         },
    //         outline:true 
    //     }, 
    //     fill: { 
    //         bgColor: { indexed: 64 } 
    //     } 
    // };
    // // 样式
    // tmpdata["A1"].s = style0;
    // 数据
    var tmpWB = {
        SheetNames: ["SheetJS"], //保存的表标题
        Sheets: {
            "SheetJS": Object.assign({},
            tmpdata, //内容
            {
                '!ref': outputPos[0] + ':' + outputPos[outputPos.length - 1] //设置填充区域
            })
        }
    };
    var tmpDown = new Blob([s2ab_th(XLSX.write(tmpWB, { bookType: 'xlsx', bookSST: false, type: 'binary' }))], {});  // 这里的数据是用来定义导出的格式类型
    var title = defaultTitle || '列表';
    saveAs_th(tmpDown, title + ".xlsx");

    /* original data */
    // var data = jsonData;
    // ths.forEach(th => {
    //     data.unshift(th);
    // });
    // var ws_name = "SheetJS";
    // var wb = new Workbook(), ws = sheet_from_array_of_arrays(data);
    // /* add worksheet to workbook */
    // wb.SheetNames.push(ws_name);
    // wb.Sheets[ws_name] = ws;

    // var wbout = XLSX.write(wb, {bookType: 'xlsx', bookSST: false, type: 'binary'});
    // var title = defaultTitle || '列表'
    // saveAs(new Blob([s2ab(wbout)], {type: "application/octet-stream"}), title + ".xlsx")
}

function saveAs_th(obj, fileName){
    // 解决IE浏览器下无法使用createObjectURL生成的blob链接下载的问题
    if('msSaveOrOpenBlob' in navigator) {
        window.navigator.msSaveOrOpenBlob(obj, fileName);
        return;
    }
    var tmpa = document.createElement("a");
    tmpa.download = fileName || "下载";
    tmpa.href = URL.createObjectURL(obj);
    tmpa.click();
    setTimeout(function () {
        URL.revokeObjectURL(obj);
    }, 100);
}