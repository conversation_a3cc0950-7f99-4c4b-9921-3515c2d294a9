
/* .el-table__body{
    height: 100%;
}
.el-table__body .tbody{
    height: 100%;
} */

.hzp-icon {
    margin-right: 10px;
    font-size: 20px;
}

.hzp-gg-icon-color {
    color: #d52e71 !important;
}

.hzp-kh-icon-color {
    color: #cc6040 !important;
}

.hzp-sp-icon-color {
    color: #cfa833 !important;
}

.hzp-dd-icon-color {
    color: #66aa1b !important;
}

.hzp-cx-icon-color {
    color: #00f4ff !important;
}

.hzp-xt-icon-color {
    color: #4951bc !important;
}

.hzp-wl-icon-color {
    margin-left: -4px !important;
    color: #67c23a !important;
}

.hzp-fdq-icon-color {
    color: #ff0000 !important;
}

.login-input input[class^='el-input'] {
    font-size: 17px;
    padding-left: 36px;
}

/* 全局自定义样式 */
/* 按钮 */
.hzpbtn-primary {
    font-size: 16px;
    background: #3E4499;
    border-color: #3E4499;
}
.hzpbtn:hover {
    background: #575db7;
    border-color: #575db7;
}
.hzpbtn-info {
    font-size: 15px;
}
.hzpbtn-info:hover {
    color: #909399 !important;
    background: #f4f4f5 !important;
    border-color: #d3d4d6 !important;
}

.hzpbtn-close {
    font-size: 16px;
}
/* 编辑 */
.hzpedit {
    color: #424CDA;
    margin-left: 10px;
    margin-right: 10px;
    cursor: pointer;
}
.hzpedit:hover {
    color: #575db7;
}

.common-container {
    width: 100%;
	overflow-y: auto;
	background: #EFF3FC;
    /* -webkit-appearance: none; */
}
/* .common-container::-webkit-scrollbar:vertical { 
	width: 2px;
} */
/* .common-container::-webkit-scrollbar-thumb { 
	border-radius: 8px; 
	border: 2px solid rgba(255,255,255,.4); 
	background-color: rgba(0, 0, 0, .5);
} */

.dialog-container {
    white-space: pre-wrap;
}
.form-data {
    padding: 20px;
}
.form-data-nopadding {
    width: calc(100% - 40px);
	padding: 20px;
}
.dialog-footer {
    text-align: center;
}
.dialog-footer button {
    margin-right: 50px;
    margin-left: 50px;
}

.data-oper {
    min-height: 70px;
}

.data-content {
    width: 100%;
}

.order-edit-row {
	width: 100%; 
	height: 40px; 
	line-height: 40px;
}

.file {
    display: inline-block;
    position: relative;
    width: 110px;
    height: 40px;
    line-height: 40px;
    color: #FFF;
    font-size: 18px;
    text-align: center;
    margin-top: 20px;
    margin-left: 20px;
    border: 1px solid #4D5AFF;
    border-radius: 4px;
    background: #4D5AFF;
    text-indent: 0;
    text-decoration: none;
    overflow: hidden;
}
.importFile {
    margin-top: 0px;
    background: #3E4499; 
    border-color: #3E4499;
}
.file input {
    position: absolute;
    font-size: 100px;
    right: 0;
    top: 0;
    opacity: 0;
}
.file:hover {
    background: #66b1ff;
    border-color: #66b1ff;
    text-decoration: none;
}

.layerPath {
    float: right; 
    width: 600px; 
    margin-top: 10px; 
    margin-right: 2px; 
    font-size: 18px; 
    text-align: center;
}
/* element.ui 样式修改 */
.el-submenu__title, .el-menu-item {
    font-size: 16px;
}
/* .el-table__body-wrapper {
    margin-top: 1px !important;
} */

.el-dialog__body {
    padding: 0;
    border-top: 1px solid #E8E8E8;
    border-bottom: 1px solid #E8E8E8;
}

.el-select {
    width: 100%;
}
.el-date-editor.el-input {
    width: 100%;
}
/* .el-table .cell {
    white-space: nowrap;
    display: inline-block !important;
} */
.flex-row {
    display: flex;
    flex-direction: row;
    align-items: center;
}