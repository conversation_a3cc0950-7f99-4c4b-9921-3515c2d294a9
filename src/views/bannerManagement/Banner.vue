<template>
	<section class="banner-container common-container" ref="bannercontainer">
		<!--操作-->
		<section class="data-oper">
			<el-button type="primary" @click="editData" class="hzpbtn-primary" v-loading="loading">添加</el-button>
			<el-button type="primary" @click="submitSort" class="hzpbtn-primary" v-loading="loading" :disabled="submitSortDisabled">提交排序</el-button>
		</section>
		<!--列表-->
		<el-table :data="datas" :stripe="true" :height="containerHeight - 55" highlight-current-row v-loading="loading" id="out-table" class="data-content" border row-key="id">
			<el-table-column label="序号" align="center" type="index" width="80">
			</el-table-column>
			<!-- <el-table-column prop="sequence" label="位置" align="center" min-width="20%">
			</el-table-column> -->
			<el-table-column prop="thumimage" label="图片" align="center" min-width="20%">
				<template slot-scope="scope">
					<el-image style="width: 50px; height: 50px" :src="scope.row.thumimage" fit="cover" v-show="!!scope.row.thumimage"></el-image>
				</template>
			</el-table-column>
			<el-table-column prop="status" label="状态" :formatter="formatStatus" align="center" min-width="20%">
			</el-table-column>
			<el-table-column prop="remark" label="备注" align="center" min-width="20%">
			</el-table-column>
			<el-table-column label="操作" align="center" width="200">
				<template slot-scope="scope">
					<label class="hzpedit" @click.prevent="delData(scope.$index, scope.row)">删除</label>|
					<label class="hzpedit" @click.prevent="enableData(scope.$index, scope.row)">{{scope.row.status===0? '禁用' : '启用'}}</label>|
					<label class="hzpedit" @click.prevent="editData(scope.$index, scope.row)">编辑</label>
				</template>
			</el-table-column>
		</el-table>
		<!--分页-->
		<!-- <pagination :pageSize="pageSize" :pageNum="pageNum" :totalNum="totalNum" 
			@handlePageSizeChange="handlePageSizeChange" @handlePageCurrentChange="handlePageCurrentChange">
		</pagination> -->
		<!--编辑数据-->
        <el-dialog :title="dialogTitle + '数据'" :visible.sync="editDialogVisible" class="dialog-container" width="500px" @opened="openDialog">
			<el-form :model="formData" :rules="rules" ref="formData" label-width="100px" class="form-data" size="mini">
				<el-form-item label="图片" prop="">
					<UploadFile :imgUrl="formData.thumimage" @getFileUrl="getFileUrl" ref='UploadFile'></UploadFile>
				</el-form-item>
				<el-form-item label="备注" prop="">
					<el-input v-model="formData.remark" placeholder="请输入" type="textarea" :rows="4"></el-input>
				</el-form-item>
        	</el-form>
            <div slot="footer" class="dialog-footer">
              	<el-button @click="closeDialog" class="hzpbtn-close">关 闭</el-button>
                <el-button type="primary" @click="handleSubmit('formData')" v-loading="loading" class="hzpbtn-primary">保 存</el-button>
            </div>
        </el-dialog>
	</section>
</template>
<script>
	import { getBannerList } from '../../api/api';
	import { addBanner } from '../../api/api';
	import { editBanner } from '../../api/api';
	import { enableBanner } from '../../api/api';
	import { deleteBanner } from '../../api/api';
	import { sortBanner } from '../../api/api';
	import util from '../../common/js/util';
	import emEnum from '../../common/js/emEnum';
	// 行列拖拽
	import Sortable from 'sortablejs';
	// 分页组件
	// import Pagination from '../Pagination';
	// 上传文件组件
	import UploadFile from '../UploadFile';

	export default {
		components: {
			//Pagination,
			UploadFile
		},
		data() {
			return {
				sysCnName: emEnum.cnName,  // 中文名称
				sysEnName: emEnum.enName,  // 英文名称

				loginId: '',   // 登录用户

				submitSortDisabled: true,  // 提交排序不可用
				idArr: [],       // 广告图编号
				sequenceArr: [],  // 广告图序号

				loading: false,
				containerHeight: 0,
				datas: [],
				// 分页
				// pageSize: 100,
				// pageNum: 1,
        		// totalNum: 0,

				areaDatas: [],  // 区域数据
				roleOptions: emEnum.roleOptions, // 角色集合
				dialogTitle: '',   // 对话框标题 
				dialogBtnVal: '',  // 按钮值
				editDialogVisible: false,  // 编辑对话框
				formData: {
					bid: '',         // 广告图编号
					highimage: '',   // 广告高清图
					thumimage: '',   // 广告缩略图
					remark: ''       // 留言
				},
				rules: {
					username: [
						{ required: true, message: '请输入用户账号', trigger: 'blur' }
					],
					role: [
						{ required: true, message: '请选择用户角色', trigger: 'change' }
					],
					areaid: [
						{ required: true, message: '请选择所属区域', trigger: 'change' }
					]
				}
			}
		},
		methods: {
			// 状态显示转换
			formatStatus: function (row, column) {
				return row.status === 0? "已打开" : "已禁用"
			},
			// 时间显示格式转换
			formatCreatTime: function (row, column) {
				if(!!row.creattime) {
					return util.formatDate.format(new Date(row.creattime.replace(/-/g, '/')), 'yyyy-MM-dd HH:mm:ss');
				} else return row.creattime;
			},
			// 获取用户列表
			getData: function () {
				let _this = this;
				// 执行操作
				_this.loading = true;
				_this.closeDialog();  // 关闭编辑对话框
				let para = {};
				getBannerList(para, _this)
				.then(res=>res.data)
				.then(data => {
					_this.loading = false;
					let { code, msg } = data;
					if (code !== "0") {
						_this.$message({
							message: msg,
							type: 'error'
						});
					} else {
						// let { pageSize, pageNum, totalNum } = data.data;
						data = data.data.list;
						_this.datas = data;
						// 广告图序号，先清空，防止出错
						_this.idArr = [];
						_this.sequenceArr = [];
						_this.datas.forEach(item => {
							_this.sequenceArr.push(item.sequence);
						});
						// 分页
						// _this.pageSize = pageSize;
						// _this.pageNum = pageNum;
						// _this.totalNum = totalNum;
					}
				});
			},
			// 删除数据
			delData: function (index, row) {
				let _this = this;
				_this.$confirm('确认删除该广告？', '提示', {
					type: 'warning'
				}).then(() => {
					_this.loading = true;
					let para = {
						bid: row.id
					};
					deleteBanner(para, _this)
					.then(res=>res.data)
					.then(data => {
						_this.loading = false;
						let { msg, code } = data;
						if (code !== "0") {
							_this.$message({
								message: msg,
								type: 'error'
							});
						} else {
							_this.$message({
								message: msg,
								type: 'success'
							});
							_this.getData();
						}
					});
				}).catch((error) => {
					if(error!=='cancel') {
						_this.$message({
							message: error.message? error.message : error,
							type: 'error'
						});
					}
				});
			},
			// 修改数据状态（禁用、启用）
			enableData: function (index, row) {
				let _this = this;
				_this.$confirm('确认' + (row.status === 0? '禁用' : '启用') + '该广告？', '提示', {
					type: 'warning'
				}).then(() => {
					_this.loading = true;
					let para = {
						bid: row.id,
						status: row.status === 0? '1' : '0'
					};
					enableBanner(para, _this)
					.then(res=>res.data)
					.then(data => {
						_this.loading = false;
						let { msg, code } = data;
						if (code !== "0") {
							_this.$message({
								message: msg,
								type: 'error'
							});
						} else {
							_this.$message({
								message: msg,
								type: 'success'
							});
							_this.getData();
						}
					});
				}).catch((error) => {
					if(error!=='cancel') {
						_this.$message({
							message: error.message? error.message : error,
							type: 'error'
						});
					}
				});
			},
			// 重置输入
			resetForm: function (formName) {
				this.$refs[formName].resetFields();
			},
			// 编辑数据
			editData: function (index, row) {
				let formName = 'formData';
				// 打开对话框
				this.editDialogVisible = true;
				// 默认值
				if(!row) {
					this.dialogTitle = "添加";
					this.dialogBtnVal = "上传图片";
					if(this.$refs[formName]) this.$refs[formName].resetFields();
					this.formData.bid = '';
					this.formData.highimage = "";
					this.formData.thumimage = "";
					this.formData.remark = "";
				} else {
					this.dialogTitle = "编辑";
					this.dialogBtnVal = "重新上传";
					this.formData.bid = row.id;
					this.formData.highimage = row.highimage;
					this.formData.thumimage = row.thumimage;
					this.formData.remark = row.remark;
				}
			},
			// 打开编辑对话框
			openDialog: function () {
				// 改变组件的图片地址和按钮值
				this.$refs.UploadFile.changeFileUrl(this.formData.thumimage, this.dialogBtnVal);
				// 焦点放到获取焦点的输入框
				let ele = document.getElementById('focus-ele');
				if(!!ele) {
					ele.focus();
				}
			},
			// 关闭编辑对话框
			closeDialog: function () {
				this.editDialogVisible = false;
			},
			// 选择图片
			choseImage: function (file) {
				this.formData.thumimage = file.url;
			},
			// 提交数据
			handleSubmit: function (formName) {
				this.$refs[formName].validate((valid) => {
          			if (valid) {
						let _this = this;
						_this.$confirm('确认' + _this.dialogTitle + '广告图？', '提示', {
							type: 'warning'
						}).then(() => {
							_this.loading = true;
							if(!_this.formData.bid) {  // 添加
								let para = {
									highimage: _this.formData.highimage,
									thumimage: _this.formData.highimage,  // _this.formData.thumimage,
									remark: _this.formData.remark,
								};
								addBanner(para, _this)
								.then(res=>res.data)
								.then(data => {
									_this.loading = false;
									let { msg, code } = data;
									if (code !== "0") {
										_this.$message({
											message: msg,
											type: 'error'
										});
									} else {
										_this.$message({
											message: msg,
											type: 'success'
										});
										_this.getData();
									}
								});
							} else {  // 编辑数据
								let para = {
									bid: _this.formData.bid,
									highimage: _this.formData.highimage,
									thumimage: _this.formData.highimage,  // _this.formData.thumimage,
									remark: _this.formData.remark,
								};
								editBanner(para, _this)
								.then(res=>res.data)
								.then(data => {
									_this.loading = false;
									let { msg, code } = data;
									if (code !== "0") {
										_this.$message({
											message: msg,
											type: 'error'
										});
									} else {
										_this.$message({
											message: msg,
											type: 'success'
										});
										_this.getData();
									}
								});
							}
						}).catch((error) => {
							if(error!=='cancel') {
								_this.$message({
									message: error.message? error.message : error,
									type: 'error'
								});
							}
						});
					} else {
						return false;
					}
				});
			},
			// 行拖拽
			rowDrop: function () {
				const tbody = document.querySelector('.el-table__body-wrapper tbody');
				const _this = this;
				Sortable.create(tbody, {
					onEnd({ newIndex, oldIndex }) {
						if(_this.sequenceArr.length > 1) {  // 大于1行才有排序的必要
							const currRow = _this.datas.splice(oldIndex, 1)[0];
							_this.datas.splice(newIndex, 0, currRow);
							_this.submitSortDisabled = false;
							// 设置序号
							for(let i = 0; i < _this.datas.length; i++) {
								_this.datas[i].sequence = _this.sequenceArr[i];
							}
						}
					}
				});
			},
			// 提交排序
			submitSort: function () {
				let _this = this;
				// 广告图编号
				_this.idArr = _this.datas.map(item => {
					return item.id;
				});
				// 提交数据
				if(!!_this.idArr && !!_this.sequenceArr && _this.idArr.length === _this.sequenceArr.length) {				
					_this.$confirm('确认提交排序结果？', '提示', {
						type: 'warning'
					}).then(() => {
						_this.loading = true;
						let para = {
							bid: _this.idArr,
							sequence: _this.sequenceArr
						};
						sortBanner(para, _this)
						.then(res=>res.data)
						.then(data => {
							_this.loading = false;
							let { msg, code } = data;
							if (code !== "0") {
								_this.$message({
									message: msg,
									type: 'error'
								});
							} else {
								_this.$message({
									message: msg,
									type: 'success'
								});
								_this.getData();
							}
						});
					}).catch((error) => {
						if(error!=='cancel') {
							_this.$message({
								message: error.message? error.message : error,
								type: 'error'
							});
						}
					});
				} else {
					_this.$message({
						message: '广告排序数据出现问题，请刷新后重试！',
						type: 'error'
					});
				}
			},
			// 获取文件路径
			getFileUrl: function (high, thum) {
				this.formData.highimage = high;
				this.formData.thumimage = thum;
			},
			// 分页事件
			// handlePageSizeChange(val) {
			// 	this.pageSize = val;
			// 	this.getData();
			// },
			// handlePageCurrentChange(val) {
			// 	this.pageNum = val;
			// 	this.getData();
			// }
		},
		// 创建VUE实例后的钩子
		created: function () {
			// 登录ID
			this.loginId = util.userData.getLoginUserFieldValue(emEnum.loginUserFileds.uid, this);
		},
		// 挂载到DOM后的钩子
		mounted: function () {
			let _this = this;
			_this.containerHeight = _this.$refs.bannercontainer.offsetHeight;
			// 获取用户
			_this.getData();
			
			// 阻止默认行为
			document.body.ondrop = function (event) {
				event.preventDefault();
				event.stopPropagation();
			};
			// 行拖拽
			_this.rowDrop()
			// 绑定窗口尺寸改变事件改变数据表的高度
			window.onresize = () => {
				return (() => {
					// 为了避免频繁触发resize函数导致页面卡顿，使用定时器
					if (!this.timer && !!_this.$refs.bannercontainer) {
						this.timer = true
						_this.containerHeight = _this.$refs.bannercontainer.offsetHeight;
						let that = this
						setTimeout(function () {
							that.timer = false
						}, 100);
					}
				})();
			}
		}
	};
</script>

<style lang="scss">
.banner-container {
	height: calc(100vh - 120px);
}
</style>