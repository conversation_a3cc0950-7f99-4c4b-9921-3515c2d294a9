<template>
	<section class="combination-container common-container" ref="combinationcontainer">
		<!--操作-->
		<section class="data-oper">
			<el-button type="primary" @click="editData" class="hzpbtn-primary" v-loading="loading">添加</el-button>
			<!-- <el-button type="primary" @click="exportExcel" class="hzpbtn-primary">导出</el-button> -->
		</section>
		<!--列表-->
		<el-table :data="datas" :stripe="true" :height="containerHeight - 85" highlight-current-row v-loading="loading" id="out-table" 
			class="data-content" border row-key="id" @cell-click="cellClick">
			<el-table-column label="序号" align="center" type="index" width="80">
				<template slot-scope="scope">
    				<span>{{(pageNum - 1) * pageSize + scope.$index + 1}}</span>
				</template>
			</el-table-column>
			<el-table-column prop="name" label="视频名称" align="center" min-width="20%">
			</el-table-column>
			<el-table-column prop="url" label="视频" align="center" min-width="20%">
				<template slot-scope="scope">
					<el-tooltip class="item" effect="dark" content="点击查看视频" placement="top" v-show="!!scope.row.url">
						<video style="width: 50px; height: 50px" :src="scope.row.url"></video>
					</el-tooltip>
				</template>
			</el-table-column>
			<el-table-column prop="thumimage" label="图片" align="center" min-width="20%">
				<template slot-scope="scope">
					<el-image style="width: 50px; height: 50px" :src="scope.row.thumimage" fit="cover" v-show="!!scope.row.thumimage"></el-image>
				</template>
			</el-table-column>
			<el-table-column prop="remark" label="留言" align="center" min-width="20%">
			</el-table-column>
			<el-table-column prop="status" label="状态" :formatter="formatStatus" align="center" min-width="20%">
			</el-table-column>
			<el-table-column label="操作" align="center" width="200">
				<template slot-scope="scope">
					<label class="hzpedit" @click.prevent="delData(scope.$index, scope.row)">删除</label>|
					<label class="hzpedit" @click.prevent="enableData(scope.$index, scope.row)">{{scope.row.status==="0"? '禁用' : '启用'}}</label>|
					<label class="hzpedit" @click.prevent="editData(scope.$index, scope.row)">编辑</label>
				</template>
			</el-table-column>
		</el-table>
		<!--分页-->
		<pagination :pageSize="pageSize" :pageNum="pageNum" :totalNum="totalNum" 
			@handlePageSizeChange="handlePageSizeChange" @handlePageCurrentChange="handlePageCurrentChange">
		</pagination>
		<!--编辑数据-->
        <el-dialog :title="dialogTitle + '数据'" :visible.sync="editDialogVisible" class="dialog-container" width="500px" @opened="openDialog">
			<el-form :model="formData" :rules="rules" ref="formData" label-width="100px" class="form-data" size="mini">
				<el-form-item label="视频名称" prop="name">
					<el-input v-model="formData.name" placeholder="请输入" id="focus-ele"></el-input>
				</el-form-item>
				<el-form-item label="视    频" prop="">
					<UploadFileVideo :videoUrl="formData.url" @getVideoUrl="getVideoUrl" ref='UploadFileVideo'></UploadFileVideo>
				</el-form-item>
				<el-form-item label="图    片" prop="">
					<UploadFile :imgUrl="formData.thumimage" @getFileUrl="getFileUrl" ref='UploadFile'></UploadFile>
				</el-form-item>
				<el-form-item label="留    言" prop="">
					<el-input v-model="formData.remark" placeholder="请输入" type="textarea" :rows="4" maxlength="100"></el-input>
				</el-form-item>
        	</el-form>
            <div slot="footer" class="dialog-footer">
              	<el-button @click="closeDialog" class="hzpbtn-close">关 闭</el-button>
                <el-button type="primary" @click="handleSubmit('formData')" v-loading="loading" class="hzpbtn-primary">保 存</el-button>
            </div>
        </el-dialog>
		<!--视频显示-->
        <el-dialog :title="videoDialogTitle" :visible.sync="videoDialogVisible" class="dialog-container" width="600px" :before-close="handleClose">
			<section style="width: 90%; margin: 10px auto;">
				<video id="videoSec" style="width: 100%; height: 304px; margin-bottom: 10px;" :src="videoUrl" controls="controls">
					Your browser does not support the video tag.
				</video>
				<section>
					<strong style="font-size: 18px; ">留言：</strong>
					<p style="margin-left: 15px;">{{ videoRemark }}</p>
				</section>
			</section>
            <!-- <div slot="footer" class="dialog-footer">
              	<el-button @click="closeVideoDialog" class="hzpbtn-close">关 闭</el-button>
            </div> -->
        </el-dialog>
	</section>
</template>
<script>
	import { getVideoList } from '../../api/api';
	import { editVideo } from '../../api/api';
	import { enableVideo } from '../../api/api';
	import { deleteVideo } from '../../api/api';
	import util from '../../common/js/util';
	import emEnum from '../../common/js/emEnum';
	// 分页组件
	import Pagination from '../Pagination';
	// 上传文件组件
	import UploadFile from '../UploadFile';
	// 上传视频组件
	import UploadFileVideo from '../UploadFileVideo';

	export default {
		components: {
			Pagination,
			UploadFile,
			UploadFileVideo
		},
		data() {
			return {
				sysCnName: emEnum.cnName,  // 中文名称
				sysEnName: emEnum.enName,  // 英文名称

				loginId: '',   // 登录用户

				loading: false,
				containerHeight: 0,
				datas: [],
				// 分页
				pageSize: 100,
				pageNum: 1,
        		totalNum: 0,

				dialogTitle: '',   // 对话框标题 
				dialogVideoBtnVal: '',  // 按钮值
				dialogBtnVal: '',  // 按钮值
				editDialogVisible: false,  // 编辑对话框
				formData: {
					vid: '',           
					name:'',         // 视频名称
					url: '',         // 视频地址
					highimage: '',   // 高清图
					thumimage: '',   // 缩略图
					remark: '',      // 留言
				},
				rules: {
					name: [
						{ required: true, message: '请输入视频名称', trigger: 'blur' }
					]
				},

				videoDialogTitle: '',       // 视频对话框标题 
				videoDialogVisible: false,  // 视频对话框
				videoUrl: '',               // 视频地址
				videoRemark: '',            // 视频留言
			}
		},
		methods: {
			// 状态显示转换
			formatStatus: function (row, column) {
				return row.status === "0"? "启用" : (row.status === "1"? "禁用" : "")
			},
			// 获取列表
			getData: function () {
				let _this = this;
				// 执行操作
				_this.loading = true;
				_this.closeDialog();  // 关闭编辑对话框
				let para = {
					pageNum: _this.pageNum,
					pageSize: _this.pageSize
				};
				getVideoList(para, _this)
				.then(res=>res.data)
				.then(data => {
					_this.loading = false;
					let { code, msg } = data;
					if (code !== "0") {
						_this.$message({
							message: msg,
							type: 'error'
						});
					} else {
						let { pageSize, pageNum, totalNum } = data.data;
						data = data.data.list;
						_this.datas = data;
						// 分页
						_this.pageSize = pageSize;
						_this.pageNum = pageNum;
						_this.totalNum = totalNum;
					}
				});
			},
			// 删除数据
			delData: function (index, row) {
				let _this = this;
				_this.$confirm('确认删除该视频？', '提示', {
					type: 'warning'
				}).then(() => {
					_this.loading = true;
					let para = {
						vid: row.id
					};
					deleteVideo(para, _this)
					.then(res=>res.data)
					.then(data => {
						_this.loading = false;
						let { msg, code } = data;
						if (code !== "0") {
							_this.$message({
								message: msg,
								type: 'error'
							});
						} else {
							_this.$message({
								message: msg,
								type: 'success'
							});
							_this.getData();
						}
					});
				}).catch((error) => {
					if(error!=='cancel') {
						_this.$message({
							message: error.message? error.message : error,
							type: 'error'
						});
					}
				});
			},
			// 修改数据状态（禁用、启用）
			enableData: function (index, row) {
				let _this = this;
				_this.$confirm('确认' + (row.status === "0"? '禁用' : '启用') + '该视频？', '提示', {
					type: 'warning'
				}).then(() => {
					_this.loading = true;
					let para = {
						vid: row.id,
						status: row.status === "0"? '1' : '0'
					};
					enableVideo(para, _this)
					.then(res=>res.data)
					.then(data => {
						_this.loading = false;
						let { msg, code } = data;
						if (code !== "0") {
							_this.$message({
								message: msg,
								type: 'error'
							});
						} else {
							_this.$message({
								message: msg,
								type: 'success'
							});
							_this.getData();
						}
					});
				}).catch((error) => {
					if(error!=='cancel') {
						_this.$message({
							message: error.message? error.message : error,
							type: 'error'
						});
					}
				});
			},
			// 重置输入
			resetForm: function (formName) {
				this.$refs[formName].resetFields();
			},
			// 编辑数据
			editData: function (index, row) {
				let formName = 'formData';
				// 打开对话框
				this.editDialogVisible = true;
				// 默认值
				if(!row) {
					this.dialogTitle = "添加";
					this.dialogVideoBtnVal = "上传视频";
					this.dialogBtnVal = "上传图片";
					if(this.$refs[formName]) this.$refs[formName].resetFields();
					this.formData.vid = "";
					this.formData.name = "";
					this.formData.url = "";
					this.formData.highimage = "";
					this.formData.thumimage = "";
					this.formData.remark = "";
				} else {
					this.dialogTitle = "编辑";
					this.dialogVideoBtnVal = "重新上传";
					this.dialogBtnVal = "重新上传";
					this.formData.vid = row.id;
					this.formData.name = row.name;
					this.formData.url = row.url;
					this.formData.highimage = row.highimage;
					this.formData.thumimage = row.thumimage;
					this.formData.remark = row.remark;
				}
			},
			// 单元格点击事件
			cellClick: function (row, column, cell, event) {
				if(column.property === 'url' && !!row.url) {  // 点击的是视频列且视频地址存在则打开对话框显示视频
					this.videoDialogTitle = row.name;
					this.videoUrl = row.url;
					this.videoRemark = row.remark;
					// 打开视频对话框
					this.videoDialogVisible = true;
				}
			},
			// 处理关闭
			handleClose: function (done) {
				// 获取视频元素
				let videoSec = document.getElementById("videoSec");
				// 如果不是暂停则暂停
				if(!videoSec.paused) {
					videoSec.pause();
				}
				done();
				// this.$confirm('确认关闭？')
				// .then(_ => {
				// 	done();
				// })
				// .catch(_ => {});
			},
			// 关闭视频对话框
			closeVideoDialog: function () {
				this.videoDialogVisible = false;
			},
			// 打开编辑对话框
			openDialog: function () {
				// 改变组件的视频地址和按钮值
				this.$refs.UploadFileVideo.changeVideoUrl(this.formData.url, this.dialogVideoBtnVal);
				// 改变组件的图片地址和按钮值
				this.$refs.UploadFile.changeFileUrl(this.formData.thumimage, this.dialogBtnVal);
				// 焦点放到获取焦点的输入框
				let ele = document.getElementById('focus-ele');
				if(!!ele) {
					ele.focus();
				}
			},
			// 关闭编辑对话框
			closeDialog: function () {
				this.editDialogVisible = false;
			},
			// 提交数据
			handleSubmit: function (formName) {
				this.$refs[formName].validate((valid) => {
          			if (valid) {
						let _this = this;
						_this.$confirm('确认' + _this.dialogTitle + '视频？', '提示', {
							type: 'warning'
						}).then(() => {
							_this.loading = true;
							let para = {
								name: _this.formData.name,
								url: _this.formData.url,
								highimage: _this.formData.highimage,
								thumimage: _this.formData.thumimage,
								remark: _this.formData.remark,
							};
							if(!!_this.formData.vid) para["vid"] = _this.formData.vid;  // 视频编号(传入为修改，不传为新增)

							editVideo(para, _this)
							.then(res=>res.data)
							.then(data => {
								_this.loading = false;
								let { msg, code } = data;
								if (code !== "0") {
									_this.$message({
										message: msg,
										type: 'error'
									});
								} else {
									_this.$message({
										message: msg,
										type: 'success'
									});
									_this.getData();
								}
							});
						}).catch((error) => {
							if(error!=='cancel') {
								_this.$message({
									message: error.message? error.message : error,
									type: 'error'
								});
							}
						});
					} else {
						return false;
					}
				});
			},
			// 导出Execl
			exportExcel: function () {
				let outTable = document.querySelector("#out-table");
				if(!!outTable) {
					// 获取文件名
					let fileName = this.sysCnName + "_" + util.formatDate.format(new Date(), "yyyy-MM-dd HH:mm:ss") + "_" + this.$root.$route.name;
					// 导出Execl
					util.exportExcel(fileName, outTable);
				} else {
					this.$message({
						message: "未找到需要导出数据的表格",
						type: 'warning'
					});
				}
			},
			// 获取视频路径
			getVideoUrl: function (url) {
				this.formData.url = url;
			},
			// 获取文件路径
			getFileUrl: function (high, thum) {
				this.formData.highimage = high;
				this.formData.thumimage = thum;
			},
			// 分页事件
			handlePageSizeChange(val) {
				this.pageSize = val;
				this.getData();
			},
			handlePageCurrentChange(val) {
				this.pageNum = val;
				this.getData();
			}
		},
		// 创建VUE实例后的钩子
		created: function () {
			// 登录ID
			this.loginId = util.userData.getLoginUserFieldValue(emEnum.loginUserFileds.uid, this);
		},
		// 挂载到DOM后的钩子
		mounted: function () {
			let _this = this;
			_this.containerHeight = _this.$refs.combinationcontainer.offsetHeight;
			// 获取用户
			_this.getData();
			// 绑定窗口尺寸改变事件改变数据表的高度
			window.onresize = () => {
				return (() => {
					// 为了避免频繁触发resize函数导致页面卡顿，使用定时器
					if (!this.timer && !!_this.$refs.combinationcontainer) {
						this.timer = true
						_this.containerHeight = _this.$refs.combinationcontainer.offsetHeight;
						let that = this
						setTimeout(function () {
							that.timer = false
						}, 100);
					}
				})();
			}
		}
	};
</script>

<style lang="scss">
.combination-container {
	height: calc(100vh - 120px);
}
</style>