<template>
	<section class="orderView-container common-container" ref="orderViewcontainer">
		<!--编辑促销标签-->
		<el-row style="width: 100%; height: 30px;">
			<el-col :span="24">
				<el-button type="primary" @click="preStep" class="hzpbtn-primary" style="height: 35px; line-height: 10px; margin-right: 20px;">返回到订单管理</el-button>
			</el-col>
		</el-row>
		<el-form :model="formData" ref="formData" label-width="100px" class="form-data-nopadding" size="mini" style="padding: 20px 0 0 0;">
			<el-row>
				<el-col :span="24">
					<el-row class="order-edit-row">
						<el-col :span="8">
							<el-form-item label="订单编号" prop="">
								<el-input v-model="formData.code" placeholder="" :disabled="editDisabled"></el-input>
							</el-form-item>
						</el-col>
						<el-col :span="8">
							<el-form-item label="下单时间" prop="">
								<el-input v-model="formData.creattime" placeholder="" :disabled="editDisabled"></el-input>
							</el-form-item>
						</el-col>
						<el-col :span="8">
							<el-form-item label="下单人" prop="">
								<el-input v-model="formData.ordercareter" placeholder="" :disabled="editDisabled"></el-input>
							</el-form-item>
						</el-col>
					</el-row>
					<el-row class="order-edit-row">
						<el-col :span="8">
							<el-form-item label="培训公司" prop="">
								<el-input v-model="formData.aagencyname" placeholder="" :disabled="editDisabled"></el-input>
							</el-form-item>
						</el-col>
						<el-col :span="8">
							<el-form-item label="销售公司" prop="">
								<el-input v-model="formData.pagencyname" placeholder="" :disabled="editDisabled"></el-input>
							</el-form-item>
						</el-col>
						<el-col :span="8">
							<el-form-item label="代理1" prop="">
								<el-input v-model="formData.cagencyname" placeholder="" :disabled="editDisabled"></el-input>
							</el-form-item>
						</el-col>
					</el-row>
					<el-row class="order-edit-row">
						<el-col :span="8">
							<el-form-item label="代理2" prop="">
								<el-input v-model="formData.tagencyname" placeholder="" :disabled="editDisabled"></el-input>
							</el-form-item>
						</el-col>
						<el-col :span="8">
							<el-form-item label="收货人" prop="">
								<el-input v-model="formData.consignee" placeholder="" :disabled="editDisabled"></el-input>
							</el-form-item>
						</el-col>
						<el-col :span="8">
							<el-form-item label="收货电话" prop="">
								<el-input v-model="formData.linkphone" placeholder="" :disabled="editDisabled"></el-input>
							</el-form-item>
						</el-col>
					</el-row>
					<el-row class="order-edit-row">
						<el-col :span="24">
							<el-form-item label="收货地址" prop="">
								<el-input v-model="formData.finaladdress" placeholder="" :disabled="editDisabled"></el-input>
							</el-form-item>
						</el-col>
					</el-row>
					<el-row class="order-edit-row">
						<el-col :span="8">
							<el-form-item label="汇款人姓名" prop="">
								<el-input v-model="formData.remittername" placeholder="" :disabled="editDisabled"></el-input>
							</el-form-item>
						</el-col>
						<el-col :span="8">
							<el-form-item label="实际总金额" prop="">
								<el-input v-model="formData.totalamount" placeholder="" :disabled="editDisabled"></el-input>
							</el-form-item>
						</el-col>
						<el-col :span="8">
							<el-form-item label="备    注" prop="">
								<el-input v-model="formData.remark" placeholder="" :disabled="editDisabled"></el-input>
							</el-form-item>
						</el-col>
					</el-row>
					<el-row class="order-edit-row">
						<el-col :span="8">
							<el-form-item label="订单类型" prop="">
								<el-input v-model="formData.ordertype" placeholder="" :disabled="editDisabled"></el-input>
							</el-form-item>
						</el-col>
						<el-col :span="8">
							<el-form-item label="促销标题" prop="">
								<el-input v-model="formData.promotiontitle" placeholder="" :disabled="editDisabled"></el-input>
							</el-form-item>
						</el-col>
						<el-col :span="8">
							<el-form-item label="云仓ID" prop="">
								<el-input v-model="formData.u8id" placeholder="" :disabled="editDisabled"></el-input>
							</el-form-item>
						</el-col>
					</el-row>
					<el-row class="order-edit-row">
						<el-col :span="8">
							<el-form-item label="订单状态" prop="">
								<el-input v-model="formData.paystatus" placeholder="" :disabled="editDisabled"></el-input>
							</el-form-item>
						</el-col>
						<el-col :span="8">
							<el-form-item label="运单号" prop="">
								<el-input v-model="formData.waybillcode" placeholder="" :disabled="editDisabled"></el-input>
							</el-form-item>
						</el-col>
						<el-col :span="8">
							<el-form-item label="" prop="">
								<el-button type="primary" @click="checkLogistics" class="hzpbtn-primary" v-loading="loading" v-if="!!formData.waybillcode">查看物流 ></el-button>
							</el-form-item>
						</el-col>
					</el-row>
				</el-col>
			</el-row>
		</el-form>
		<!--列表-->
		<div class="stepContent">
			<template v-for="(step, stepIndex) in orderstep">
				<label v-bind:key="stepIndex + step.promotionstepid">{{ step.promotionstepname }}</label>
				<el-table :data="step.orderstep" highlight-current-row v-loading="loading" class="data-content" border 
					v-bind:key="stepIndex + step.promotionstepid + 1000000" :row-class-name="tableRowClassName">
					<el-table-column prop="spname" label="名称" align="center" min-width="20%">
					</el-table-column>
					<el-table-column prop="spcode" label="编号" align="center" min-width="20%">
					</el-table-column>
					<el-table-column prop="spthumImage" label="图片" align="center" min-width="20%">
						<template scope="scope">
							<el-image style="width: 50px; height: 50px" :src="scope.row.spthumImage" fit="cover" v-show="!!scope.row.spthumImage"></el-image>
						</template>
					</el-table-column>
					<el-table-column prop="spsize" label="数量" align="center" min-width="20%">
					</el-table-column>
					<el-table-column prop="spmoney" label="价格" align="center" min-width="20%">
					</el-table-column>
					<el-table-column prop="zpcode" label="类型" :formatter="formatType" align="center" width="100">
					</el-table-column>
					<!-- <el-table-column prop="spname" label="商品名称" align="center" min-width="20%">
					</el-table-column>
					<el-table-column prop="spcode" label="商品编号" align="center" min-width="20%">
					</el-table-column>
					<el-table-column prop="spthumImage" label="图片" align="center" min-width="20%">
						<template scope="scope">
							<el-image style="width: 50px; height: 50px" :src="scope.row.spthumImage" fit="cover" v-show="!!scope.row.spthumImage"></el-image>
						</template>
					</el-table-column>
					<el-table-column prop="spsize" label="商品数量" align="center" min-width="20%">
					</el-table-column>
					<el-table-column prop="spmoney" label="商品价格" align="center" min-width="20%">
					</el-table-column>
					<el-table-column prop="zpname" label="赠品名称" align="center" min-width="20%">
					</el-table-column>
					<el-table-column prop="zpcode" label="赠品编号" align="center" min-width="20%">
					</el-table-column>
					<el-table-column prop="zpthumImage" label="赠品图片" align="center" min-width="20%">
						<template scope="scope">
							<el-image style="width: 50px; height: 50px" :src="scope.row.zpthumImage" fit="cover" v-show="!!scope.row.zpthumImage"></el-image>
						</template>
					</el-table-column>
					<el-table-column prop="zpsize" label="赠品数量" align="center" min-width="20%">
					</el-table-column>
					<el-table-column prop="zpmoney" label="赠品价格" align="center" min-width="20%">
					</el-table-column> -->
				</el-table>
			</template>
		</div>
		<!--物流数据数据-->
        <el-dialog :title="'查看物流'" :visible.sync="logisticsDialogVisible" class="dialog-container" width="700px" @opened="openDialog">
			<el-table :data="logisticsDatas" :stripe="true" :height="400" highlight-current-row v-loading="loading" id="out-table" class="data-content" border row-key="id">
				<el-table-column label="序号" align="center" type="index" width="80">
					<template slot-scope="scope">
						<span>{{(pageNum - 1) * pageSize + scope.$index + 1}}</span>
					</template>
				</el-table-column>
				<el-table-column prop="name" label="订单单号" align="center" min-width="20%">
				</el-table-column>
				<el-table-column prop="code" label="下单时间" align="center" min-width="20%">
				</el-table-column>
				<el-table-column prop="type" label="下单人" align="center" min-width="20%">
				</el-table-column>
				<el-table-column prop="unit" label="物流信息" align="center" min-width="40%">
				</el-table-column>
				<el-table-column prop="remark" label="备注" align="center" min-width="20%">
				</el-table-column>
			</el-table>
        </el-dialog>
	</section>
</template>
<script>
	// 订单
	import { getOrderList } from '../../api/api';
	import { findwaybill } from '../../api/api';
	// 订单商品
	import { orderDetail } from '../../api/api';
	import { sortBanner } from '../../api/api';
	import util from '../../common/js/util';
	import emEnum from '../../common/js/emEnum';
	// 行列拖拽
	import Sortable from 'sortablejs';

	export default {
		data() {
			return {
				sysCnName: emEnum.cnName,  // 中文名称
				sysEnName: emEnum.enName,  // 英文名称

				loginId: '',   // 登录用户
				oid: '',       // 订单id
				opageSize: 100,
				oPageNum: 1,

				submitSortDisabled: true,  // 提交排序不可用
				idArr: [],        // 编号
				sequenceArr: [],  // 序号

				loading: false,
				containerHeight: 0,
				datas: [],
				orderstep: [],  // 促销步骤及商品集合

				orderDisabled: true,  // 是否不可编辑
				formData: {},

				editDisabled: true,  // 是否不可编辑
				logisticsDialogVisible: false,  // 物流对话框
				logisticsDatas: [],  // 物流数据
			}
		},
		methods: {
			// 类型显示转换
			formatType: function (row, column) {
				return !!row.zpcode? '赠品' : '';
			},
			// 获取列表
			getData: function () {
				let _this = this;
				if(!!_this.formData.code) {
					// 执行操作
					_this.loading = true;
					let para = {
						code: _this.formData.code,
					};
					orderDetail(para, _this)
					.then(res=>res.data)
					.then(data => {
						_this.loading = false;
						let { code, msg } = data;
						if (code !== "0") {
							_this.$message({
								message: msg,
								type: 'error'
							});
						} else {
							data = data.data.list;
							if(!!data && data.length > 0) {
								// 处理促销步骤
								let orderstep = data[0].orderstep;
								orderstep.forEach(ele => {
									// 促销步骤商品
									ele.orderstep.forEach(innerEle => {
										if(!!innerEle.zpcode) {
											innerEle.spcode = innerEle.zpcode;
											innerEle.spname = innerEle.zpname;
											innerEle.spthumImage = innerEle.zpthumImage;
											innerEle.spsize = innerEle.zpsize;
											innerEle.spmoney = innerEle.zpmoney;
										}
									});
									ele.orderstep.sort(util.compareID('id'));
								});
								_this.orderstep = orderstep;
							}
						}
					});
				}
			},
			// 查看物流
			checkLogistics: function (index, row) {
				// 获取物流信息
				let _this = this;
				if(!!_this.formData.waybillcode) {
					// 执行操作
					_this.loading = true;
					let para = {
						waybillcode: _this.formData.waybillcode,
					};
					findwaybill(para, _this)
					.then(res=>res.data)
					.then(data => {
						_this.loading = false;
						let { code, msg } = data;
						if (code !== "0") {
							_this.$message({
								message: msg,
								type: 'error'
							});
						} else {
							// 打开对话框
							_this.logisticsDialogVisible = true;
						}
					});
				}
			},
			// 打开编辑对话框
			openDialog: function () {

			},
			// 关闭编辑对话框
			closeDialog: function () {
				this.logisticsDialogVisible = false;
			},
			// 行拖拽
			rowDrop: function () {
				const tbody = document.querySelector('.el-table__body-wrapper tbody');
				const _this = this;
				Sortable.create(tbody, {
					onEnd({ newIndex, oldIndex }) {
						if(_this.sequenceArr.length > 1) {  // 大于1行才有排序的必要
							const currRow = _this.datas.splice(oldIndex, 1)[0];
							_this.datas.splice(newIndex, 0, currRow);
							_this.submitSortDisabled = false;
							// 设置序号
							for(let i = 0; i < _this.datas.length; i++) {
								_this.datas[i].sequence = _this.sequenceArr[i];
							}
						}
					}
				});
			},
			// 提交排序
			submitSort: function () {
				let _this = this;
				// 编号
				_this.idArr = _this.datas.map(item => {
					return item.id;
				});
				// 提交数据
				if(!!_this.idArr && !!_this.sequenceArr && _this.idArr.length === _this.sequenceArr.length) {				
					_this.$confirm('确认提交排序结果？', '提示', {
						type: 'warning'
					}).then(() => {
						// _this.loading = true;
						// let para = {
						// 	bid: _this.idArr,
						// 	sequence: _this.sequenceArr
						// };
						// sortBanner(para, _this)
						// .then(res=>res.data)
						// .then(data => {
						// 	_this.loading = false;
						// 	let { msg, code } = data;
						// 	if (code !== "0") {
						// 		_this.$message({
						// 			message: msg,
						// 			type: 'error'
						// 		});
						// 	} else {
						// 		_this.$message({
						// 			message: msg,
						// 			type: 'success'
						// 		});
						// 		_this.getData();
						// 	}
						// });
					}).catch((error) => {
						if(error!=='cancel') {
							_this.$message({
								message: error.message? error.message : error,
								type: 'error'
							});
						}
					});
				} else {
					_this.$message({
						message: '排序数据出现问题，请刷新后重试！',
						type: 'error'
					});
				}
			},
			// 上一步骤
			preStep: function () {
				this.$router.push({ path: '/order', query: {ps: this.oPageSize, pn: this.oPageNum}});
			},
			// 导出Execl
			exportExcel: function () {
				let outTable = document.querySelector("#out-table");
				if(!!outTable) {
					// 获取文件名
					let fileName = this.sysCnName + "_" + util.formatDate.format(new Date(), "yyyy-MM-dd HH:mm:ss") + "_" + this.$root.$route.name;
					// 导出Execl
					util.exportExcel(fileName, outTable);
				} else {
					this.$message({
						message: "未找到需要导出数据的表格",
						type: 'warning'
					});
				}
			},
			// 获取数据
			getOrderData: function () {
				let _this = this;
				// 执行操作
				_this.loading = true;
				let para = {
					oid: _this.oid,
				};
				getOrderList(para, _this)
				.then(res=>res.data)
				.then(data => {
					_this.loading = false;
					let { code, msg } = data;
					if (code !== "0") {
						_this.$message({
							message: msg,
							type: 'error'
						});
					} else {
						data = data.data.list;
						_this.formData = {...data[0]};
						// 订单类型
						if(_this.formData.ordertype === '1') _this.formData.ordertype = "活动订单";
						else if(_this.formData.ordertype === '2') _this.formData.ordertype = "促销订单";
						else _this.formData.ordertype = "";
						// 订单状态
						if(_this.formData.paystatus === 0) _this.formData.paystatus = "待支付";
						else if(_this.formData.paystatus === '1') _this.formData.paystatus = "已支付";
						else if(_this.formData.paystatus === '2') _this.formData.paystatus = "已发货";
						else if(_this.formData.paystatus === '3') _this.formData.paystatus = "已完成";
						else _this.formData.paystatus = "";
						// 获取对应商品列表
						_this.getData();
					}
				});
			},
			// 表格行Class名称
			tableRowClassName: function ({row, rowIndex}) {
				if (!!row.zpcode) {
					return 'zp-row';
				}
				return '';
			}
		},
		// 创建VUE实例后的钩子
		created: function () {
			// 登录ID
			this.loginId = util.userData.getLoginUserFieldValue(emEnum.loginUserFileds.uid, this);
			// 获取参数值
			let query = this.$route.query;
			this.oid = query.oid;
			this.oPageSize = query.ps;
			this.oPageNum = query.pn;
		},
		// 挂载到DOM后的钩子
		mounted: function () {
			let _this = this;
			_this.containerHeight = _this.$refs.orderViewcontainer.offsetHeight;
			// 获取订单数据
			_this.getOrderData();
			
			// 阻止默认行为
			document.body.ondrop = function (event) {
				event.preventDefault();
				event.stopPropagation();
			};
			// 行拖拽
			//_this.rowDrop()
			// 绑定窗口尺寸改变事件改变数据表的高度
			window.onresize = () => {
				return (() => {
					// 为了避免频繁触发resize函数导致页面卡顿，使用定时器
					if (!this.timer && !!_this.$refs.orderViewcontainer) {
						this.timer = true
						_this.containerHeight = _this.$refs.orderViewcontainer.offsetHeight;
						let that = this
						setTimeout(function () {
							that.timer = false
						}, 100);
					}
				})();
			}
		}
	};
</script>

<style lang="scss">
.orderView-container {
	height: calc(100vh - 120px);
	.stepContent {
		height: calc( 100vh - 120px - 385px);
		overflow-y: auto;
	}
}
.el-table .zp-row {
	background-color: #f0f9eb !important;
}
</style>