<template>
	<section class="order-container common-container" ref="ordercontainer">
		<!--操作-->
		<section class="data-oper">
			<el-row>
				<el-col :span="8" align="left">
					<el-button type="primary" @click="editCol" class="hzpbtn-primary">设置显示列</el-button>
					<el-button type="primary" @click="exportExcel" class="hzpbtn-primary" v-if="loginRole !== 2">导出订单</el-button>
					<el-button type="primary" @click="exportDetailExcelByPageNum" class="hzpbtn-primary" v-if="loginRole !== 2">导出明细</el-button>
				</el-col>
				<el-col :span="2" align="left">
					<el-select style="width: 98%;" v-model="sortAreaId" placeholder="按区域搜索" @change="searchList">
						<el-option
							v-for="item in allAreaDatas"
							:key="item.id"
							:label="item.areaname"
							:value="item.id">
						</el-option>
					</el-select>
				</el-col>
				<el-col :span="2" align="left">
					<el-select v-model="aagencyid" style="width: 97%;" clearable placeholder="按培训公司搜索" @change="searchList" v-if="loginRole === 1">
						<el-option
							v-for="item in agencyDatas"
							:key="item.aagencyid"
							:label="item.aagencyname"
							:value="item.aagencyid">
						</el-option>
					</el-select>
				</el-col>
				<el-col :span="2" align="left">
					<el-select v-model="paystatus" style="width: 97%;" clearable placeholder="按订单状态搜索" @change="searchList">
						<el-option
							v-for="item in orderStatusOptions"
							:key="item.key"
							:label="item.value"
							:value="item.key">
						</el-option>
					</el-select>
				</el-col>
				<el-col :span="2" align="center">
					<el-date-picker v-model="orderStartime" type="date" clearable placeholder="订单日期起" :picker-options="startPickerOptions" @change="orderTimeChange('start')"> </el-date-picker>
				</el-col>
				<el-col :span="2" align="center">
					<el-date-picker v-model="orderEndtime" type="date" clearable placeholder="订单日期止" :picker-options="endPickerOptions" @change="orderTimeChange('end')"> </el-date-picker>
				</el-col>
				<el-col :span="2" align="right">
					<el-select v-model="fuzzyField" style="width: 97%;" clearable placeholder="搜索字段" @change="searchColumn">
						<el-option
							v-for="item in fuzzyFieldOptions"
							:key="item.key"
							:label="item.value"
							:value="item.key">
						</el-option>
					</el-select>
				</el-col>
				<el-col :span="4" align="ceter">
					<el-input v-model="vague" style="width: 97%;" clearable placeholder="搜索内容" @change="searchList">
						<el-button slot="append" icon="el-icon-search"></el-button>
					</el-input>
				</el-col>
				<el-col :span="4" align="left">
					<el-select style="width: 98%;" filterable v-model="orderInvCode" clearable placeholder="请选择商品">
						<el-option
							v-for="item2 in selectSPList"
							:key="item2.code"
							:label="item2.name"
							:value="item2.code">
						</el-option>
					</el-select>
				</el-col>
				<!-- <el-col :span="3" align="right">
					<el-input v-model="fuzzyFieldValue" style="width: 97%;" clearable placeholder="搜索内容" @change="searchList" @keyup.native="searchList">
						<el-button slot="append" icon="el-icon-search"></el-button>
					</el-input>
				</el-col> -->
				<!-- <el-col :span="3" align="center">
					<a href="javascript:;" class="file" style="margin-top: 0px;">导入Excel
						<input id="file" type="file" @change='importExcel($event)' accept=".xlsx, .xls, .csv" />
					</a>
				</el-col> -->
				<el-button type="primary" @click="searchList" class="hzpbtn-primary">搜索</el-button>
			</el-row>
		</section>
		<!--列表-->
		<el-table :data="datas" :stripe="true" :height="containerHeight - 100" highlight-current-row v-loading="loading" id="out-table" 
			class="data-content" border row-key="id">
			<af-table-column label="序号" align="center" width="60">
				<template slot-scope="scope">
    				<span>{{(pageNum - 1) * pageSize + scope.$index + 1}}</span>
				</template>
			</af-table-column>
			<af-table-column prop="code" label="订单单号" align="center" v-if="colUnShowKeys.indexOf('code') === -1">
			</af-table-column>
			<af-table-column prop="creattime1" label=" 订单日期 " :formatter="formatCreatdate" align="center" v-if="colUnShowKeys.indexOf('creatdate') === -1">
			</af-table-column>
			<af-table-column prop="creattime2" label="订单时间" :formatter="formatCreattime" align="center" v-if="colUnShowKeys.indexOf('creattime') === -1">
			</af-table-column>
			<af-table-column prop="ordercaretername" label="下单人" align="center" v-if="colUnShowKeys.indexOf('ordercaretername') === -1">
			</af-table-column>
			<af-table-column prop="aagencyname" label="培训公司" align="center" v-if="colUnShowKeys.indexOf('aagencyname') === -1">
			</af-table-column>
			<af-table-column prop="pagencyname" label="销售公司" align="center" v-if="colUnShowKeys.indexOf('pagencyname') === -1">
			</af-table-column>
			<af-table-column prop="cagencyname" label="代理1" align="center" v-if="colUnShowKeys.indexOf('cagencyname') === -1">
			</af-table-column>
			<af-table-column prop="tagencyname" label="代理2" align="center" v-if="colUnShowKeys.indexOf('tagencyname') === -1">
			</af-table-column>
			<af-table-column prop="consignee" label="收货人" align="center" v-if="colUnShowKeys.indexOf('consignee') === -1">
			</af-table-column>
			<af-table-column prop="linkphone" label="收货电话" align="center" v-if="colUnShowKeys.indexOf('linkphone') === -1">
			</af-table-column>
			<af-table-column prop="province" label="省" align="center" v-if="colUnShowKeys.indexOf('province') === -1">
			</af-table-column>
			<af-table-column prop="city" label="市" align="center" v-if="colUnShowKeys.indexOf('city') === -1">
			</af-table-column>
			<af-table-column prop="district" label="县（区）" align="center" v-if="colUnShowKeys.indexOf('district') === -1">
			</af-table-column>
			<af-table-column prop="promotiontitle" label="促销标题" align="center" v-if="colUnShowKeys.indexOf('promotiontitle') === -1">
			</af-table-column>
			<af-table-column prop="totalamount" label="实际总金额" align="center" v-if="colUnShowKeys.indexOf('totalamount') === -1">
			</af-table-column>
			<!-- <af-table-column prop="voucherid" label="返单券ID" align="center" v-if="colUnShowKeys.indexOf('voucherid') === -1">
			</af-table-column>
			<af-table-column prop="voucheramount" label="返单券金额" :formatter="formatVoucherAmount" align="center" v-if="colUnShowKeys.indexOf('voucheramount') === -1">
			</af-table-column> -->
			<af-table-column prop="paystatus" label="订单状态" :formatter="formatPaystatus" align="center" v-if="colUnShowKeys.indexOf('paystatus') === -1">
			</af-table-column>
			<!-- <af-table-column prop="u8id" label="U8ID" align="center" v-if="colUnShowKeys.indexOf('u8id') === -1">
			</af-table-column> -->
			<!-- <af-table-column label="云仓ID" align="center" v-if="colUnShowKeys.indexOf('u8tradeid') === -1">
				<template slot-scope="scope">
					<label style="text-decoration: underline; color: #d0326c; cursor: pointer;" @click.prevent="checkU8idPayfailreason(scope.$index, scope.row)" v-if="scope.row.paystatus === '-3'">同步失败！</label>
					<label v-if="scope.row.paystatus !== '-3'">{{ scope.row.u8tradeid }}</label>
				</template>
			</af-table-column> -->
			<af-table-column prop="franchisee" label="加盟商姓名" align="center" v-if="colUnShowKeys.indexOf('franchisee') === -1">
			</af-table-column>
			<af-table-column prop="finaladdress" label="收货地址" align="left" v-if="colUnShowKeys.indexOf('finaladdress') === -1">
			</af-table-column>
			<el-table-column prop="remark" label="备注" align="center" min-width="150" v-if="colUnShowKeys.indexOf('remark') === -1">
			</el-table-column>
			<af-table-column prop="isreported" label="是否报备" :formatter="formatIsreported" align="center" v-if="colUnShowKeys.indexOf('isreported') === -1">
			</af-table-column>
			<af-table-column prop="storename" label="店铺名称" align="center" v-if="colUnShowKeys.indexOf('storename') === -1">
			</af-table-column>
			<!-- <af-table-column prop="notreportreason" label="未报备原因" align="center" v-if="colUnShowKeys.indexOf('notreportreason') === -1">
			</af-table-column> -->
			<af-table-column prop="ordertype" label="订单类型" :formatter="formatOrderType" align="center" v-if="colUnShowKeys.indexOf('ordertype') === -1">
			</af-table-column>
			<af-table-column label="操作" align="center" width="280" fixed="right" :key="Math.random()">
				<template slot-scope="scope">
					<!-- <label class="hzpedit" @click.prevent="delData(scope.$index, scope.row)" v-if="loginRole === 1 || scope.row.paystatus === '0'">删除</label> -->
					<!-- <span v-if="loginRole === 1 || scope.row.paystatus === '0'">|</span> -->
					<!-- <label class="hzpedit" @click.prevent="editPaystatusData(scope.$index, scope.row)" v-if="scope.row.paystatus === '0' || !scope.row.paystatus">付款</label> -->
					<!-- <span v-if="scope.row.paystatus === '0' || !scope.row.paystatus">|</span> -->
					<!-- <label class="hzpedit" @click.prevent="checkLogistics(scope.$index, scope.row)" v-if="!!scope.row.waybillcode">物流</label>
					<span v-if="!!scope.row.waybillcode">|</span> -->
					<label class="hzpedit" @click.prevent="lookShopInfo(scope.$index, scope.row)">查看店铺报备</label>|
					<label class="hzpedit" @click.prevent="nextStep(scope.$index, scope.row)" v-loading="currentOpDataList.indexOf(scope.row.code) >= 0">详情</label>|
					<el-dropdown trigger="hover" style="line-height: 0;" v-loading="currentOpDataList.indexOf(scope.row.code) >= 0">
						<label class="edit-content edit-more">更多</label>
						<el-dropdown-menu slot="dropdown" class="edit-more-content">
							<el-dropdown-item @click.native.prevent="editPaystatusData(scope.$index, scope.row)" v-if="scope.row.paystatus === '0' || !scope.row.paystatus || (loginRole === 1 && scope.row.paystatus === '-3')" v-loading="loading">付款</el-dropdown-item>
							<!-- <el-dropdown-item @click.native.prevent="cancelPaystatusData(scope.$index, scope.row)" v-if="(loginRole === 1 && scope.row.paystatus === '1')" v-loading="loading">订单撤回</el-dropdown-item> -->
							<el-dropdown-item @click.native.prevent="editData(scope.$index, scope.row)">编辑</el-dropdown-item>
							<el-dropdown-item @click.native.prevent="delData(scope.$index, scope.row)" v-if="loginRole === 1 || scope.row.paystatus === '0'">删除</el-dropdown-item>
						</el-dropdown-menu>
					</el-dropdown>
				</template>
			</af-table-column>
		</el-table>
		<!--分页-->
		<pagination :pageSize="pageSize" :pageNum="pageNum" :totalNum="totalNum" 
			@handlePageSizeChange="handlePageSizeChange" @handlePageCurrentChange="handlePageCurrentChange">
		</pagination>
		<!--编辑数据-->
        <el-dialog title="设置显示列" :visible.sync="colDialogVisible" class="dialog-container" width="600px" @close="closeColSet">
			<el-transfer v-model="colUnShowKeys" :data="colDatas" :titles="['显示列', '不显示列']" style="width: 500px; margin: 15px auto;"></el-transfer>
        </el-dialog>
		<!--编辑数据-->
        <el-dialog :title="dialogTitle + '数据'" :visible.sync="editDialogVisible" class="dialog-container" width="800px" @opened="openDialog">
			<el-form :model="formData" :rules="rules" ref="formData" label-width="100px" class="form-data" size="mini">
				<el-row class="order-edit-row">
					<el-col :span="8">
						<el-form-item label="订单编号" prop="code">
							<el-input v-model="formData.code" placeholder="" :disabled="editDisabled"></el-input>
						</el-form-item>
					</el-col>
					<el-col :span="8">
						<el-form-item label="订单日期" prop="creattime">
							<el-input v-model="formData.creattime" placeholder="" :disabled="editDisabled"></el-input>
						</el-form-item>
					</el-col>
					<el-col :span="7">
						<el-form-item label="下单人" prop="ordercaretername">
							<el-input v-model="formData.ordercaretername" placeholder="" :disabled="editDisabled"></el-input>
						</el-form-item>
					</el-col>
				</el-row>
				<el-row class="order-edit-row">
					<el-col :span="8">
						<el-form-item label="培训公司" prop="">
							<el-select v-model="formData.aagencyid" clearable placeholder="请选择" id="focus-ele">
								<el-option
									v-for="item in agencyDatas"
									:key="item.aagencyid"
									:label="item.aagencyname"
									:value="item.aagencyid">
								</el-option>
							</el-select>
						</el-form-item>
					</el-col>
                </el-row>
				<el-row class="order-edit-row">
					<!-- <el-col :span="12">
						<el-form-item label="培训公司" prop="">
							<el-select v-model="formData.aagencyid" clearable placeholder="请选择" id="focus-ele">
								<el-option
									v-for="item in agencyDatas"
									:key="item.aagencyid"
									:label="item.aagencyname"
									:value="item.aagencyid">
								</el-option>
							</el-select>
						</el-form-item>
					</el-col> -->
					<el-col :span="8">
						<el-form-item label="销售公司" prop="">
							<el-input v-model="formData.pagencyname" placeholder="请输入"></el-input>
						</el-form-item>
					</el-col>
					<el-col :span="8">
						<el-form-item label="代理1" prop="">
							<el-input v-model="formData.cagencyname" placeholder="请输入"></el-input>
						</el-form-item>
					</el-col>
					<el-col :span="7">
						<el-form-item label="代理2" prop="">
							<el-input v-model="formData.tagencyname" placeholder="请输入"></el-input>
						</el-form-item>
					</el-col>
				</el-row>
				<el-row class="order-edit-row">
					<el-col :span="12">
						<el-form-item label="收货人" prop="consignee">
							<el-input v-model="formData.consignee" placeholder="请输入"></el-input>
						</el-form-item>
					</el-col>
					<el-col :span="11">
						<el-form-item label="收货电话" prop="linkphone">
							<el-input v-model="formData.linkphone" placeholder="请输入"></el-input>
						</el-form-item>
					</el-col>
				</el-row>
				<el-row class="order-edit-row">
					<el-col :span="24">
						<el-form-item label="省市县(区)" prop="province">
							<region-picker :placeholder="{province: '选择省份', city: '选择市', district: '选择县（区）'}" 
								:province="formData.region.province" :city="formData.region.city" :district="formData.region.district" @onchange="regionChange">
							</region-picker>
						</el-form-item>
					</el-col>
				</el-row>
				<el-row class="order-edit-row">
					<el-col :span="24">
						<el-form-item label="收货地址" prop="address">
							<el-input v-model="formData.address" placeholder="请输入"></el-input>
						</el-form-item>
					</el-col>
				</el-row>
				<el-row class="order-edit-row">
					<el-col :span="24">
						<el-form-item label="备    注" prop="remark">
							<el-input v-model="formData.remark" placeholder="请输入"></el-input>
						</el-form-item>
					</el-col>
				</el-row>
				<el-row class="order-edit-row">
					<el-col :span="12">
						<el-form-item label="加盟商姓名" prop="franchisee">
							<el-input v-model="formData.franchisee" placeholder="请输入"></el-input>
						</el-form-item>
					</el-col>
					<el-col :span="11">
						<el-form-item label="是否报备" prop="isreported">
							<el-select v-model="formData.isreported" placeholder="请选择">
								<el-option
									v-for="item in isreportedOptions"
									:key="item.key"
									:label="item.value"
									:value="item.key">
								</el-option>
							</el-select>
						</el-form-item>
					</el-col>
				</el-row>
				<el-row class="order-edit-row">
					<el-col :span="12">
						<el-form-item label="店铺名称" prop="storename">
							<el-input v-model="formData.storename" placeholder="请输入"></el-input>
						</el-form-item>
					</el-col>
					<!-- <el-col :span="11">
						<el-form-item label="未报备原因" prop="notreportreason">
							<el-input v-model="formData.notreportreason" placeholder="请输入" maxlength="18"></el-input>
						</el-form-item>
					</el-col> -->
				</el-row>
				<el-row class="order-edit-row">
					<el-col :span="12">
						<el-form-item label="订单类型" prop="ordertype">
							<el-select v-model="formData.ordertype" placeholder="请选择" @change="ordertypeChange" :disabled="editDisabled">
								<el-option
									v-for="item in orderTypeOptions"
									:key="item.key"
									:label="item.value"
									:value="item.key">
								</el-option>
							</el-select>
						</el-form-item>
					</el-col>
					<el-col :span="11">
						<el-form-item label="促销标题" prop="promotiontitle">
							<el-input v-model="formData.promotiontitle" placeholder="" :disabled="promotiontitleDisabled"></el-input>
						</el-form-item>
					</el-col>
				</el-row>
				<el-row class="order-edit-row">
					<el-col :span="12">
						<el-form-item label="实际总金额" prop="totalamount">
							<el-input v-model="formData.totalamount" placeholder="请输入" @keyup.native="cash" :disabled="editDisabled"></el-input>
						</el-form-item>
					</el-col>
					<el-col :span="11" v-if="formData.paystatus === '0'">
						<el-form-item label="订单状态" prop="">
							<el-button type="primary" @click="clickPaystatus" class="hzpbtn-primary" v-loading="loading">付款</el-button>  <!--currentOpDataList.indexOf(formData.code) >= 0-->
						</el-form-item>
					</el-col>
				</el-row>
        	</el-form>
            <div slot="footer" class="dialog-footer">
              	<el-button @click="closeDialog" class="hzpbtn-close">关 闭</el-button>
                <el-button type="primary" @click="handleSubmit('formData')" v-loading="loading" class="hzpbtn-primary">保 存</el-button>
            </div>
        </el-dialog>
		<!--物流数据数据-->
        <el-dialog :title="'查看物流'" :visible.sync="logisticsDialogVisible" class="dialog-container" width="700px" @opened="openDialog">
			<el-table :data="logisticsDatas" :stripe="true" :height="400" highlight-current-row v-loading="loading" id="out-table" class="data-content" border row-key="id">
				<!-- <af-table-column label="序号" align="center" type="index" width="80">
				</af-table-column> -->
				<af-table-column prop="name" label="订单单号" align="center" min-width="20%">
				</af-table-column>
				<af-table-column prop="code" label="下单时间" align="center" min-width="20%">
				</af-table-column>
				<af-table-column prop="type" label="下单人" align="center" min-width="20%">
				</af-table-column>
				<af-table-column prop="unit" label="物流信息" align="center" min-width="40%">
				</af-table-column>
				<af-table-column prop="remark" label="备注" align="center" min-width="20%">
				</af-table-column>
			</el-table>
        </el-dialog>
		<!--导出进度条-->
		<el-dialog :title="'导出订单明细进度'" :visible.sync="progressDialogVisible" class="dialog-container" width="700px" @opened="openProgressDialog" :before-close="beforeCloseProgressDialog">
			<el-row style="height: 60px;">
				<el-col :span="22">
					<el-progress style="margin: 15px 20px;" :text-inside="true" :stroke-width="24" :percentage="progressPercentage" status="success"></el-progress>
				</el-col>
				<el-col :span="2">
					<span style="font-size: 35px; line-height: 65px;" v-loading="true" element-loading-spinner="el-icon-loading"></span>
				</el-col>
			</el-row>
        </el-dialog>
		<!--同步失败原因-->
        <el-dialog :title="'同步订单失败原因'" :visible.sync="u8idDialogVisible" class="dialog-container" width="700px">
			<div style="height: 200px; margin: 30px; color: red; font-size: 20px; line-height: 30px;">{{ u8idPayfailreason }}</div>
        </el-dialog>
		<!--查看图片-->
        <el-dialog :title="'查看店铺报备信息'" :visible.sync="picDialogVisible" class="dialog-container" width="700px">
			<div style="padding: 20px 40px;">
				<div>客户编号：{{ shopDetail.clientcode }}</div>
				<div>客户名称：{{ shopDetail.clientname }}</div>
				<div>培训公司：{{ shopDetail.aAgencyName }}</div>
				<div>销售公司：{{ shopDetail.pAgencyName }}</div>
				<div>代理1：{{ shopDetail.cAgencyName }}</div>
				<div>代理2：{{ shopDetail.tAgencyName }}</div>
				<div>店铺名称：{{ shopDetail.shopname }}</div>
				<div>店铺地址：{{ shopDetail.province+shopDetail.city+shopDetail.district+shopDetail.shopaddress }}</div>
				<div>法人名称：{{ shopDetail.legalpersonname }}</div>
				<!-- <div>收货人：{{ shopDetail.consignee }}</div>
				<div>联系电话：{{ shopDetail.linkphone }}</div> -->
				<div class="flex-row">
					<div>审核状态：</div>
					<label v-if="shopDetail.status == 0" class="recommend btj">未审核</label>
					<label v-if="shopDetail.status == 1" class="recommend tj">审核通过</label>
					<label v-if="shopDetail.status == 2" class="recommend jj">审核拒绝</label>
				</div>
			</div>
			<div style="display: flex; flex-direction: row;justify-content: space-between;padding:10px 40px 20px; ">
				<div style="display: flex;flex-direction: column; align-items: center;">
					<el-image
						style="width: 100px; height: 100px;"
						:src="srcList[3]"
						:preview-src-list="srcList">
					</el-image>
					<span style="margin-top: 10px;">法人身份证正面</span>
				</div>
				<div style="display: flex;flex-direction: column;align-items: center; ">
					<el-image
						style="width: 100px; height: 100px;"
						:src="srcList[4]"
						:preview-src-list="srcList">
					</el-image>
					<span style="margin-top: 10px;">法人身份证反面</span>
				</div>
				<div style="display: flex;flex-direction: column;align-items: center; ">
					<el-image
						style="width: 100px; height: 100px;"
						:src="srcList[2]"
						:preview-src-list="srcList">
					</el-image>
					<span style="margin-top: 10px;">营业执照</span>
				</div>
			
				<div style="display: flex;flex-direction: column; align-items: center;">
					<el-image
						style="width: 100px; height: 100px;"
						:src="srcList[0]"
						:preview-src-list="srcList">
					</el-image>
					<span style="margin-top: 10px;">门头照</span>
				</div>
				<div style="display: flex;flex-direction: column; align-items: center;">
					<el-image
						style="width: 100px; height: 100px;"
						:src="srcList[1]"
						:preview-src-list="srcList">
					</el-image>
					<span style="margin-top: 10px;">货架照</span>
				</div>
			</div>
		</el-dialog>
	</section>
</template>
<script>
	import { getOrderList,queryOrderShopReport } from '../../api/api';
	import { editOrder, confirmPayOrder, cancelPayOrder } from '../../api/api';
	import { enableOrder } from '../../api/api';
	import { editPaystatus } from '../../api/api';
	import { editOrderWaybillcode, getInventoryList } from '../../api/api';
	import { findwaybill } from '../../api/api';
	import { areabyroleList } from '../../api/api';
	// 订单商品
	import { orderDetail } from '../../api/api';
	import { orderDetailByPage } from '../../api/api';
	import util from '../../common/js/util';
	import emEnum from '../../common/js/emEnum';
	// 行列拖拽
	//import Sortable from 'sortablejs';
	// 上传文件组件
	import UploadFile from '../UploadFile';
	// 分页组件
	import Pagination from '../Pagination';

	export default {
		components: {
			UploadFile,
			Pagination
		},
		name: "订单管理",
		data() {
			// 校验省市县
			var validatorPcd = (rule, value, callback) => {
				if (!this.formData.region.province) {
					return callback(new Error('请选择省'));
				} else if (!this.formData.region.city) {
					return callback(new Error('请选择市'));
				} else if (!this.formData.region.district) {
					return callback(new Error('请选择县（区）'));
				}
				callback();
			};
			return {
				srcList:[],
				picDialogVisible:false,
				sysCnName: emEnum.cnName,  // 中文名称
				sysEnName: emEnum.enName,  // 英文名称
				pageName: 'order',  // 页面名称

				loginId: '',   // 登录用户
				loginRole: '', // 登录角色
				areaid: '',    // 登陆区域
				wxaccount: '', // 微信手机号

				loading: false,
				containerHeight: 0,
				datas: [],
				// 订单类型可选项
				orderTypeOptions: emEnum.orderTypeOptions,
				// 订单状态可选项
				orderStatusOptions: emEnum.orderStatusOptions,
				// 是否报备可选项
				isreportedOptions: emEnum.isreportedOptions,
				// 模糊字段可选项
				fuzzyFieldOptions: emEnum.fuzzyFieldOptions,

				// 分页
				pageSize: 100,
				pageNum: 1,
				totalNum: 0,

				allAreaDatas: [],  // 带全部的区域
				// 模糊字段
				fuzzyField: '',  // 模糊字段
				fuzzyFieldValue: '',  // 字段值

				// 筛选
				vague: '',      // 快速搜索
				sortAreaId: '',  // 搜索区域
				aagencyid: '',  // 培训公司
				paystatus: '',  // 订单状态
				orderStartime: '', // 订单日期起
				orderEndtime: '',  // 订单日期止
				orderStartStamp: '',  // 开始时间戳
				orderEndStamp: '',    // 结束时间戳

				startPickerOptions: {
					disabledDate: time => {
						if(!!this.orderEndtime) {
							return time.getTime() > new Date(this.orderEndtime);
						}
						return false;
					}
				},  // 订单日期始时间范围
				endPickerOptions: {
					disabledDate: time => {
						if(!!this.orderStartime) {
							return time.getTime() < new Date(this.orderStartime);
						}
						return false;
					}
				},  // 订单日期止时间范围

				promotiontitleDisabled: true,  // 促销标题不可编辑
				waybillcodeDisabled: false,    // 运单号不可编辑

				logisticsDialogVisible: false,  // 物流对话框
				logisticsDatas: [],  // 物流数据
				agencyDatas: [],   // 培训公司数据

				progressDialogVisible: false,  // 进度对话框
				progressPercentage: 0,         // 进度百分比

				u8idDialogVisible: false,  // u8id同步失败对话框
				u8idPayfailreason: '',     // u8id同步失败原因

				editDisabled: true,  // 是否不可编辑
				dialogTitle: '',     // 对话框标题
				editDialogVisible: false,  // 编辑对话框
				formData: {
					oid: '',           // 订单id
					code:'',           // 订单code
					creattime: '',     // 下单时间
					ordercaretername: '',  // 下单人

					aagencyid: '',    // 培训公司
					pagencyname: '',  // 销售公司
					cagencyname: '',  // 代理1
					tagencyname: '',  // 代理2

					consignee: '',     // 收货人
					linkphone:'',      // 收货电话
					region: {
						province: '',  // 省份
						city: '',      // 市
						district: '',  // 县（区）
					},  // 省市县（区）
					address: '',       // 地址
					remark: '',        // 备注

					franchisee: '',  // 加盟商姓名
					isreported: '',    // 是否报备
					storename: '',    // 店铺名称
					notreportreason: '', // 未报备原因
					totalamount: '',   // 实际总金额
					ordertype: '',     // 订单类型
					promotiontitle: '',// 促销标题
					paystatus: '',     // 订单状态
					waybillcode: '',   // 运单号
					u8tradeid: '',          // u8tradeid
				},
				rules: {
					consignee: [
						{ required: true, message: '请输入收货人', trigger: 'blur' }
					],
					linkphone: [
						{ required: true, message: '请输入收货电话', trigger: 'blur' }
					],
					address: [
						{ required: true, message: '请输入地址', trigger: 'blur' }
					],
					province: [
						{ required: true, validator: validatorPcd, trigger: 'blur'}
					],
				},
				fixedPos: 'right',  // 停靠位置
				// 显示列选择相关
				colDialogVisible: false,  // 设置显示列对话框
				colDatas: [{key: 'code', label: '订单单号'},
						{key: 'creatdate', label: '订单日期'},
						{key: 'creattime', label: '订单时间'},
						{key: 'ordercaretername', label: '下单人'},
						{key: 'aagencyname', label: '培训公司'},
						{key: 'pagencyname', label: '销售公司'},
						{key: 'cagencyname', label: '代理1'},
						{key: 'tagencyname', label: '代理2'},
						{key: 'consignee', label: '收货人'},
						{key: 'linkphone', label: '收货电话'},
						{key: 'province', label: '省'},
						{key: 'city', label: '市'},
						{key: 'district', label: '县（区）'},
						{key: 'franchisee', label: '加盟商姓名'},
						{key: 'finaladdress', label: '收货地址'},
						{key: 'isreported', label: '是否报备'},
						{key: 'storename', label: '店铺名称'},
						// {key: 'notreportreason', label: '未报备原因'},
						{key: 'totalamount', label: '实际总金额'},
						// {key: 'voucherid', label: '返单券ID'},
						// {key: 'voucheramount', label: '返单券金额'},
						{key: 'ordertype', label: '订单类型'},
						{key: 'promotiontitle', label: '促销标题'},
						{key: 'paystatus', label: '订单状态'},
						{key: 'u8tradeid', label: '云仓ID'},
						{key: 'remark', label: '备注'}],  // 列数据
				colUnShowKeys: ['creattime','province','city','district'],  // 不显示列的键集合

				detailDataList: [],    // 明细数据集合

				currentOpDataList: [],  // 当前操作明细数据集合

				shopDetail:{}, // 店铺报备信息

				selectSPList: [],
				orderInvCode: '',
			}
		},
		methods: {
			// 查看店铺报备
			lookShopInfo: function (index, row) {
				let _this = this;
				_this.shopDetail = {}
				// 执行操作
				_this.loading = true;
				let para = {
          			uid:_this.loginId,
					rid:row.reportid
				};
				queryOrderShopReport(para, _this)
				.then(res=>res.data)
				.then(data => {
					_this.loading = false;
					let { code, msg } = data;
					if (code !== "0") {
						_this.$message({
							message: msg,
							type: 'error'
						});
					} else {
						data = data.data
						_this.shopDetail = data
						_this.srcList = [data.doorphoto,data.shelvephoto,data.buslicphoto,data.legalpersonfrontphoto,data.legalpersonbackphoto]
						_this.picDialogVisible = true;
					}
				});
			},
			// 下单时间显示格式转换
			formatCreatdate: function (row, column) {
				let r = row.creattime.match(util.Regex.longDateRegex);
				if(r != null){
			　　	return util.formatDate.format(new Date(row.creattime.replace(/-/g, '/')), 'yyyy-MM-dd');
				} else return row.creattime;
				// if(!!row.creattime) {
				// 	return util.formatDate.format(new Date(row.creattime.replace(/-/g, '/')), 'yyyy-MM-dd');
				// } else return row.creattime;
			},
			formatCreattime: function (row, column) {
				let r = row.creattime.match(util.Regex.longDateRegex);
				if(r != null){
			　　	return util.formatDate.format(new Date(row.creattime.replace(/-/g, '/')), 'HH:mm:ss');
				} else return row.creattime;
			},
			// 订单类型显示转换：1活动订单，2促销订单
			formatOrderType: function (row, column) {
				if(row.ordertype === '1') return "活动订单";
				else if(row.ordertype === '2') return "促销订单";
				else return "";
			},
			// 返单券金额为0显示为空
			formatVoucherAmount: function (row, column) {
				if(row.voucheramount === 0 || row.voucheramount === '0') return "";
				else return row.voucheramount;
			},
			// 订单状态显示转换：0 待支付，1 已付款，2 已发货，3 部分发货
			formatPaystatus: function (row, column) {
				if(row.paystatus === '0' || row.paystatus === '-3' || !row.paystatus) return "待支付";
				else if(row.paystatus === '1') return "已付款";
				else if(row.paystatus === '2') return "已发货";
				else if(row.paystatus === '3') return "部分发货";
				else return "";
			},
			// 是否报备
			formatIsreported: function (row, column) {
				let rOp = emEnum.isreportedOptions.find(item=>{
					return item.key === row.isreported;
				});
				if(!rOp) return '';
				else return rOp.value;
			},
			// 金额校验
			cash: function (){
				this.formData.totalamount = this.formData.totalamount.replace(/[^\.\d]/g,'');   // 清除"数字"和"."以外的字符
				this.formData.totalamount = this.formData.totalamount.replace(/^\./g, '');      // 第一个字符不能为.
				let temp = this.formData.totalamount.replace(/\./g,'');  // 出现多个点则只去掉最后一个点
				if(this.formData.totalamount.length >= temp.length + 2) {
					this.formData.totalamount = this.formData.totalamount.substr(0, this.formData.totalamount.length - 1);
				}
			},
			// 订单时间改变事件
			orderTimeChange: function (control) {
				let _this = this;
				_this.orderStartStamp = '';
				_this.orderEndStamp = '';
				if(!!_this.orderStartime && !!_this.orderEndtime) {
					let s = util.formatDate.format(new Date(_this.orderStartime), 'yyyy-MM-dd') + ' 00:00:00';
					let e = util.formatDate.format(new Date(_this.orderEndtime), 'yyyy-MM-dd') + ' 23:59:59';
					_this.orderStartStamp = new Date(s).getTime();
					_this.orderEndStamp = new Date(e).getTime();
				}
				// 获取列表
				_this.searchList();
			},
			// 搜索字段
			searchColumn: function () {
				if(!!this.vague) {
					this.getData();	
				}
			},
			// 搜索数据
			searchList: function () {
				this.pageNum = 1;
				this.getData();
			},
			// 获取列表
			getData: function () {
				let _this = this;
				// 执行操作
				_this.loading = true;
				_this.closeDialog();  // 关闭编辑对话框
				let para = {
					status: 0,
					pageNum: _this.pageNum,
					pageSize: _this.pageSize
				};
				// 培训公司
				if(!!_this.aagencyid) para['aagencyid'] = _this.aagencyid;
				// 订单状态
				if(!!_this.paystatus) para['paystatus'] = _this.paystatus;
				// 订单起止时间
				if(!!_this.orderStartStamp && !!_this.orderEndStamp) {
					para['startime'] = _this.orderStartStamp;
					para['endtime'] = _this.orderEndStamp;
				}
				// 是否限制区域
				if(!!_this.sortAreaId) para['areaid'] = _this.sortAreaId;
				else if(_this.loginRole !== 1){
					para['areaid'] = _this.areaid;
				}
				// 是否传微信手机号
				if(!!_this.wxaccount && (_this.loginRole === 1 || !_this.sortAreaId)){
					para['wxaccount'] = _this.wxaccount;
				}
				// 是否有模糊字段
				if(!!_this.fuzzyField && !!_this.vague) {
					para['column'] = _this.fuzzyField;
					para['columnvalue'] = _this.vague;
				} else {
					// 快速搜索
					if(!!_this.vague) para['vague'] = _this.vague;
				}

				if (this.orderInvCode) {
					para["invcode"] = this.orderInvCode;
				}
				
				// 获取数据
				getOrderList(para, _this)
				.then(res=>res.data)
				.then(data => {
					_this.loading = false;
					let { code, msg } = data;
					if (code !== "0") {
						_this.$message({
							message: msg,
							type: 'error'
						});
					} else {
						let { pageSize, pageNum, totalNum } = data.data;
						data = data.data.list;
						_this.datas = data;
						// 分页
						_this.pageSize = pageSize;
						_this.pageNum = pageNum;
						_this.totalNum = totalNum;
					}
				});
			},
			// 重置输入
			resetForm: function (formName) {
				this.$refs[formName].resetFields();
			},
			// 删除数据
			delData: function (index, row) {
				let _this = this;
				_this.$confirm('确认删除该订单？', '提示', {
					type: 'warning'
				}).then(() => {
					_this.loading = true;
					let para = {
						oid: row.id,
						status: '1'
					};
					enableOrder(para, _this)
					.then(res=>res.data)
					.then(data => {
						_this.loading = false;
						let { msg, code } = data;
						if (code !== "0") {
							_this.$message({
								message: msg,
								type: 'error'
							});
						} else {
							_this.$message({
								message: '订单删除成功',
								type: 'success'
							});
							_this.getData();
						}
					});
				}).catch((error) => {
					if(error!=='cancel') {
						_this.$message({
							message: error.message? error.message : error,
							type: 'error'
						});
					}
				});
			},
			// 修改订单支付状态
			editPaystatusData: function (index, row) {
				let sta = '';
				let staCode = '';
				if(row.paystatus === '0' || row.paystatus === '-3' || !row.paystatus) {
					sta = '付款';
					staCode = '1';
				} 
				// else if(row.paystatus === '1') {
				// 	sta = '发货';
				// 	staCode = '2';
				// } 
				else if(row.paystatus === '2') {
					sta = '完成';
					staCode = '3';
				}
				this.paystatusDataUpdate(row.code, staCode, sta);
			},
			cancelPaystatusData(index, row) {
				let _this = this;
				_this.$confirm('确认撤回订单吗，撤回之后订单状态为待支付，并且取消云仓订单？', '提示', {
					type: 'warning'
				}).then(() => {
					let orderCode = row.code;
					
					_this.loading = true;
					let para = {
						ordercode: orderCode,
						updateuser: util.userData.getLoginUserFieldValue(emEnum.loginUserFileds.username, this)
					};
					cancelPayOrder(para, _this)
					.then(res=>res.data)
					.then(data => {
						_this.loading = false;
						let { code, msg } = data;
						if (code !== "0") {
							_this.$message({
								message: msg,
								type: 'error'
							});
						} else {
							_this.$message({
								message: msg,
								type: 'success'
							});
							_this.getData();
						}
					});
				})
			},
			clickPaystatus: function () {
				let sta = '';
				let staCode = '';
				if(this.formData.paystatus === '0') {
					sta = '付款';
					staCode = '1';
				} else {
					sta = '完成';
					staCode = '3';
				}
				this.paystatusDataUpdate(this.formData.code, staCode, sta);
			},
			// 订单状态更新
			paystatusDataUpdate: function (code, staCode, staDesc) {
				if(!!staCode) {
					let _this = this;
					_this.$confirm('确认订单【' + staDesc + '】？', '提示', {
						type: 'warning'
					}).then(() => {
						let orderCode = code;
						// 执行操作
						//util.array.addElement(_this.currentOpDataList, orderCode);
						_this.loading = true;
						// 付款使用新接口
						if (staCode == 1) {
							let para = {
								ordercode: orderCode,
								updateuser: util.userData.getLoginUserFieldValue(emEnum.loginUserFileds.username, this)
							};
							confirmPayOrder(para, _this)
							.then(res=>res.data)
							.then(data => {
								_this.loading = false;
								let { code, msg } = data;
								if (code !== "0") {
									_this.$message({
										message: msg,
										type: 'error'
									});
								} else {
									_this.$message({
										message: msg,
										type: 'success'
									});
									_this.getData();
								}
							});
						}else{
							let para = {
								code: orderCode,
								paystatus: staCode,
							};
							editPaystatus(para, _this)
							.then(res=>res.data)
							.then(data => {
								//util.array.removeElement(_this.currentOpDataList, orderCode);
								_this.loading = false;
								let { code, msg } = data;
								if (code !== "0") {
									_this.$message({
										message: msg,
										type: 'error'
									});
								} else {
									_this.$message({
										message: msg,
										type: 'success'
									});
									_this.getData();
								}
							});
						}
					}).catch((error) => {
						if(error!=='cancel') {
							_this.$message({
								message: error.message? error.message : error,
								type: 'error'
							});
						}
					});
				} else {
					this.$message({
						message: '订单支付状态有误，请确认后修改！',
						type: 'warning'
					});
				}
			},
			// 编辑数据
			editData: function (index, row) {
				if(!row) {
					this.$message({
						message: '请选择要编辑的数据！',
						type: 'warning'
					});
				} else {
					// 打开对话框
					this.editDialogVisible = true;
					this.dialogTitle = "编辑";
					// 默认值
					this.formData.oid = row.id;
					this.formData.code = row.code;
					this.formData.creattime = row.creattime;  //this.formatCreattime(row);
					this.formData.ordercaretername = row.ordercaretername;
					this.formData.aagencyid = row.aagencyid == null? '' : (row.aagencyid + '');
					this.formData.pagencyname = row.pagencyname;
					this.formData.cagencyname = row.cagencyname;
					this.formData.tagencyname = row.tagencyname;

					this.formData.consignee = row.consignee;
					this.formData.linkphone = row.linkphone;
					this.formData.region.province = row.province;
					this.formData.region.city = row.city;
					this.formData.region.district = row.district;
					this.formData.address = row.address;
					this.formData.remark = row.remark;

					this.formData.franchisee = row.franchisee;
					this.formData.storename = row.storename;
					this.formData.isreported = row.isreported;
					this.formData.notreportreason = row.notreportreason;
					this.formData.totalamount = row.totalamount;
					this.formData.ordertype = row.ordertype;
					this.formData.promotiontitle = row.promotiontitle;
					this.formData.paystatus = row.paystatus;
					this.formData.waybillcode = row.waybillcode;
					this.formData.u8tradeid = row.u8tradeid;
				}
			},
			// 查看物流
			checkLogistics: function (index, row) {
				// 获取物流信息
				let _this = this;
				if(!!row.waybillcode) {
					// 执行操作
					_this.loading = true;
					_this.closeLogisticsDialog();  // 关闭物流对话框
					let para = {
						waybillcode: row.waybillcode,
					};
					findwaybill(para, _this)
					.then(res=>res.data)
					.then(data => {
						_this.loading = false;
						let { code, msg } = data;
						if (code !== "0") {
							_this.$message({
								message: msg,
								type: 'error'
							});
						} else {
							// 打开对话框
							_this.logisticsDialogVisible = true;
						}
					});
				}
			},
			// 关闭物流对话框
			closeLogisticsDialog: function () {
				this.logisticsDialogVisible = false;
			},
			// 打开编辑对话框
			openDialog: function () {
				// 焦点放到获取焦点的输入框
				let ele = document.getElementById('focus-ele');
				if(!!ele) {
					ele.focus();
				}
			},
			// 打开进度对话框
			openProgressDialog: function () {
				this.progressPercentage = 0;
				this.detailDataList = [];
			},
			// 关闭进度对话框
			beforeCloseProgressDialog: function (done) {
				let _this = this;
				_this.$confirm('正在导出订单明细，关闭将结束导出，确认关闭？')
				.then(r => {
					done();
				}).catch(r => {});
			},
			closeProgressDialog: function () {
				this.progressDialogVisible = false;
				this.progressPercentage = 0;
				this.detailDataList = [];
			},
			// 查看失败原因
			checkU8idPayfailreason: function (index, row) {
				this.u8idPayfailreason = row.payfailreason;
				this.u8idDialogVisible = true;
			},
			// 关闭u8id失败原因对话框前清空
			beforeCloseU8idDialog: function () {
				this.u8idPayfailreason = '';
			},
			// 订单类型改变事件
			ordertypeChange: function () {
				// if(this.formData.ordertype === 1) {
				// 	this.promotiontitleDisabled = true;
				// } else {
				// 	this.promotiontitleDisabled = false;
				// }
			},
			// 订单状态改变事件
			orderstatusChange: function () {
				if(this.formData.paystatus === 0 || this.formData.paystatus === 1) {
					this.waybillcodeDisabled = true;
				} else {
					this.waybillcodeDisabled = false;
				}
			},
			// 关闭编辑对话框
			closeDialog: function () {
				this.editDialogVisible = false;
			},
			// 提交数据
			handleSubmit: function (formName) {
				this.$refs[formName].validate((valid) => {
          			if (valid) {
						let _this = this;
						_this.$confirm('确认' + _this.dialogTitle + '订单？', '提示', {
							type: 'warning'
						}).then(() => {
							_this.loading = true;
							if(!_this.formData.oid) {  // 添加
								_this.$message({
									message: '请选择要编辑的数据！',
									type: 'warning'
								});
							} else {  // 编辑数据
								let para = {
									oid: _this.formData.oid,

									aagencyid: _this.formData.aagencyid,
									pagencyname: _this.formData.pagencyname,
									cagencyname: _this.formData.cagencyname,
									tagencyname: _this.formData.tagencyname,

									consignee: _this.formData.consignee,
									linkphone: _this.formData.linkphone,
									province: _this.formData.region.province,
									city: _this.formData.region.city,
									district: _this.formData.region.district,
									address: _this.formData.address,
									remark: _this.formData.remark,

									franchisee: _this.formData.franchisee,
									storename: _this.formData.storename,
									isreported: _this.formData.isreported,
									notreportreason: _this.formData.notreportreason,
									totalamount: _this.formData.totalamount,
									ordertype: _this.formData.ordertype,
									// paystatus: _this.formData.paystatus,
									// waybillcode: _this.formData.waybillcode,
									// u8id: _this.formData.u8id,
								};
								// 培训公司姓名
								let agency = _this.agencyDatas.find(item=>{
									return item.aagencyid === para.aagencyid;
								});
								if(!!agency) {
									para["aagencyname"] = agency.aagencyname;
								} else {
									para["aagencyname"] = "";
								}

								editOrder(para, _this)
								.then(res=>res.data)
								.then(data => {
									_this.loading = false;
									let { msg, code } = data;
									if (code !== "0") {
										_this.$message({
											message: msg,
											type: 'error'
										});
									} else {
										_this.$message({
											message: msg,
											type: 'success'
										});
										_this.getData();
									}
								});
							}
						}).catch((error) => {
							if(error!=='cancel') {
								_this.$message({
									message: error.message? error.message : error,
									type: 'error'
								});
							}
						});
					} else {
						return false;
					}
				});
			},
			// 下一步骤
			nextStep: function (index, row) {
				this.$router.push({ path: '/orderView', query: {oid: row.id, ps: this.pageSize, pn: this.pageNum}});
			},
			// 导入Excel
			importExcel: function (e) {
				let file = e.target.files[0];
				// 判断文件
				if (!file) {
					this.$message({
						message: '请选择导入的文件！',
						type: 'warning'
					});
					return;
				}
				// 判断格式
				const types = file.name.split('.')[1];
				const fileType = ['xlsx', 'xls', 'csv'].some(item => item === types);
				if (!fileType) {
					this.$message({
						message: '格式错误，请重新选择！',
						type: 'warning'
					});
					return;
				} else {
					let _this = this;
					// 处理数据
					let xlsxJson;
					// 提交数据
					let dataArray = [];
					let nullArray = [];  // 运单号为null的数据
					util.file2Xce(file).then(tabJson => {
						// 清空选中文件
						let f = document.getElementById('file');
						f.value = '';
						// 处理数据
						if (tabJson && tabJson.length > 0) {
							xlsxJson = tabJson;
							// xlsxJson就是解析出来的json数据,数据格式如下
							// [
							//   {
							//     sheetName: sheet1
							//     sheet: sheetData
							//   }
							// ]
							// 处理要提交的数据
							if(!!xlsxJson && xlsxJson.length > 0) {
								xlsxJson.forEach(sheetItem => {
									if(!!sheetItem && !!sheetItem.sheet && sheetItem.sheet.length > 0) {
										sheetItem.sheet.forEach(item => {
											if(!!item.code) {
												if(!!item.waybillcode) {
													dataArray.push({code: item.code, waybillcode: !!item.waybillcode? item.waybillcode : ''});
												} else {
													nullArray.push(item.code);
												}
											}
										});
									}
								});
								if(!!dataArray && dataArray.length > 0) {
									// 执行操作
									_this.loading = true;
									let para = {
										data: dataArray
									};
									editOrderWaybillcode(para, _this)
									.then(res=>res.data)
									.then(data => {
										_this.loading = false;
										let { code, msg } = data;
										if (code !== "0") {
											_this.$message({
												message: msg,
												type: 'error'
											});
										} else {
											if(!!nullArray && nullArray.length > 0) {
												_this.$message({
													message: nullArray.join(',') + ' 等订单对应运单号为空，请确认后重新导入！',
													type: 'warning'
												});
											}
											_this.getData();
										}
									});
								} else {
									if(!!nullArray && nullArray.length > 0) {
										_this.$message({
											message: nullArray.join(',') + ' 等订单对应运单号为空，请确认后重新导入！',
											type: 'warning'
										});
									} else {
										_this.$message({
											message: '未读取到有效数据，请确认数据格式后重新导入！',
											type: 'warning'
										});
									}
								}
							}
						} else {
							_this.$message({
								message: '导入文件的数据为空！',
								type: 'warning'
							});
						}
					});
				}
			},
			// 导出订单Execl
			exportExcel: function () {
				let _this = this;
				// 执行操作
				_this.loading = true;
				_this.closeDialog();  // 关闭编辑对话框
				let para = {
					status: 0,        // 用禁用代替删除，故此处只查状态为0的数据，即启用的数据
					pageNum: 1,
					pageSize: _this.totalNum
				};
				// 培训公司
				if(!!_this.aagencyid) para['aagencyid'] = _this.aagencyid;
				// 订单状态
				if(!!_this.paystatus) para['paystatus'] = _this.paystatus;
				// 订单起止时间
				if(!!_this.orderStartStamp && !!_this.orderEndStamp) {
					para['startime'] = _this.orderStartStamp;
					para['endtime'] = _this.orderEndStamp;
				}
				// 快速搜索
				// if(!!_this.vague) para['vague'] = _this.vague;
				// 是否限制区域
				if(!!_this.sortAreaId) para['areaid'] = _this.sortAreaId;
				else if(_this.loginRole !== 1){
					para['areaid'] = _this.areaid;
				}
				// 是否传微信手机号
				if(!!_this.wxaccount && (_this.loginRole === 1 || !_this.sortAreaId)){
					para['wxaccount'] = _this.wxaccount;
				}
				// 是否有模糊字段
				if(!!_this.fuzzyField && !!_this.vague) {
					para['column'] = _this.fuzzyField;
					para['columnvalue'] = _this.vague;
				} else {
					// 快速搜索
					if(!!_this.vague) para['vague'] = _this.vague;
				}

				if (this.orderInvCode) {
					para["invcode"] = this.orderInvCode;
				}
				
				// 获取数据
				getOrderList(para, _this)
				.then(res=>res.data)
				.then(data => {
					_this.loading = false;
					let { code, msg } = data;
					if (code !== "0") {
						_this.$message({
							message: msg,
							type: 'error'
						});
					} else {
						data = data.data.list;
						_this.doExport(data);
					}
				});
			},
			// 执行导出
			doExport: function (tabData) {
				let _this = this;
				// 导出数据
				if(!!tabData && tabData.length > 0) {
					let i = 1;
					tabData.forEach(item => {
						item.id = i;
						item["creattime1"] = _this.formatCreatdate(item);
						item["creattime2"] = _this.formatCreattime(item);
						item.isreported = _this.formatIsreported(item);
						item.ordertype = _this.formatOrderType(item);
						item.voucheramount = _this.formatVoucherAmount(item);
						item.paystatus = _this.formatPaystatus(item);
						item.totalamount = parseFloat(item.totalamount);
						i++;
					});
					// 表头和对应字段
					let tHeader = [ '序号', '订单单号', '订单日期', '订单时间', '下单人', '培训公司', '销售公司', '代理1', '代理2', '收货人', '收货电话', 
						'省', '市', '县（区）', '收货地址', '加盟商姓名', '是否报备', '店铺名称', '未报备原因', '实际总金额', '返单券ID', '返单券金额', '订单类型', '促销标题', '订单状态', '云仓ID', '备注' ];
					let filterVal = [ 'id', 'code', 'creattime1', 'creattime2', 'ordercaretername', 'aagencyname', 'pagencyname', 'cagencyname', 'tagencyname', 'consignee', 'linkphone',
						'province', 'city', 'district', 'finaladdress', 'franchisee', 'isreported', 'storename', 'notreportreason', 'totalamount', 'voucherid', 'voucheramount', 'ordertype', 'promotiontitle', 'paystatus', 'u8tradeid', 'remark' ];
					// 获取文件名
					let fileName = _this.sysCnName + "_" + util.formatDate.format(new Date(), "yyyy-MM-dd HH:mm:ss") + "_" + _this.$root.$route.name;
					util.exportJsonToExcel(fileName, JSON.stringify(tabData), tHeader, filterVal);
				} else {
					this.$message({
						message: "没有需要导出的数据",
						type: 'warning'
					});
				}
			},
			// 导出明细Execl
			exportDetailExcel: function () {
				let _this = this;
				let detailData = [];
				// 执行操作
				_this.loading = true;
				let para = {
					status: 0,        // 用禁用代替删除，故此处只查状态为0的数据，即启用的数据
				};
				// 培训公司
				if(!!_this.aagencyid) para['aagencyid'] = _this.aagencyid;
				// 订单状态
				if(!!_this.paystatus) para['paystatus'] = _this.paystatus;
				// 订单起止时间
				if(!!_this.orderStartStamp && !!_this.orderEndStamp) {
					para['startime'] = _this.orderStartStamp;
					para['endtime'] = _this.orderEndStamp;
				}
				// 快速搜索
				// if(!!_this.vague) para['vague'] = _this.vague;
				// 是否限制区域
				if(!!_this.sortAreaId) para['areaid'] = _this.sortAreaId;
				else if(_this.loginRole !== 1){
					para['areaid'] = _this.areaid;
				}
				// 是否传微信手机号
				if(!!_this.wxaccount && (_this.loginRole === 1 || !_this.sortAreaId)){
					para['wxaccount'] = _this.wxaccount;
				}
				// 是否有模糊字段
				if(!!_this.fuzzyField && !!_this.vague) {
					para['column'] = _this.fuzzyField;
					para['columnvalue'] = _this.vague;
				} else {
					// 快速搜索
					if(!!_this.vague) para['vague'] = _this.vague;
				}

				orderDetail(para, _this)
				.then(res=>res.data)
				.then(data => {
					_this.loading = false;
					let { code, msg } = data;
					if (code !== "0") {
						_this.$message({
							message: msg,
							type: 'error'
						});
					} else {
						data = data.data.list;

						if(!!data && data.length > 0) {
							data.forEach(d => {
								d.orderstep.forEach(ele => {
									let bZxg = ele.promotionsteptype === "4";
									// 促销步骤商品
									ele.orderstep.forEach(innerEle => {
										let obj = {
											id: d.id,
											code: d.code,   // 订单编号
											ordercaretername: d.ordercaretername,  // 下单人
											aagencyname: d.aagencyname,  // 广域
											pagencyname: d.pagencyname,  // 销售公司
											cagencyname: d.cagencyname,  // 代理1
											tagencyname: d.tagencyname,  // 代理2

											franchisee: d.franchisee,  // 加盟商
											consignee: d.consignee,  // 收货人
											linkphone: d.linkphone,  // 电话
											province: d.province,  // 省
											city: d.city,  // 市
											district: d.district,  // 县

											finaladdress: d.finaladdress,  // 收获地址
											remark: d.remark,  // 备注
											creattime: isNaN(d.creattime) && !isNaN(Date.parse(d.creattime)) ? util.formatDate.format(new Date(d.creattime.replace(/-/g, '/')), 'yyyy-MM-dd HH:mm:ss') : d.creattime,  // 具体下单时间
											ordertype: _this.formatOrderType(d),  // 订单类型
											status: d.status === 0 ? '启用' : (d.status === 1 ? '禁用' : '结单'),  // 订单状态
											paystatus: _this.formatPaystatus(d),  // 支付状态

											spcode: innerEle.spcode,
											spname: innerEle.spname,
											stepname: innerEle.orderstepname,
											spsize: innerEle.spsize,
											spmoney: innerEle.spmoney,
											spamount: 0,  //parseFloat(innerEle.spsize) * parseFloat(innerEle.spmoney),
											spccomunitname: innerEle.spccomunitname,
											zpcode: bZxg ? '' : innerEle.spcode,
										};
										detailData.push(obj);
										if(!!innerEle.zpinventorylist && innerEle.zpinventorylist.length > 0) {
											innerEle.zpinventorylist.forEach(zpItem => {
												let zpObj = {
													id: d.id,
													code: d.code,   // 订单编号
													ordercaretername: d.ordercaretername,  // 下单人
													aagencyname: d.aagencyname,  // 广域
													pagencyname: d.pagencyname,  // 销售公司
													cagencyname: d.cagencyname,  // 代理1
													tagencyname: d.tagencyname,  // 代理2

													franchisee: d.franchisee,  // 加盟商
													consignee: d.consignee,  // 收货人
													linkphone: d.linkphone,  // 电话
													province: d.province,  // 省
													city: d.city,  // 市
													district: d.district,  // 县

													finaladdress: d.finaladdress,  // 收获地址
													remark: d.remark,  // 备注
													creattime: isNaN(d.creattime) && !isNaN(Date.parse(d.creattime)) ? util.formatDate.format(new Date(d.creattime.replace(/-/g, '/')), 'yyyy-MM-dd HH:mm:ss') : d.creattime,  // 具体下单时间
													ordertype: _this.formatOrderType(d),  // 订单类型
													status: d.status === 0 ? '启用' : (d.status === 1 ? '禁用' : '结单'),  // 订单状态
													paystatus: _this.formatPaystatus(d),  // 支付状态

													spcode: zpItem.zpcode,
													spname: zpItem.zpname,
													stepname: innerEle.orderstepname,
													spsize: zpItem.zpsize,
													spmoney: zpItem.zpmoney,
													spamount: 0,
													spccomunitname: zpItem.zpccomunitname,
													zpcode: zpItem.zpcode,
												}
												detailData.push(zpObj);
											});
										}
									});
								});
							});
							// 数据存在则按id排序
							// if(detailData != null && detailData.length > 0)
							// {
							// 	detailData.sort(util.compareID('id'));
							// }
							
							_this.doDetailExport(detailData);
						} else {
							this.$message({
								message: "没有需要导出的数据",
								type: 'warning'
							});
						}
					}
				});
			},
			// 导出明细Execl(分页)
			exportDetailExcelByPage: function () {
				let _this = this;
				// 进度条
				_this.progressDialogVisible = true;

				// 执行操作
				let para = {
					status: 0,        // 用禁用代替删除，故此处只查状态为0的数据，即启用的数据
					pageNum: 1,
					pageSize: _this.totalNum
				};
				// 培训公司
				if(!!_this.aagencyid) para['aagencyid'] = _this.aagencyid;
				// 订单状态
				if(!!_this.paystatus) para['paystatus'] = _this.paystatus;
				// 订单起止时间
				if(!!_this.orderStartStamp && !!_this.orderEndStamp) {
					para['startime'] = _this.orderStartStamp;
					para['endtime'] = _this.orderEndStamp;
				}
				// 快速搜索
				// if(!!_this.vague) para['vague'] = _this.vague;
				// 是否限制区域
				if(!!_this.sortAreaId) para['areaid'] = _this.sortAreaId;
				else if(_this.loginRole !== 1){
					para['areaid'] = _this.areaid;
				}
				// 是否传微信手机号
				if(!!_this.wxaccount && (_this.loginRole === 1 || !_this.sortAreaId)){
					para['wxaccount'] = _this.wxaccount;
				}
				// 是否有模糊字段
				if(!!_this.fuzzyField && !!_this.vague) {
					para['column'] = _this.fuzzyField;
					para['columnvalue'] = _this.vague;
				} else {
					// 快速搜索
					if(!!_this.vague) para['vague'] = _this.vague;
				}
				
				// 获取数据
				getOrderList(para, _this)
				.then(res=>res.data)
				.then(data => {
					let { code, msg } = data;
					if (code !== "0") {
						_this.$message({
							message: msg,
							type: 'error'
						});
					} else {
						data = data.data.list;
						// 明细数据
						let detailData = [];
						if(!!data && data.length > 0) {
							// 订单编号
							let orderCodeArr = data.map(a => a.code);
							let oldLen = orderCodeArr.length;
							// 循环订单数据获取明细
							let num = 20;
							// 根据订单数据获取明细
							while(!!orderCodeArr && orderCodeArr.length > 0) {
								// 关闭进度条即中断导出
								if(!_this.progressDialogVisible) {
									return;
								}
								// 设置百分比
								_this.progressPercentage = parseInt(100 - orderCodeArr.length / oldLen * 100);
								// 获取前20个
								let codeArr = orderCodeArr.splice(0, num);
								if(!!codeArr && codeArr.length > 0) {
									let codeStr = codeArr.join(',');
									// 参数
									let para = {
										code: codeStr
									};
									// 分批获取明细数据
									orderDetailByPage(para, _this)
									.then(res=>res.data)
									.then(data => {
										let { code, msg } = data;
										if (code === "0") {
											data = data.data.list;
											if(!!data && data.length > 0) {
												data.forEach(d => {
													d.orderstep.forEach(ele => {
														let bZxg = ele.promotionsteptype === "4";
														// 促销步骤商品
														ele.orderstep.forEach(innerEle => {
															let obj = {
																id: d.id,
																code: d.code,   // 订单编号
																ordercaretername: d.ordercaretername,  // 下单人
																aagencyname: d.aagencyname,  // 广域
																pagencyname: d.pagencyname,  // 销售公司
																cagencyname: d.cagencyname,  // 代理1
																tagencyname: d.tagencyname,  // 代理2

																franchisee: d.franchisee,  // 加盟商
																consignee: d.consignee,  // 收货人
																linkphone: d.linkphone,  // 电话
																province: d.province,  // 省
																city: d.city,  // 市
																district: d.district,  // 县

																finaladdress: d.finaladdress,  // 收获地址
																remark: d.remark,  // 备注
																creattime: isNaN(d.creattime) && !isNaN(Date.parse(d.creattime)) ? util.formatDate.format(new Date(d.creattime.replace(/-/g, '/')), 'yyyy-MM-dd HH:mm:ss') : d.creattime,  // 具体下单时间
																ordertype: _this.formatOrderType(d),  // 订单类型
																status: d.status === 0 ? '启用' : (d.status === 1 ? '禁用' : '结单'),  // 订单状态
																paystatus: _this.formatPaystatus(d),  // 支付状态

																spcode: innerEle.spcode,
																spname: innerEle.spname,
																stepname: innerEle.orderstepname,
																spsize: innerEle.spsize,
																spmoney: innerEle.spmoney,
																spamount: 0,  //parseFloat(innerEle.spsize) * parseFloat(innerEle.spmoney),
																spccomunitname: innerEle.spccomunitname,
																zpcode: bZxg ? '' : innerEle.spcode,
															};
															detailData.push(obj);
															if(!!innerEle.zpinventorylist && innerEle.zpinventorylist.length > 0) {
																innerEle.zpinventorylist.forEach(zpItem => {
																	let zpObj = {
																		id: d.id,
																		code: d.code,   // 订单编号
																		ordercaretername: d.ordercaretername,  // 下单人
																		aagencyname: d.aagencyname,  // 广域
																		pagencyname: d.pagencyname,  // 销售公司
																		cagencyname: d.cagencyname,  // 代理1
																		tagencyname: d.tagencyname,  // 代理2

																		franchisee: d.franchisee,  // 加盟商
																		consignee: d.consignee,  // 收货人
																		linkphone: d.linkphone,  // 电话
																		province: d.province,  // 省
																		city: d.city,  // 市
																		district: d.district,  // 县

																		finaladdress: d.finaladdress,  // 收获地址
																		remark: d.remark,  // 备注
																		creattime: isNaN(d.creattime) && !isNaN(Date.parse(d.creattime)) ? util.formatDate.format(new Date(d.creattime.replace(/-/g, '/')), 'yyyy-MM-dd HH:mm:ss') : d.creattime,  // 具体下单时间
																		ordertype: _this.formatOrderType(d),  // 订单类型
																		status: d.status === 0 ? '启用' : (d.status === 1 ? '禁用' : '结单'),  // 订单状态
																		paystatus: _this.formatPaystatus(d),  // 支付状态

																		spcode: zpItem.zpcode,
																		spname: zpItem.zpname,
																		stepname: innerEle.orderstepname,
																		spsize: zpItem.zpsize,
																		spmoney: zpItem.zpmoney,
																		spamount: 0,
																		spccomunitname: zpItem.zpccomunitname,
																		zpcode: zpItem.zpcode,
																	}
																	detailData.push(zpObj);
																});
															}
														});
													});
												});
											}
										}
									});
								}
							}
						}
						// 导出数据
						if(_this.progressDialogVisible) {
							if(detailData && detailData.length > 0) {
								_this.doDetailExport(detailData);
								_this.progressPercentage = 100;
								// 关闭进度条提示框
								_this.closeProgressDialog();
							} else {
								_this.progressPercentage = 100;
								// 关闭进度条提示框
								_this.closeProgressDialog();
								_this.$message({
									message: "没有需要导出的数据",
									type: 'warning'
								});
							}
						}
					}
				});
			},
			// 导出明细Execl(分页)
			exportDetailExcelByPageNum: function () {
				let _this = this;
				_this.$confirm('该操作是一个耗时操作，请耐心等待', '提示', {
					type: 'warning'
				}).then(() => {
					// 进度条
					_this.progressDialogVisible = true;
					// 明细分页
					let _pageSize = 10;//80;  // 20200628 暂时改为10页一起查
					let _pageNum = 1;
					let para = {
						status: 0,        // 用禁用代替删除，故此处只查状态为0的数据，即启用的数据
						pageNum: _pageNum,
						pageSize: _pageSize
					};
					// 培训公司
					if(!!_this.aagencyid) para['aagencyid'] = _this.aagencyid;
					// 订单状态
					if(!!_this.paystatus) para['paystatus'] = _this.paystatus;
					// 订单起止时间
					if(!!_this.orderStartStamp && !!_this.orderEndStamp) {
						para['startime'] = _this.orderStartStamp;
						para['endtime'] = _this.orderEndStamp;
					}
					// 快速搜索
					// if(!!_this.vague) para['vague'] = _this.vague;
					// 是否限制区域
					if(!!_this.sortAreaId) para['areaid'] = _this.sortAreaId;
					else if(_this.loginRole !== 1){
						para['areaid'] = _this.areaid;
					}
					// 是否传微信手机号
					if(!!_this.wxaccount && (_this.loginRole === 1 || !_this.sortAreaId)){
						para['wxaccount'] = _this.wxaccount;
					}
					// 是否有模糊字段
					if(!!_this.fuzzyField && !!_this.vague) {
						para['column'] = _this.fuzzyField;
						para['columnvalue'] = _this.vague;
					} else {
						// 快速搜索
						if(!!_this.vague) para['vague'] = _this.vague;
					}
					_this.callOrderDetailByPage(para);
				}).catch((error) => {
					if(error!=='cancel') {
						_this.$message({
							message: error.message? error.message : error,
							type: 'error'
						});
					}
				});
			},
			// 执行导出数据
			doExportDetailExcelByPageNum: function () {
				let _this = this;
				// 导出数据
				if(_this.progressDialogVisible) {
					_this.progressPercentage = 100;
					if(_this.detailDataList && _this.detailDataList.length > 0) {
						_this.doDetailExport(_this.detailDataList);
					} else {
						_this.$message({
							message: "没有需要导出的数据",
							type: 'warning'
						});
					}
					// 关闭进度条提示框
					_this.closeProgressDialog();
				} else {
					_this.detailDataList = [];
				}
			},
			// 调用分页方法
			callOrderDetailByPage: function (para) {
				let _this = this;
				// 获取数据
				orderDetailByPage(para, _this)
				.then(res=>res.data)
				.then(data => {
					let { code, msg } = data;
					if (code === "0") {
						// 分页
						let { pageSize, pageNum, totalNum } = data.data;
						// 明细数据
						data = data.data.list;
						if(!!data && data.length > 0) {
							data.forEach(d => {
								d.orderstep.forEach(ele => {
									let bZxg = ele.promotionsteptype === "4";
									// 促销步骤商品
									ele.orderstep.forEach(innerEle => {
										let obj = {
											id: d.id,
											code: d.code,   // 订单编号
											creattime: d.creattime,  // 订单日期
											ordercaretername: d.ordercaretername,  // 下单人
											aagencyname: d.aagencyname,  // 广域
											pagencyname: d.pagencyname,  // 销售公司
											cagencyname: d.cagencyname,  // 代理1
											tagencyname: d.tagencyname,  // 代理2

											franchisee: d.franchisee,  // 加盟商
											consignee: d.consignee,  // 收货人
											linkphone: d.linkphone,  // 电话
											province: d.province,  // 省
											city: d.city,  // 市
											district: d.district,  // 县

											finaladdress: d.finaladdress,  // 收获地址
											remark: d.remark,  // 备注
											creattime: isNaN(d.creattime) && !isNaN(Date.parse(d.creattime)) ? util.formatDate.format(new Date(d.creattime.replace(/-/g, '/')), 'yyyy-MM-dd HH:mm:ss') : d.creattime,  // 具体下单时间
											ordertype: _this.formatOrderType(d),  // 订单类型
											status: d.status === 0 ? '启用' : (d.status === 1 ? '禁用' : '结单'),  // 订单状态
											paystatus: _this.formatPaystatus(d),  // 支付状态

											spcode: innerEle.spcode,
											spname: innerEle.spname,
											stepname: innerEle.orderstepname,
											spsize: innerEle.spsize,
											spmoney: innerEle.spmoney,
											spamount: 0,  //parseFloat(innerEle.spsize) * parseFloat(innerEle.spmoney),
											spccomunitname: innerEle.spccomunitname,
											zpcode: bZxg ? '' : innerEle.spcode,
											// 店铺名称
											storename: d.storename,
											// 是否报备
											isreported: d.isreported,
											// 未报备原因
											notreportreason: d.notreportreason,
										};
										if(_this.progressDialogVisible) _this.detailDataList.push(obj);
										if(!!innerEle.zpinventorylist && innerEle.zpinventorylist.length > 0) {
											innerEle.zpinventorylist.forEach(zpItem => {
												let zpObj = {
													id: d.id,
													code: d.code,   // 订单编号
													creattime: d.creattime,  // 订单日期
													ordercaretername: d.ordercaretername,  // 下单人
													aagencyname: d.aagencyname,  // 广域
													pagencyname: d.pagencyname,  // 销售公司
													cagencyname: d.cagencyname,  // 代理1
													tagencyname: d.tagencyname,  // 代理2

													franchisee: d.franchisee,  // 加盟商
													consignee: d.consignee,  // 收货人
													linkphone: d.linkphone,  // 电话
													province: d.province,  // 省
													city: d.city,  // 市
													district: d.district,  // 县

													finaladdress: d.finaladdress,  // 收获地址
													remark: d.remark,  // 备注
													creattime: isNaN(d.creattime) && !isNaN(Date.parse(d.creattime)) ? util.formatDate.format(new Date(d.creattime.replace(/-/g, '/')), 'yyyy-MM-dd HH:mm:ss') : d.creattime,  // 具体下单时间
													ordertype: _this.formatOrderType(d),  // 订单类型
													status: d.status === 0 ? '启用' : (d.status === 1 ? '禁用' : '结单'),  // 订单状态
													paystatus: _this.formatPaystatus(d),  // 支付状态

													spcode: zpItem.zpcode,
													spname: zpItem.zpname,
													stepname: innerEle.orderstepname,
													spsize: zpItem.zpsize,
													spmoney: zpItem.zpmoney,
													spamount: 0,
													spccomunitname: zpItem.zpccomunitname,
													zpcode: zpItem.zpcode,
													// 店铺名称
													storename: d.storename,
													// 是否报备
													isreported: d.isreported,
													// 未报备原因
													notreportreason: d.notreportreason,
												}
												if(_this.progressDialogVisible) _this.detailDataList.push(zpObj);
											});
										}
									});
								});
							});
						}
						// 设置循环条件及百分比
						let currentNum = pageSize * pageNum; 
						if(totalNum <= currentNum || !_this.progressDialogVisible) {
							_this.progressPercentage = 100;
							_this.doExportDetailExcelByPageNum();
							return;
						} else {
							_this.progressPercentage = parseInt(currentNum / totalNum * 100);
							let para = {
								status: 0,        // 用禁用代替删除，故此处只查状态为0的数据，即启用的数据
								pageNum: pageNum + 1,
								pageSize: pageSize
							};
							// 培训公司
							if(!!_this.aagencyid) para['aagencyid'] = _this.aagencyid;
							// 订单状态
							if(!!_this.paystatus) para['paystatus'] = _this.paystatus;
							// 订单起止时间
							if(!!_this.orderStartStamp && !!_this.orderEndStamp) {
								para['startime'] = _this.orderStartStamp;
								para['endtime'] = _this.orderEndStamp;
							}
							// 快速搜索
							// if(!!_this.vague) para['vague'] = _this.vague;
							// 是否限制区域
							if(!!_this.sortAreaId) para['areaid'] = _this.sortAreaId;
							else if(_this.loginRole !== 1){
								para['areaid'] = _this.areaid;
							}
							// 是否传微信手机号
							if(!!_this.wxaccount && (_this.loginRole === 1 || !_this.sortAreaId)){
								para['wxaccount'] = _this.wxaccount;
							}
							// 是否有模糊字段
							if(!!_this.fuzzyField && !!_this.vague) {
								para['column'] = _this.fuzzyField;
								para['columnvalue'] = _this.vague;
							} else {
								// 快速搜索
								if(!!_this.vague) para['vague'] = _this.vague;
							}
							_this.callOrderDetailByPage(para);
						}
					} else {
						_this.doExportDetailExcelByPageNum();
					}
				});
			},
			// 合计显示转换
			formatAmount: function (row, column) {
				// 数量
				let size = parseFloat(row.spsize);
				if(size.toString() == 'NaN') return 0;
				// 单价
				let money = parseFloat(row.spmoney);
				if(money.toString() == 'NaN') return 0;
				// 返回合计
				return size * money;
			},
			// 类型显示转换
			formatType: function (row, column) {
				return !!row.zpcode? '赠品' : '';
			},
			// 执行导出
			doDetailExport: function (tabData) {
				let _this = this;
				// 导出数据
				if(!!tabData && tabData.length > 0) {
					let i = 1;
					tabData.forEach(item => {
						item.id = i;
						item.creattime1 = util.formatDate.format(new Date(item.creattime.replace(/-/g, '/')), 'yyyy-MM-dd');
						item.zpcode = _this.formatType(item);
						item.spmoney = parseFloat(item.spmoney);
						item.spsize = parseFloat(item.spsize);
						item.spamount = parseFloat(_this.formatAmount(item));
						item.isreported = _this.formatIsreported(item);
						i++;
					});
					// 表头和对应字段
					let tHeader = [ '序号', '订单编号', '订单日期', '下单人', '广域', '销售公司', '代理1', '代理2', '加盟商', '收货人', '电话', '省份', '市', '县（区）', '收货地址',  '是否报备', '店铺名称',  '未报备原因', 
						'备注', '具体下单时间', '订单类型', '订单状态', '支付状态', '商品类别', '商品名称', '商品编码', '单价', '数量', '总价', '是否赠品' ];
					let filterVal = [ 'id', 'code', 'creattime1', 'ordercaretername', 'aagencyname', 'pagencyname', 'cagencyname', 'tagencyname', 'franchisee', 'consignee', 'linkphone', 'province', 'city', 'district', 'finaladdress', 'isreported', 'storename', 'notreportreason',
						'remark', 'creattime', 'ordertype', 'status', 'paystatus', 'stepname', 'spname', 'spcode', 'spmoney', 'spsize', 'spamount', 'zpcode' ];
					// 获取文件名
					let fileName = _this.sysCnName + "_" + util.formatDate.format(new Date(), "yyyy-MM-dd HH:mm:ss") + "_" + '明细表';
					util.exportJsonToExcel(fileName, JSON.stringify(tabData), tHeader, filterVal);
				} else {
					this.$message({
						message: "没有需要导出的数据",
						type: 'warning'
					});
				}
			},
			// 设置显示列
			editCol: function () {
				this.colDialogVisible = true;
			},
			// 关闭设置显示列
			closeColSet: function () {
				this.fixedPos = "none";
				// 存储不显示列
				util.editStore.SetUnShowCols(this.pageName, this.colUnShowKeys);
				this.fixedPos = "right";
			},
			// 省市县（区）改变
			regionChange: function (val) {
				if(this.formData.region.province !== val.province) {
					this.formData.region.province = val.province;
					this.formData.region.city = '';
					this.formData.region.district = '';
				} else if(this.formData.region.city !== val.city) {
					this.formData.region.city = val.city;
					this.formData.region.district = '';
				} else if(this.formData.region.district !== val.district) {
					this.formData.region.district = val.district;
				}
			},
			// 获取培训公司
			getAllAgencys: function () {
				let _this = this;
				_this.loading = true;
				let para = {};
				areabyroleList(para, _this)
				.then(res=>res.data)
				.then(data => {
					let { code, msg } = data;
					if (code !== "0") {
						_this.$message({
							message: msg,
							type: 'error'
						});
					} else {
						let agencyData = data.data.list;
						let areaData = data.data.list2;
						// 培训公司
						_this.agencyDatas = agencyData;
						// 区域
						let areaDatas = areaData.filter(item => item.status === 0);
						// 处理可选区域
						_this.allAreaDatas.push({id: '', areaname: '全部'});
						if(!!_this.areaid) {
							let areas = _this.areaid.split(',');
							areaDatas.forEach(item => {
								areas.forEach(a => {
									if((a + '') === (item.areaId + '')) {
										_this.allAreaDatas.push({id: item.areaId, areaname: item.areaName});
									}
								});
							});
							// if(!!_this.allAreaDatas && _this.allAreaDatas.length > 0) {
							// 	_this.sortAreaId = _this.allAreaDatas[0].id;
							// }
						} else {
							areaDatas.forEach(item => {
								// _this.allAreaDatas.push({id: item.id, areaname: item.areaname});
								_this.allAreaDatas.push({id: item.areaId, areaname: item.areaName});
							});
						}
						_this.loading = false;
						// 获取数据
						_this.getData();
					}
				});
			},
			// 分页事件
			handlePageSizeChange(val) {
				this.pageSize = val;
				this.getData();
			},
			handlePageCurrentChange(val) {
				this.pageNum = val;
				this.getData();
			},

			// 获取商品对应列表
			getSPData: function () {
				let _this = this;
				// 执行操作
				_this.loading = true;
				let para = {
					pageNum: 1,
					pageSize: 10000,
				};
				getInventoryList(para, _this)
				.then(res=>res.data)
				.then(data => {
					_this.loading = false;
					let { code, msg } = data;
					if (code !== "0") {
						_this.$message({
							message: msg,
							type: 'error'
						});
					} else {
						_this.selectSPList = data.data.list;
					}
				});
			},
		},
		// 创建VUE实例后的钩子
		created: function () {
			// 登录ID
			this.loginId = util.userData.getLoginUserFieldValue(emEnum.loginUserFileds.uid, this);
			// 根据权限做不同的操作
			let role = util.userData.getLoginUserFieldValue(emEnum.loginUserFileds.role, this);
			this.loginRole = role;
			// 用户区域ID
			if(role === 1 || role === 3 || role === 4) this.areaid = '';
			else this.areaid = util.userData.getLoginUserFieldValue(emEnum.loginUserFileds.areaid, this);
			// 微信手机号
			this.wxaccount = util.userData.getLoginUserFieldValue(emEnum.loginUserFileds.wxaccount, this);
			// 获取不显示列
			let unShowCols = util.editStore.GetUnShowCols(this.pageName);
			if(!!unShowCols && unShowCols !== 'none') {
				this.colUnShowKeys = unShowCols;
			}
			// 获取参数值
			let query = this.$route.query;
			if(!!query && !!query.ps && !!query.pn) {
				this.pageSize = parseInt(query.ps);
				this.pageNum = parseInt(query.pn);
			}

			this.getSPData();
		},
		// 挂载到DOM后的钩子
		mounted: function () {
			let _this = this;
			_this.containerHeight = _this.$refs.ordercontainer.offsetHeight;
			// 获取培训公司
			_this.getAllAgencys();
			// 绑定窗口尺寸改变事件改变数据表的高度
			window.onresize = () => {
				return (() => {
					// 为了避免频繁触发resize函数导致页面卡顿，使用定时器
					if (!this.timer && !!_this.$refs.ordercontainer) {
						this.timer = true
						_this.containerHeight = _this.$refs.ordercontainer.offsetHeight;
						let that = this
						setTimeout(function () {
							that.timer = false
						}, 100);
					}
				})();
			}
		}
	};
</script>

<style lang="scss">
.order-container {
	height: calc(100vh - 120px);
	.edit-content {
		color: #3ca2ff;
		margin-left: 10px;
		margin-right: 10px;
		cursor: pointer;
	}
	.edit-more::after {
		// content: ' ﹀';
		font-size: 12px;
		height: 12px;
		line-height: 12px;
		vertical-align: baseline;
	}
	.edit-more:hover::after {
		// content: ' ︿';
		font-size: 12px;
		height: 12px;
		line-height: 12px;
		vertical-align: text-top;
	}
}
.recommend {
	font-size:14px;
	padding: 2px;
	border-radius: 5px;
	color: #fff;
	width:65px;
	text-align: center;
	display: inline-block;
}
.tj {
	background: #00B136;
}
.btj {
	background: #ff9800;
}
.jj{
	background: red;
}
</style>