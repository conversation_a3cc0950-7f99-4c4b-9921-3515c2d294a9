<template>
	<section class="orderView-container common-container" ref="orderViewcontainer">
		<!--编辑促销标签-->
		<el-row style="width: 100%; height: 30px;">
			<el-col :span="24">
				<el-button type="primary" @click="preStep" class="hzpbtn-primary" style="height: 35px; line-height: 10px; margin-right: 20px;">返回到订单管理</el-button>
				<el-button type="primary" @click="exportExcel" class="hzpbtn-primary" style="height: 35px; line-height: 10px;">导出</el-button>
				<el-button type="primary" @click="fhOrder" class="hzpbtn-primary" style="height: 35px; line-height: 10px;">发货</el-button>
				<el-button type="primary" @click="cancleOrder" class="hzpbtn-primary" style="height: 35px; line-height: 10px;">撤回订单</el-button>
			</el-col>
		</el-row>
		<el-form :model="formData" ref="formData" label-width="100px" class="form-data" size="mini" style="padding: 20px 0 0 0;">
			<el-collapse v-model="activeName" @change="collapseChange" accordion>
				<el-collapse-item :title="'订单【' + formData.code + '】详情（点击' + orderColl + '）'" name="coll">
					<el-row  style="margin: auto 10px;">
						<el-col :span="24">
							<el-row class="order-edit-row">
								<el-col :span="6">
									<el-form-item label="订单编号" prop="">
										<el-input v-model="formData.code" placeholder="" :disabled="editDisabled"></el-input>
									</el-form-item>
								</el-col>
								<el-col :span="6">
									<el-form-item label="订单日期" prop="">
										<el-input v-model="formData.creattime" placeholder="" :disabled="editDisabled"></el-input>
									</el-form-item>
								</el-col>
								<el-col :span="6">
									<el-form-item label="下单人" prop="">
										<el-input v-model="formData.ordercareter" placeholder="" :disabled="editDisabled"></el-input>
									</el-form-item>
								</el-col>
								<el-col :span="6">
									<el-form-item label="培训公司" prop="">
										<el-input v-model="formData.aagencyname" placeholder="" :disabled="editDisabled"></el-input>
									</el-form-item>
								</el-col>
							</el-row>
							<el-row class="order-edit-row">
								<el-col :span="6">
									<el-form-item label="销售公司" prop="">
										<el-input v-model="formData.pagencyname" placeholder="" :disabled="editDisabled"></el-input>
									</el-form-item>
								</el-col>
								<el-col :span="6">
									<el-form-item label="代理1" prop="">
										<el-input v-model="formData.cagencyname" placeholder="" :disabled="editDisabled"></el-input>
									</el-form-item>
								</el-col>
								<el-col :span="6">
									<el-form-item label="代理2" prop="">
										<el-input v-model="formData.tagencyname" placeholder="" :disabled="editDisabled"></el-input>
									</el-form-item>
								</el-col>
								<el-col :span="6">
									<el-form-item label="收货人" prop="">
										<el-input v-model="formData.consignee" placeholder="" :disabled="editDisabled"></el-input>
									</el-form-item>
								</el-col>
							</el-row>
							<el-row class="order-edit-row">
								<el-col :span="6">
									<el-form-item label="收货电话" prop="">
										<el-input v-model="formData.linkphone" placeholder="" :disabled="editDisabled"></el-input>
									</el-form-item>
								</el-col>
								<el-col :span="12">
									<el-form-item label="收货地址" prop="">
										<el-input v-model="formData.finaladdress" placeholder="" :disabled="editDisabled"></el-input>
									</el-form-item>
								</el-col>
								<el-col :span="6">
									<el-form-item label="汇款人姓名" prop="">
										<el-input v-model="formData.remittername" placeholder="" :disabled="editDisabled"></el-input>
									</el-form-item>
								</el-col>
							</el-row>
							<el-row class="order-edit-row">
								<el-col :span="6">
									<el-form-item label="实际总金额" prop="">
										<el-input v-model="formData.totalamount" placeholder="" :disabled="editDisabled"></el-input>
									</el-form-item>
								</el-col>
								<el-col :span="6">
									<el-form-item label="备    注" prop="">
										<el-input v-model="formData.remark" placeholder="" :disabled="editDisabled"></el-input>
									</el-form-item>
								</el-col>
								<el-col :span="6">
									<el-form-item label="订单类型" prop="">
										<el-input v-model="formData.ordertype" placeholder="" :disabled="editDisabled"></el-input>
									</el-form-item>
								</el-col>
								<el-col :span="6">
									<el-form-item label="促销标题" prop="">
										<el-input v-model="formData.promotiontitle" placeholder="" :disabled="editDisabled"></el-input>
									</el-form-item>
								</el-col>
							</el-row>
							<el-row class="order-edit-row">
								<el-col :span="6">
									<el-form-item label="云仓ID" prop="">
										<el-input v-model="formData.u8id" placeholder="" :disabled="editDisabled"></el-input>
									</el-form-item>
								</el-col>
								<el-col :span="6">
									<el-form-item label="订单状态" prop="">
										<el-input v-model="formData.paystatus" placeholder="" :disabled="editDisabled"></el-input>
									</el-form-item>
								</el-col>
								<el-col :span="6">
									<el-form-item label="运单号" prop="">
										<el-input v-model="formData.waybillcode" placeholder="" :disabled="editDisabled"></el-input>
									</el-form-item>
								</el-col>
								<el-col :span="6" v-if="1 == 0">
									<el-form-item label="" prop="">
										<el-button type="primary" @click="checkWaybill" class="hzpbtn-primary" v-loading="loading" v-if="!!formData.waybillcode">查看物流 ></el-button>
									</el-form-item>
								</el-col>
							</el-row>
						</el-col>
					</el-row>
				</el-collapse-item>
			</el-collapse>
		</el-form>
		<!--列表-->
		<el-table :data="datas" :height="containerHeight - orderHeight" highlight-current-row v-loading="loading" class="data-content" border :row-class-name="tableRowClassName"
		@selection-change="handleSelectionChange">
			<el-table-column :selectable="selectableStatus" type="selection" align="center">
    	</el-table-column>
			<el-table-column label="序号" align="center" type="index" width="80">
			</el-table-column>
			<el-table-column prop="stepname" label="促销类别" align="center" min-width="20%">
			</el-table-column>
			<el-table-column prop="spcode" label="商品编号" align="center" min-width="20%">
			</el-table-column>
			<el-table-column prop="spname" label="商品名称" align="center" min-width="20%">
			</el-table-column>
			<!-- <el-table-column prop="spthumImage" label="图片" align="center" min-width="20%">
				<template scope="scope">
					<el-image style="width: 50px; height: 50px" :src="scope.row.spthumImage" fit="cover" v-show="!!scope.row.spthumImage"></el-image>
				</template>
			</el-table-column> -->
			<el-table-column prop="spccomunitname" label="单位" align="center" min-width="20%">
			</el-table-column>
			<el-table-column prop="spsize" label="数量" align="center" min-width="20%">
			</el-table-column>
			<el-table-column prop="spmoney" label="单价(元)" align="center" min-width="20%">
			</el-table-column>
			<el-table-column prop="spamount" label="合计(元)" :formatter="formatAmount" align="center" min-width="20%">
			</el-table-column>
			<el-table-column prop="zpcode" label="是否赠品" :formatter="formatType" align="center" width="100">
			</el-table-column>
			<el-table-column prop="delivery_status" label="发货状态" :formatter="formatDeliveryStatus" align="center" width="100">
			</el-table-column>
			<el-table-column prop="logis_code" label="运单号" align="center" width="120">
				<template slot-scope="scope">
					<label class="hzpedit" @click="clickWayBill2(scope.row.logis_code)">{{scope.row.logis_code}}</label>
				</template>
			</el-table-column>
			<el-table-column prop="sync_ordercode" label="仓库订单号" align="center" width="120">
			</el-table-column>
		</el-table>
		<!--物流运单号-->
    <el-dialog :title="'运单号'" :visible.sync="wayBillDialogVisible" class="dialog-container" width="700px" @opened="openDialog">
			<section class="wbsec">
				<el-tag v-for="wb in wayBillDatas" v-bind:key="wb" @click="clickWayBill(wb)" type="success" class="wbtag">{{ wb }}&nbsp;<i class="el-icon-aim"></i></el-tag>
			</section>
    </el-dialog>
		<!--物流数据数据-->
      <el-dialog :title="'物流信息'" :visible.sync="logisticsDialogVisible" class="dialog-container" width="700px" height="600px" @opened="openDialog">
				<section style="margin: 10px 0;">
					<label class="billNo">运单号：{{ logisticsBillNo }}</label>
					<label class="billState">{{ logisticsStatuStr(logisticsStatu) }}</label>
					<section class="billContent">
						<ul class="bcul">
							<li v-for="(lo, index) in logisticsDatas" v-bind:key="lo.operatetime"  class="bcli" :style="index == 0? 'color: #E3A53F;' : ''">
								<el-row>
									<el-col :span="1">
										<span class="el-icon-location"></span>
									</el-col>
									<el-col :span="8">
										<span>{{ lo.time }}</span>
									</el-col>
									<el-col :span="24">
										<span v-html="lo.content" class="bccon">
											{{ lo.content }}
										</span>
									</el-col>
								</el-row>
							</li>
						</ul>
						<!-- <el-steps direction="vertical" :active="0">
							<el-step v-for="lo in logisticsDatas" v-bind:key="lo.operatetime" 
								:title="formatTitle(lo.date, lo.time)" :description="lo.content" icon="el-icon-location"
								style="margin-bottom: 10px;"></el-step>
						</el-steps> -->
					</section>
				</section>
			<!-- <el-table :data="logisticsDatas" :stripe="true" :height="400" highlight-current-row v-loading="loading" id="out-table" class="data-content" border row-key="id">
				<el-table-column label="序号" align="center" type="index" width="80">
				</el-table-column>
				<el-table-column prop="date" label="日期" align="center" width="100">
				</el-table-column>
				<el-table-column prop="time" label="时间" align="center" width="80">
				</el-table-column>
				<el-table-column prop="content" label="内容" align="center" min-width="100%">
				</el-table-column>
			</el-table> -->
      </el-dialog>
			<el-dialog title="请选择对应发货商品" :visible.sync="selectSPListDialog" class="dialog-container" width="600px" @opened="openDialog">
				<section style="margin: 10px 0;">
					<div v-for="(item,index) of multipleSelection" :key="index" style="display: flex;text-align: center;align-items: center;margin: 10px;">
						<span>{{item.spname}}：</span>
						<el-select style="width:400px" filterable v-model="item.realInvCode" clearable placeholder="请选择" @change="selectSPChange(index)">
							<el-option
								v-for="item2 in selectSPList"
								:key="item2.code"
								:label="item2.name"
								:value="item2.code">
							</el-option>
						</el-select>
					</div>
					<div slot="footer" class="dialog-footer" style="margin-top:80px">
						<el-button @click="selectSPListDialog=false;selectSPList=[]" class="hzpbtn-close">关 闭</el-button>
						<el-button type="primary" @click="dealPartSync()" v-loading="loading" class="hzpbtn-primary">保 存</el-button>
					</div>
				</section>
			</el-dialog>
			<el-dialog title="撤回订单" :visible.sync="cancleOrderDialog" class="dialog-container" width="600px" @opened="openDialog">
				<section style="margin: 10px 0;">
					<el-table :data="syncOrderCodedatas" :stripe="true" :height="550" highlight-current-row v-loading="loading" id="out-table" class="data-content" border>
						<!-- <el-table-column :selectable="true" type="selection" align="center">
						</el-table-column> -->
						<el-table-column prop="sync_ordercode" label="仓库订单号">
						</el-table-column>
						<el-table-column label="操作" align="center" width="100">
							<template slot-scope="scope">
								<label class="hzpedit" @click.prevent="cancleOrderSync(scope.$index, scope.row)">撤回</label>
							</template>
						</el-table-column>
					</el-table>
				</section>
			</el-dialog>
	</section>
</template>
<script>
	// 订单
	import { getOrderList,queryOrderInventoryDelivery,cancelPayOrder } from '../../api/api';
	import { findwaybill } from '../../api/api';
	// 订单商品
	import { orderDetail } from '../../api/api';
	import { queryWaybill,orderPartSyncWms,getInventoryList } from '../../api/api';
	import util from '../../common/js/util';
	import emEnum from '../../common/js/emEnum';
	// 行列拖拽
	import Sortable from 'sortablejs';

	export default {
		data() {
			return {
				sysCnName: emEnum.cnName,  // 中文名称
				sysEnName: emEnum.enName,  // 英文名称

				loginId: '',   // 登录用户
				oid: '',       // 订单id
				opageSize: 100,
				oPageNum: 1,

				submitSortDisabled: true,  // 提交排序不可用
				idArr: [],        // 编号
				sequenceArr: [],  // 序号

				loading: false,
				containerHeight: 0,
				orderHeight: 343,  // 订单详情高度
				orderColl: '隐藏',  // 订单可变为的状态
				activeName: ['coll'],  // 展开名称
				datas: [],
				orderstep: [],  // 促销步骤及商品集合

				orderDisabled: true,  // 是否不可编辑
				formData: {},

				editDisabled: true,  // 是否不可编辑

				wayBillDialogVisible: false,  // 运单对话框
				wayBillDatas: [],  // 运单数据

				logisticsDialogVisible: false,  // 物流对话框
				logisticsDatas: [],  // 物流数据
				logisticsBillNo: '', // 物流运单号
				logisticsStatu: '',  // 物流状态
				multipleSelection: [],
				selectSPListDialog: false,//选择商品清单
				selectSPList:[],
				cancleOrderDialog: false,//撤回订单
				syncOrderCodedatas:[],
			}
		},
		methods: {
			cancleOrder(){
				this.cancleOrderDialog = true;
			},
			cancleOrderSync(index, row) {
				let _this = this;
				_this.$confirm('确认撤回订单吗，撤回之后将取消云仓订单？', '提示', {
					type: 'warning'
				}).then(() => {
					_this.loading = true;
					let para = {
						ordercode: _this.formData.code,
						sync_code: row.sync_ordercode,
						updateuser: util.userData.getLoginUserFieldValue(emEnum.loginUserFileds.username, this)
					};
					cancelPayOrder(para, _this)
					.then(res=>res.data)
					.then(data => {
						_this.loading = false;
						let { code, msg } = data;
						if (code !== "0") {
							_this.$message({
								message: msg,
								type: 'error'
							});
						} else {
							_this.$message({
								message: msg,
								type: 'success'
							});
							this.cancleOrderDialog = false;
							_this.getData();
						}
					});
				})
			},
			selectSPChange(index){
				let _this = this;
				console.log(index)
				_this.selectSPList.forEach(element => {
					if (element.code == _this.multipleSelection[index].realInvCode) {
						_this.multipleSelection[index].realInvName = element.name;
					}
				});
				_this.$forceUpdate();
			},
			// 选中项改变事件
			handleSelectionChange: function (val) {
				let _this = this;
				_this.multipleSelection = [];
				if(!!val && val.length > 0) {
					val.forEach(a=>{
						_this.multipleSelection.push(a);
					});
				}
			},
			// 合计显示转换
			formatAmount: function (row, column) {
				// 数量
				let size = parseFloat(row.spsize);
				if(size.toString() == 'NaN') return 0;
				// 单价
				let money = parseFloat(row.spmoney);
				if(money.toString() == 'NaN') return 0;
				// 返回合计
				return size * money;
			},
			// 发货状态显示
			formatDeliveryStatus: function (row, column) {
				if(row.delivery_status === '1') return "待发货";
				else if(row.delivery_status === '2') return "已发货";
				else return "";
			},
			selectableStatus(row) {
				if (row.delivery_status) {
					return false;
				}
				return true;
			},
			// 类型显示转换
			formatType: function (row, column) {
				return !!row.zpcode? '赠品' : '';
			},
			// 格式化标题
			formatTitle: function (date, time) {
				return util.formatDate.format(new Date(date), "yyyy-MM-dd") + ' ' + time;
			},
			// 格式化内容
			formatContent: function (content) {
				return content.replace(/&lt;/g, "<").replace(/&gt;/g, ">");
			},
			// 展开事件
			collapseChange: function (val) {
				if(val === "coll") {
					this.orderHeight = 343;
					this.orderColl = "隐藏";
				} else {
					this.orderHeight = 100;
					this.orderColl = "展开";
				}
			},
			// 获取列表
			getData: function () {
				let _this = this;
				_this.datas = [];
				if(!!_this.formData.code) {
					// 执行操作
					_this.loading = true;
					let para = {
						code: _this.formData.code,
					};
					orderDetail(para, _this)
					.then(res=>res.data)
					.then(data => {
						_this.loading = false;
						let { code, msg } = data;
						if (code !== "0") {
							_this.$message({
								message: msg,
								type: 'error'
							});
						} else {
							data = data.data.list;
							if(!!data && data.length > 0) {
								// 处理促销步骤
								let orderstep = data[0].orderstep;
								let index = 1;
								orderstep.forEach(ele => {
									// 序号  商品编号  商品名称  下单步骤  单价  合计  是否赠品
									let bZxg = ele.promotionsteptype === "4";
									// 促销步骤商品
									ele.orderstep.forEach(innerEle => {
										let obj = {
											id: index,
											spcode: innerEle.spcode,
											spname: innerEle.spname,
											stepname: innerEle.orderstepname,
											spsize: innerEle.spsize,
											spmoney: innerEle.spmoney,
											spamount: parseFloat(innerEle.spsize) * parseFloat(innerEle.spmoney),
											spccomunitname: innerEle.spccomunitname,
											zpcode: bZxg ? '' : innerEle.spcode,
											fhId: innerEle.id,
											delivery_status: '',
											logis_code: '',
											sync_ordercode:'',
										};
										_this.datas.push(obj);
										if(!!innerEle.zpinventorylist && innerEle.zpinventorylist.length > 0) {
											innerEle.zpinventorylist.forEach(zpItem => {
												let zpObj = {
													id: index,
													spcode: zpItem.zpcode,
													spname: zpItem.zpname,
													stepname: innerEle.orderstepname,
													spsize: zpItem.zpsize,
													spmoney: zpItem.zpmoney,
													spamount: parseFloat(zpItem.zpsize) * parseFloat(zpItem.zpmoney),
													spccomunitname: zpItem.zpccomunitname,
													zpcode: zpItem.zpcode,
													fhId: zpItem.id,
													delivery_status: '',
													logis_code: '',
													sync_ordercode:'',
												};
												_this.datas.push(zpObj);
											});
										}
										index++;
									});
								});
							}
							_this.queryOrderInventoryDelivery();
						}
					});
				}
			},
			//获取订单发货状态
			queryOrderInventoryDelivery(){
				let _this = this;
				console.log(this.datas)
				let itemList = []
				this.datas.forEach(element => {
					itemList.push(`${element.fhId}`);
				});
				// 执行操作
				_this.loading = true;
				let para = {
					ordercode: _this.formData.code,
					itemList:itemList,
				};
				queryOrderInventoryDelivery(para, _this)
				.then(res=>res.data)
				.then(data => {
					_this.loading = false;
					let { code, msg } = data;
					if (code !== "0") {
						_this.$message({
							message: msg,
							type: 'error'
						});
					} else {
						data = data.data.list;
						if(!!data && data.length > 0) {
							if (data.length == _this.datas.length) {
								for (let index = 0; index < data.length; index++) {
									const element = data[index];
									const element2 = _this.datas[index];
									element2.delivery_status = element.delivery_status;
									element2.logis_code = element.logis_code;
									element2.sync_ordercode = element.sync_ordercode;
								}
							}
						}
					}
				});
			},
			// 查看运单
			checkWaybill: function () {
				let _this = this;
				_this.wayBillDatas = [];
				let noData = true;
				if(!!_this.formData.waybillcode) {
					let codes = _this.formData.waybillcode.split(',');
					if(!!codes && codes.length > 0) {
						noData = false;
						_this.wayBillDatas = codes;
						// 打开对话框
						_this.wayBillDialogVisible = true;
					}
				}
				// 没有可用运单号时提醒
				if(noData) {
					_this.$message({
						message: '没有可用运单号！',
						type: 'error'
					});
				}
			},
			//
			clickWayBill2(wb){
				this.checkSFWaybill(wb,this.formData.code);
			},
			// 查看顺丰物流信息
			checkSFWaybill: function (wayid, ordercode) {
				// 获取物流信息
				let _this = this;
				_this.logisticsDatas = [];
				_this.logisticsBillNo = '';
				_this.logisticsStatu = '';
				if(!!wayid) {
					// 执行操作
					_this.loading = true;
					// {"uid":"1","wid":"DPK364465866292","ordercode":"************","channel":"pc"}
					let para = {
						uid: _this.loginId,
						wid: wayid,
						ordercode: ordercode
					};
					queryWaybill(para, _this)
					.then(res=>res.data)
					.then(data => {
						_this.loading = false;
						let { code, msg } = data;
						if (code !== "0") {
							_this.$message({
								message: msg,
								type: 'error'
							});
						} else {
							if(!!data.data) {
								let result = data.data.logs_info;
								_this.logisticsBillNo = result.no;
								_this.logisticsStatu = result.state;
								if(!!result.list) {
									_this.logisticsDatas = result.list;
								}
							} else {
								_this.logisticsBillNo = wayid;
								_this.logisticsStatu = "暂无物流信息";
							}
							// 打开对话框
							_this.logisticsDialogVisible = true;
						}
					});
				}
			},
			// 订单单击事件
			clickWayBill: function (wb) {
				this.checkLogistics(wb);
			},
			// 查看物流
			checkLogistics: function (wayid) {
				// 获取物流信息
				let _this = this;
				_this.logisticsDatas = [];
				_this.logisticsBillNo = '';
				_this.logisticsStatu = '';
				if(!!wayid) {
					// 执行操作
					_this.loading = true;
					let para = {
						wid: wayid,
					};
					findwaybill(para, _this)
					.then(res=>res.data)
					.then(data => {
						_this.loading = false;
						let { code, msg } = data;
						if (code !== "0") {
							_this.$message({
								message: msg,
								type: 'error'
							});
						} else {
							if(!!data.data && !!data.data.list && !!data.data.list.result) {
								let result = data.data.list.result;
								_this.logisticsBillNo = result.billno;
								_this.logisticsStatu = result.billnostate;
								if(!!result.tracks) {
									_this.logisticsDatas = data.data.list.result.tracks;
								}
							}
							// 打开对话框
							_this.logisticsDialogVisible = true;
						}
					});
				}
			},
			//  物流状态：-1：单号或代码错误；0：暂无轨迹；1:快递收件；2：在途中；3：签收；4：问题件 5.疑难件 6.退件签收 
			logisticsStatuStr(state) {
				switch (`${state}`) {
					case "-1":
						return "单号错误"
					case "0":
						return "暂无轨迹"
					case "1":
						return "快递收件"
					case "2":
						return "在途中"
					case "3":
						return "已签收"
					case "4":
						return "问题件"
					case "5":
						return "疑难件"
					case "6":
						return "退件签收"
					default:
						return "暂无物流信息";
				}
			},
			// 打开编辑对话框
			openDialog: function () {

			},
			// 关闭编辑对话框
			closeDialog: function () {
				this.logisticsDialogVisible = false;
			},
			// 行拖拽
			rowDrop: function () {
				const tbody = document.querySelector('.el-table__body-wrapper tbody');
				const _this = this;
				Sortable.create(tbody, {
					onEnd({ newIndex, oldIndex }) {
						if(_this.sequenceArr.length > 1) {  // 大于1行才有排序的必要
							const currRow = _this.datas.splice(oldIndex, 1)[0];
							_this.datas.splice(newIndex, 0, currRow);
							_this.submitSortDisabled = false;
							// 设置序号
							for(let i = 0; i < _this.datas.length; i++) {
								_this.datas[i].sequence = _this.sequenceArr[i];
							}
						}
					}
				});
			},
			// 提交排序
			submitSort: function () {
				let _this = this;
				// 编号
				_this.idArr = _this.datas.map(item => {
					return item.id;
				});
				// 提交数据
				if(!!_this.idArr && !!_this.sequenceArr && _this.idArr.length === _this.sequenceArr.length) {				
					_this.$confirm('确认提交排序结果？', '提示', {
						type: 'warning'
					}).then(() => {
						// _this.loading = true;
						// let para = {
						// 	bid: _this.idArr,
						// 	sequence: _this.sequenceArr
						// };
						// sortBanner(para, _this)
						// .then(res=>res.data)
						// .then(data => {
						// 	_this.loading = false;
						// 	let { msg, code } = data;
						// 	if (code !== "0") {
						// 		_this.$message({
						// 			message: msg,
						// 			type: 'error'
						// 		});
						// 	} else {
						// 		_this.$message({
						// 			message: msg,
						// 			type: 'success'
						// 		});
						// 		_this.getData();
						// 	}
						// });
					}).catch((error) => {
						if(error!=='cancel') {
							_this.$message({
								message: error.message? error.message : error,
								type: 'error'
							});
						}
					});
				} else {
					_this.$message({
						message: '排序数据出现问题，请刷新后重试！',
						type: 'error'
					});
				}
			},
			// 上一步骤
			preStep: function () {
				this.$router.push({ path: '/order', query: {ps: this.oPageSize, pn: this.oPageNum}});
			},
			// 导出Execl
			exportExcel: function () {
				this.doExport();
			},
			//发货
			fhOrder(){
				let _this = this;
				if (_this.multipleSelection.length == 0) {
					_this.$message({
						message: '请先选择需要发货的商品',
						type: 'error'
					});
					return;
				}
				let ysCount = 0;
				_this.multipleSelection.forEach(element => {
					element.realInvCode = '';
					element.realInvName = '';
					if (element.spcode.endsWith('-YS')) {
						ysCount++;
					}
				});
				console.log(_this.multipleSelection);
				
				if (ysCount==0) {
					_this.$confirm('确认将该订单部分商品发货吗？', '提示', {
						type: 'warning'
					}).then(() => {
						_this.dealPartSync();
					});
				} else {
					if (ysCount == _this.multipleSelection.length) {
						_this.getSPData()
					} else {
						_this.$message({
							message: '预售商品不能与正式商品一起发货，请重新选择发货商品',
							type: 'error'
						});
					}
				}
				
			},
			// 获取预售商品对应列表
			getSPData: function () {
				let _this = this;
				// 执行操作
				_this.loading = true;
				let para = {
					pageNum: 1,
					pageSize: 10000,
				};
				getInventoryList(para, _this)
				.then(res=>res.data)
				.then(data => {
					_this.loading = false;
					let { code, msg } = data;
					if (code !== "0") {
						_this.$message({
							message: msg,
							type: 'error'
						});
					} else {
						_this.selectSPListDialog = true;
						_this.selectSPList = data.data.list;
					}
				});
			},
			//处理预售商品
			// dealYSSync(){
			// 	let _this = this;
			// 	let ysInvs = []
			// 	_this.multipleSelection.forEach(element => {
			// 		ysInvs.push({
			// 			itemId: element.fhId,
			// 			itemCode:  element.spcode,
			// 			itemName:  element.spname,
			// 			realInvCode:  element.realInvCode,
			// 			realInvName:  element.realInvName,
			// 			itemSize:  element.spsize,
			// 			itemPrice: element.spmoney,
			// 			orderstepname:  _this.formData.ordertype === '1'?'活动订单':element.stepname,
			// 			isGift:  element.zpcode.length>0?'1':'0',
			// 		})
			// 	});
			// 	// 执行操作
			// 	_this.loading = true;
			// 	let para = {
			// 		ordercode: _this.formData.code,
			// 		inventorys: ysInvs,
			// 		updateuser: _this.loginId,
			// 	};
			// 	orderPartSyncWms(para, _this)
			// 	.then(res=>res.data)
			// 	.then(data => {
			// 		_this.loading = false;
			// 		let { code, msg } = data;
			// 		if (code !== "0") {
			// 			_this.$message({
			// 				message: msg,
			// 				type: 'error'
			// 			});
			// 		} else {
			// 			_this.queryOrderInventoryDelivery();
			// 		}
			// 	});
			// },
			//处理非预售商品
			dealPartSync(){
				let _this = this;
				let inventorys = []
				_this.multipleSelection.forEach(element => {
					inventorys.push({
						itemId: element.fhId,
						itemCode:  element.spcode,
						itemName:  element.spname,
						realInvCode:  element.realInvCode,
						realInvName:  element.realInvName,
						itemSize:  element.spsize,
						itemPrice: element.spmoney,
						orderstepname:  _this.formData.ordertype === '活动订单'?'活动订单':element.stepname,
						isGift:  element.zpcode.length>0?'1':'0',
					})
				});
				// 执行操作
				_this.loading = true;
				let para = {
					ordercode: _this.formData.code,
					inventorys: inventorys,
					updateuser: _this.loginId,
				};
				orderPartSyncWms(para, _this)
				.then(res=>res.data)
				.then(data => {
					_this.loading = false;
					let { code, msg } = data;
					if (code !== "0") {
						_this.$message({
							message: msg,
							type: 'error'
						});
					} else {
						_this.selectSPListDialog = false;
						_this.getData();
					}
				});
			},
			// 执行导出
			doExport: function () {
				let _this = this;
				// 导出数据
				if(!!_this.formData || (!!_this.datas && _this.datas.length > 0)) {
					// 商品
					let tabData2 = [];
					let i = 1;
					_this.datas.forEach(item => {
						let spData = {
							id: i,
							spcode: item.spcode,
							spname: item.spname,
							stepname: item.stepname,
							spsize: parseFloat(item.spsize),
							spmoney: parseFloat(item.spmoney),
							spamount: parseFloat(_this.formatAmount(item)),
							spccomunitname: item.spccomunitname,
							zpcode: _this.formatType(item),
						};
						tabData2.push(spData);
						i++;
					});
					// 表头和对应字段
					let ths = [];
					let h2 = [ '序号', '促销类别', '商品编号', '商品名称', '单位', '数量', '单价(元)', '合计(元)', '是否赠品' ];
					let f2 = ['id', 'stepname', 'spcode', 'spname', 'spccomunitname', 'spsize', 'spmoney', 'spamount', 'zpcode'];
					ths.push(h2);
					// 订单内容
					let h3 = [ '', '', '', '', '', '', '', '', '' ];
					let h4 = [ '备注', !_this.formData.remark?'':_this.formData.remark, '', '', '', '', '', '', '' ];
					let h5 = [ '收货地址', !_this.formData.finaladdress?'':_this.formData.finaladdress, '', '', '', '', '', '', '' ];
					let h6 = [ '收货人', !_this.formData.consignee?'':_this.formData.consignee, '电话', !_this.formData.linkphone?'':_this.formData.linkphone, '', '', '', '', '' ];
					let h7 = [ '培训公司', !_this.formData.aagencyname?'':_this.formData.aagencyname, '销售公司', !_this.formData.pagencyname?'':_this.formData.pagencyname, '代理1', !_this.formData.cagencyname?'':_this.formData.cagencyname, '', '代理2', !_this.formData.tagencyname?'':_this.formData.tagencyname ];
					let h8 = [ '订单编号', !_this.formData.code?'':_this.formData.code, '', '下单人', !_this.formData.ordercaretername?'':_this.formData.ordercaretername, '', '', '', '' ];
					let h9 = [ '订单日期', !_this.formData.creattime?'':_this.formData.creattime, '', '加盟商', !_this.formData.franchisee?'':_this.formData.franchisee, '', '', '云仓ID', !_this.formData.u8id?'':_this.formData.u8id ];
					// let h10 = [ '', '', '', '', '订单商品详情表', '', '', '', '' ];
					let h10 = [ '', '', '', '订单商品详情表', '', '', '', '', '' ];
					// 把订单内容加入头部
					ths.push(h3);
					ths.push(h4);
					ths.push(h5);
					ths.push(h6);
					ths.push(h7);
					ths.push(h8);
					ths.push(h9);
					ths.push(h10);
					// 获取文件名
					let fileName = _this.sysCnName + "_" + util.formatDate.format(new Date(), "yyyy-MM-dd HH:mm:ss") + "_" + _this.$root.$route.name;
					// 导出
					util.exportJsonToExcelMultTh(fileName, JSON.stringify(tabData2), ths, f2);
				} else {
					this.$message({
						message: "没有需要导出的数据",
						type: 'warning'
					});
				}
			},
			// 获取数据
			getOrderData: function () {
				let _this = this;
				// 执行操作
				_this.loading = true;
				let para = {
					oid: _this.oid,
				};
				getOrderList(para, _this)
				.then(res=>res.data)
				.then(data => {
					_this.loading = false;
					let { code, msg } = data;
					if (code !== "0") {
						_this.$message({
							message: msg,
							type: 'error'
						});
					} else {
						data = data.data.list;
						data = data.find(item => {
							return item.id === _this.oid;
						});
						_this.formData = {...data};
						// 订单类型
						if(_this.formData.ordertype === '1') _this.formData.ordertype = "活动订单";
						else if(_this.formData.ordertype === '2') _this.formData.ordertype = "促销订单";
						else _this.formData.ordertype = "未知订单类型";
						// 订单状态
						if(_this.formData.paystatus === '0') _this.formData.paystatus = "待支付";
						else if(_this.formData.paystatus === '1') _this.formData.paystatus = "已支付";
						else if(_this.formData.paystatus === '2') _this.formData.paystatus = "已发货";
						else if(_this.formData.paystatus === '3') _this.formData.paystatus = "部分发货";
						else _this.formData.paystatus = "";
						console.log(_this.formData)
						_this.syncOrderCodedatas = [];
						let syncOrderCodedatas = _this.formData.sync_ordercode.split(',');
						syncOrderCodedatas.forEach(element => {
							_this.syncOrderCodedatas.push({sync_ordercode:element});
						});
						// 获取对应商品列表
						_this.getData();
					}
				});
			},
			// 表格行Class名称
			tableRowClassName: function ({row, rowIndex}) {
				if (!!row.zpcode) {
					return 'zp-row';
				}
				return '';
			}
		},
		// 创建VUE实例后的钩子
		created: function () {
			// 登录ID
			this.loginId = util.userData.getLoginUserFieldValue(emEnum.loginUserFileds.uid, this);
			// 获取参数值
			let query = this.$route.query;
			this.oid = query.oid;
			this.oPageSize = query.ps;
			this.oPageNum = query.pn;
		},
		// 挂载到DOM后的钩子
		mounted: function () {
			let _this = this;
			_this.containerHeight = _this.$refs.orderViewcontainer.offsetHeight;
			// 获取订单数据
			_this.getOrderData();
			// 阻止默认行为
			document.body.ondrop = function (event) {
				event.preventDefault();
				event.stopPropagation();
			};
			// 行拖拽
			//_this.rowDrop()
			// 绑定窗口尺寸改变事件改变数据表的高度
			window.onresize = () => {
				return (() => {
					// 为了避免频繁触发resize函数导致页面卡顿，使用定时器
					if (!this.timer && !!_this.$refs.orderViewcontainer) {
						this.timer = true
						_this.containerHeight = _this.$refs.orderViewcontainer.offsetHeight;
						let that = this
						setTimeout(function () {
							that.timer = false
						}, 100);
					}
				})();
			}
		}
	};
</script>

<style lang="scss">
.orderView-container {
	height: calc(100vh - 120px);
	.wbsec {
		margin: 30px 20px;
		.wbtag {
			font-size: 18px;
			border-radius: 50px;
			margin: 10px;
			cursor: pointer;
		}
		.wbtag:hover {
			color: #409eff;
		}
	}
	.billNo {
		float: right;
		font-size: 18px;
		margin-right: 30px;
	}
	.billState {
    	display: inline-block;
		font-size: 18px;
    	color: #E3A53F;
		background: #FCE4BD;
    	padding: 5px 20px;
		margin-left: 30px;
		margin-bottom: 10px;
		border-radius: 30px;
	}
	.billContent {
		height: 500px;
		font-size: 15px;
		border-top: 1px solid #c0c4cc;
		overflow-y: auto;
		.bcul {
			list-style: none;
			.bcli {
				margin-bottom: 10px;
				.bccon {
					padding-left: 20px;
					border-left: 1px #c0c4cc dashed;
    				margin-left: 6px;
				}
			}
		}
	}
}
.el-table .zp-row {
	background-color: #f0f9eb !important;
}
.el-collapse-item__content {
	padding-bottom: 5px !important;
}
</style>