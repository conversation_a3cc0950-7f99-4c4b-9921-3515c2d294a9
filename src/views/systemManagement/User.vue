<template>
	<section class="user-container common-container" ref="usercontainer">
		<!--操作-->
		<section class="data-oper">
			<el-button type="primary" @click="editData" class="hzpbtn-primary" v-loading="loading">添加</el-button>
			<!-- <el-button type="primary" @click="exportExcel" class="hzpbtn-primary">导出</el-button> -->
		</section>
		<!--列表-->
		<el-table :data="datas" :stripe="true" :height="containerHeight - 85" highlight-current-row v-loading="loading" id="out-table" class="data-content" border>
			<el-table-column label="序号" align="center" type="index" width="60">
				<template slot-scope="scope">
    				<span>{{(pageNum - 1) * pageSize + scope.$index + 1}}</span>
				</template>
			</el-table-column>
			<el-table-column prop="username" label="用户账号" align="center" min-width="20%">
			</el-table-column>
			<el-table-column prop="role" label="用户角色" :formatter="formatRole" align="center" min-width="20%">
			</el-table-column>
			<el-table-column prop="areaname" label="所属区域" align="center" min-width="20%">
			</el-table-column>
			<el-table-column prop="creattime" label="创建时间" :formatter="formatCreatTime" align="center" min-width="20%">
			</el-table-column>
			<el-table-column prop="wxaccount" label="微信手机号" align="center" min-width="20%">
			</el-table-column>
			<el-table-column prop="remark" label="备注" align="center" min-width="20%">
			</el-table-column>
			<el-table-column label="操作" align="center" width="130">
				<template slot-scope="scope">
					<label class="hzpedit" @click.prevent="editData(scope.$index, scope.row)">编辑</label>|
					<el-dropdown trigger="hover" style="line-height: 0;">
						<label class="edit-content edit-more">更多</label>
						<el-dropdown-menu slot="dropdown" class="edit-more-content">
							<el-dropdown-item @click.native.prevent="enableData(scope.$index, scope.row)" v-if="scope.row.role !== 1">{{scope.row.status===0? '禁用' : '启用'}}</el-dropdown-item>
							<el-dropdown-item @click.native.prevent="delData(scope.$index, scope.row)" v-if="scope.row.role !== 1">删除</el-dropdown-item>
							<el-dropdown-item @click.native.prevent="resetPassword(scope.$index, scope.row)">重置密码</el-dropdown-item>
						</el-dropdown-menu>
					</el-dropdown>
				</template>
			</el-table-column>
		</el-table>
		<!--分页-->
		<pagination :pageSize="pageSize" :pageNum="pageNum" :totalNum="totalNum" 
			@handlePageSizeChange="handlePageSizeChange" @handlePageCurrentChange="handlePageCurrentChange">
		</pagination>
		<!--编辑数据-->
        <el-dialog :title="dialogTitle + '数据'" :visible.sync="editDialogVisible" class="dialog-container" width="500px" @opened="openDialog">
			<el-form :model="formData" :rules="rules" ref="formData" label-width="100px" class="form-data" size="mini">
				<el-form-item label="用户账号" prop="username">
					<el-input v-model="formData.username" placeholder="请输入" id="focus-ele"></el-input>
				</el-form-item>
				<el-form-item label="用户角色" prop="role">
					<el-select v-model="formData.role" placeholder="请选择" @change="roleChange" :disabled="roleDisabled">
						<el-option
							v-for="item in roleOptions"
							:key="item.key"
							:label="item.value"
							:value="item.key">
						</el-option>
					</el-select>
				</el-form-item>
				<el-form-item label="所属区域" prop="areaid">
					<el-select v-model="formData.areaid" multiple value-key="id" placeholder="请选择" :disabled="areaDisabled">
						<el-option
							v-for="item in areaDatas"
							:key="item.id"
							:label="item.areaname"
							:value="item.id">
						</el-option>
					</el-select>
				</el-form-item>
				<el-form-item label="微信手机号" prop="wxaccount">
					<el-input v-model="formData.wxaccount" placeholder="请输入" :disabled="areaDisabled"></el-input>
				</el-form-item>
				<el-form-item label="备       注" prop="">
					<el-input v-model="formData.remark" placeholder="请输入" type="textarea" :rows="4"></el-input>
				</el-form-item>
        	</el-form>
            <div slot="footer" class="dialog-footer">
              	<el-button @click="closeDialog" class="hzpbtn-close">关 闭</el-button>
                <el-button type="primary" @click="handleSubmit('formData')" v-loading="loading" class="hzpbtn-primary">保 存</el-button>
            </div>
        </el-dialog>
	</section>
</template>
<script>
	import { getAreaConfig } from '../../api/api';
	import { getUserList } from '../../api/api';
	import { addUser } from '../../api/api';
	import { editUser } from '../../api/api';
	import { enableUser } from '../../api/api';
	import { deleteUser } from '../../api/api';
  	import { resetUserPass } from '../../api/api';
	import util from '../../common/js/util';
	import emEnum from '../../common/js/emEnum';
	// 分页组件
	import Pagination from '../Pagination';

	export default {
		components: {
			Pagination
		},
		data() {
			return {
				sysCnName: emEnum.cnName,  // 中文名称
				sysEnName: emEnum.enName,  // 英文名称

				loginId: '',   // 登录用户

				loading: false,
				containerHeight: 0,
				datas: [],
				// 分页
				pageSize: 100,
				pageNum: 1,
        		totalNum: 0,

				areaDatas: [],  // 区域数据
				roleOptions: emEnum.roleOptions, // 角色集合
				dialogTitle: '',   // 对话框标题 
				editDialogVisible: false,  // 编辑对话框
				roleDisabled: false,  // 角色不可编辑
				areaDisabled: false,  // 区域、微信手机号不可编辑
				formData: {
					id: '',        // 选中用户ID
					username: '',  // 用户账号
					role: '',      // 权限 '1：超管  2：区域管理员   3：订单管理员',
					areaid: '',    // 区域id  如果是超级管理员 不需要传该参数
					wxaccount: '', // 微信手机号
					remark: ''     // 备注
				},
				rules: {
					username: [
						{ required: true, message: '请输入用户账号', trigger: 'blur' }
					],
					role: [
						{ required: true, message: '请选择用户角色', trigger: 'change' }
					],
					areaid: [
						{ required: true, message: '请选择所属区域', trigger: 'change' }
					]
				}
			}
		},
		methods: {
			// 用户角色显示转换
			formatRole: function (row, column) {
				let roleOp = emEnum.roleOptions.find(item=>{
					return item.key === row.role;
				});
				if(!roleOp) return '';
				else return roleOp.value;
			},
			// 时间显示格式转换
			formatCreatTime: function (row, column) {
				if(!!row.creattime) {
					return util.formatDate.format(new Date(row.creattime.replace(/-/g, '/')), 'yyyy-MM-dd HH:mm:ss');
				} else return row.creattime;
			},
			// 获取列表
			getData: function () {
				let _this = this;
				// 执行操作
				_this.loading = true;
				_this.closeDialog();  // 关闭编辑对话框
				let para = {
					pageNum: _this.pageNum,
					pageSize: _this.pageSize
				};
				getUserList(para, _this)
				.then(res=>res.data)
				.then(data => {
					_this.loading = false;
					let { code, msg } = data;
					if (code !== "0") {
						_this.$message({
							message: msg,
							type: 'error'
						});
					} else {
						let { pageSize, pageNum, totalNum } = data.data;
						data = data.data.list;
						_this.datas = data;
						// 分页
						_this.pageSize = pageSize;
						_this.pageNum = pageNum;
						_this.totalNum = totalNum;
					}
				});
			},
			// 删除数据
			delData: function (index, row) {
				let _this = this;
				_this.$confirm('确认删除该用户？', '提示', {
					type: 'warning'
				}).then(() => {
					_this.loading = true;
					let para = {
						uid: row.id
					};
					deleteUser(para, _this)
					.then(res=>res.data)
					.then(data => {
						_this.loading = false;
						let { msg, code } = data;
						if (code !== "0") {
							_this.$message({
								message: msg,
								type: 'error'
							});
						} else {
							_this.$message({
								message: msg,
								type: 'success'
							});
							_this.getData();
						}
					});
				}).catch((error) => {
					if(error!=='cancel') {
						_this.$message({
							message: error.message? error.message : error,
							type: 'error'
						});
					}
				});
			},
			// 修改数据状态（禁用、启用）
			enableData: function (index, row) {
				let _this = this;
				_this.$confirm('确认' + (row.status === 0? '禁用' : '启用') + '该用户？', '提示', {
					type: 'warning'
				}).then(() => {
					_this.loading = true;
					let para = {
						uid: row.id,
						status: row.status === 0? '1' : '0'
					};
					enableUser(para, _this)
					.then(res=>res.data)
					.then(data => {
						_this.loading = false;
						let { msg, code } = data;
						if (code !== "0") {
							_this.$message({
								message: msg,
								type: 'error'
							});
						} else {
							_this.$message({
								message: msg,
								type: 'success'
							});
							_this.getData();
						}
					});
				}).catch((error) => {
					if(error!=='cancel') {
						_this.$message({
							message: error.message? error.message : error,
							type: 'error'
						});
					}
				});
			},
			// 重置密码
			resetPassword: function (index, row) {
				let _this = this;
				_this.$confirm('确认将【' + row.username + '】的密码重置为初始密码？', '提示', {
					type: 'warning'
				}).then(() => {
					_this.loading = true;
					let para = {
						uid: row.id
					};
					resetUserPass(para, _this)
					.then(res=>res.data)
					.then(data => {
						_this.loading = false;
						let { msg, code } = data;
						if (code !== "0") {
							_this.$message({
								message: msg,
								type: 'error'
							});
						} else {
							_this.$message({
								message: msg,
								type: 'success'
							});
						}
					});
				}).catch((error) => {
					if(error!=='cancel') {
						_this.$message({
							message: error.message? error.message : error,
							type: 'error'
						});
					}
				});
			},
			// 重置输入
			resetForm: function (formName) {
				this.$refs[formName].resetFields();
			},
			// 编辑数据
			editData: function (index, row) {
				let formName = 'formData';
				// 打开对话框
				this.editDialogVisible = true;
				this.formData.areaid = [];
				// 默认值
				if(!row) {
					this.dialogTitle = "添加";
					if(this.$refs[formName]) this.$refs[formName].resetFields();
					this.formData.id = "";
					this.formData.username = "";
					// 角色默认选中第一个
					this.formData.role = this.roleOptions[0].key;
					this.roleDisabled = false;
					this.formData.areaid = [];
					// 区域默认选中第一个
					// if(!!this.areaDatas && this.areaDatas.length > 0) {
					// 	this.formData.areaid.push(this.areaDatas[0].id + '');
					// }
					this.formData.wxaccount = "";
					this.formData.remark = "";
				} else {
					this.dialogTitle = "编辑";
					this.formData.id = row.id;
					this.formData.username = row.username;
					// 角色
					let roleOp = emEnum.roleOptions.find(item=>{
						return item.key === row.role;
					});
					if(!roleOp) {
						// 角色默认选中第一个
						this.formData.role = this.roleOptions[0].key;
						this.roleDisabled = false;
					} else {
						this.formData.role = row.role;
						this.roleDisabled = row.role === 1;
					}
					// 选中的区域
					this.formData.areaid = !!row.areaid? row.areaid.split(',') : [];
					this.formData.wxaccount = row.wxaccount;
					this.formData.remark = row.remark;
					
					// 校验输入
					if(this.$refs[formName]) this.$refs[formName].validate();
				}
				// 用户角色改变事件修改区域可用状态
				this.roleChange(this.formData.areaid);
			},
			// 打开编辑对话框
			openDialog: function () {
				// 焦点放到获取焦点的输入框
				let ele = document.getElementById('focus-ele');
				if(!!ele) {
					ele.focus();
				}
			},
			// 关闭编辑对话框
			closeDialog: function () {
				this.editDialogVisible = false;
			},
			// 提交数据
			handleSubmit: function (formName) {
				this.$refs[formName].validate((valid) => {
          			if (valid) {
						let _this = this;
						_this.$confirm('确认' + _this.dialogTitle + '用户？', '提示', {
							type: 'warning'
						}).then(() => {
							_this.loading = true;
							let selAreaid = '';
							if(!!_this.formData.areaid) {
								selAreaid = _this.formData.areaid.join(',');
							}
							if(!_this.formData.id) {  // 添加
								let para = {
									username: _this.formData.username,
									role: _this.formData.role,
									wxaccount: _this.formData.wxaccount,
									remark: _this.formData.remark,
								};
								// 如果选中区域则则传
								if(!!selAreaid) para['areaid'] = selAreaid;

								addUser(para, _this)
								.then(res=>res.data)
								.then(data => {
									_this.loading = false;
									let { msg, code } = data;
									if (code !== "0") {
										_this.$message({
											message: msg,
											type: 'error'
										});
									} else {
										_this.$message({
											message: msg,
											type: 'success'
										});
										_this.getData();
									}
								});
							} else {  // 编辑数据
								let para = {
									uid: _this.formData.id,
									username: _this.formData.username,
									role: _this.formData.role,
									areaid: selAreaid,
									wxaccount: _this.formData.wxaccount,
									remark: _this.formData.remark,
								};
								editUser(para, _this)
								.then(res=>res.data)
								.then(data => {
									_this.loading = false;
									let { msg, code } = data;
									if (code !== "0") {
										_this.$message({
											message: msg,
											type: 'error'
										});
									} else {
										_this.$message({
											message: msg,
											type: 'success'
										});
										_this.getData();
									}
								});
							}
						}).catch((error) => {
							if(error!=='cancel') {
								_this.$message({
									message: error.message? error.message : error,
									type: 'error'
								});
							}
						});
					} else {
						return false;
					}
				});
			},
			// 导出Execl
			exportExcel: function () {
				let _this = this;
				// 执行操作
				_this.loading = true;
				_this.closeDialog();  // 关闭编辑对话框
				let para = {
					pageNum: 1,
					pageSize: _this.totalNum
				};
				// 获取数据
				getUserList(para, _this)
				.then(res=>res.data)
				.then(data => {
					_this.loading = false;
					let { code, msg } = data;
					if (code !== "0") {
						_this.$message({
							message: msg,
							type: 'error'
						});
					} else {
						data = data.data.list;
						_this.doExport(data);
					}
				});
			},
			// 执行导出
			doExport: function (tabData) {
				let _this = this;
				// 导出数据
				if(!!tabData && tabData.length > 0) {
					let i = 1;
					tabData.forEach(item => {
						item.id = i;
						item.role = _this.formatRole(item);
						item.creattime = _this.formatCreatTime(item);
						i++;
					});
					// 获取文件名
					let fileName = _this.sysCnName + "_" + util.formatDate.format(new Date(), "yyyy-MM-dd HH:mm:ss") + "_" + _this.$root.$route.name;
					// 表头和对应字段
					let tHeader = [ '序号', '用户账号', '用户角色', '所属区域', '创建时间', '备注' ];
					let filterVal = [ 'id', 'username', 'role', 'areaname', 'creattime', 'remark' ];
					util.exportJsonToExcel(fileName, JSON.stringify(tabData), tHeader, filterVal);
				} else {
					this.$message({
						message: "没有需要导出的数据",
						type: 'warning'
					});
				}
			},
			// 用户角色改变
			roleChange: function (areas) {
				if(this.formData.role === 1 || this.formData.role === 3 || this.formData.role === 4) {
					this.formData.areaid = [];
					this.areaDisabled = true;
					this.rules.areaid[0].required = false;
					this.formData.wxaccount = "";
				} else {
					// 区域默认选中第一个
					this.areaDisabled = false;
					this.rules.areaid[0].required = true;
				}
			},
			// 获取区域
			getAllAreas: function () {
				let _this = this;
				_this.loading = true;
				let para = {};
				getAreaConfig(para, _this)
				.then(res=>res.data)
				.then(data => {
					let { code, msg } = data;
					if (code !== "0") {
						_this.$message({
							message: msg,
							type: 'error'
						});
					} else {
						data = data.data.list;
						_this.areaDatas = data.filter(item => item.status === 0);
						_this.loading = false;
					}
				});
			},
			// 分页事件
			handlePageSizeChange(val) {
				this.pageSize = val;
				this.getData();
			},
			handlePageCurrentChange(val) {
				this.pageNum = val;
				this.getData();
			}
		},
		// 创建VUE实例后的钩子
		created: function () {
			// 登录ID
			this.loginId = util.userData.getLoginUserFieldValue(emEnum.loginUserFileds.uid, this);
			// 获取区域
			this.getAllAreas();
		},
		// 挂载到DOM后的钩子
		mounted: function () {
			let _this = this;
			_this.containerHeight = _this.$refs.usercontainer.offsetHeight;
			// 获取用户
			_this.getData();
			// 绑定窗口尺寸改变事件改变数据表的高度
			window.onresize = () => {
				return (() => {
					// 为了避免频繁触发resize函数导致页面卡顿，使用定时器
					if (!this.timer && !!_this.$refs.usercontainer) {
						this.timer = true
						_this.containerHeight = _this.$refs.usercontainer.offsetHeight;
						let that = this
						setTimeout(function () {
							that.timer = false
						}, 100);
					}
				})();
			}
		}
	};
</script>

<style lang="scss">
.user-container {
	height: calc(100vh - 120px);
	.edit-content {
		color: #3ca2ff;
		margin-left: 10px;
		margin-right: 10px;
		cursor: pointer;
	}
	.edit-more::after {
		// content: ' ﹀';
		font-size: 12px;
		height: 12px;
		line-height: 12px;
		vertical-align: baseline;
	}
	.edit-more:hover::after {
		// content: ' ︿';
		font-size: 12px;
		height: 12px;
		line-height: 12px;
		vertical-align: text-top;
	}
}
</style>