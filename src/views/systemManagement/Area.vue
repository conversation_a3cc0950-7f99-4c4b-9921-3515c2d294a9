<template>
	<section class="area-container common-container" ref="areacontainer">
		<!--操作-->
		<section class="data-oper">
			<el-button type="primary" @click="editData" class="hzpbtn-primary" v-loading="loading">添加</el-button>
			<!-- <el-button type="primary" @click="exportExcel" class="hzpbtn-primary">导出</el-button> -->
		</section>
		<!--列表-->
		<el-table :data="datas" :stripe="true" :height="containerHeight - 55" highlight-current-row v-loading="loading" id="out-table" class="data-content" border>
			<el-table-column label="序号" align="center" type="index" width="60">
			</el-table-column>
			<el-table-column prop="areaname" label="团队名" align="center" min-width="50%">
			</el-table-column>
			<el-table-column prop="isinternal" label="是否内部员工" :formatter="formatIsinternal" align="center" min-width="25%">
			</el-table-column>
			<el-table-column prop="creattime" label="创建时间" :formatter="formatCreatTime" align="center" min-width="50%">
			</el-table-column>
			<el-table-column label="操作" align="center" width="300">
				<template slot-scope="scope">
					<label class="hzpedit" @click.prevent="delData(scope.$index, scope.row)">删除</label>|
					<label class="hzpedit" @click.prevent="enableData(scope.$index, scope.row)">{{scope.row.status===0? '禁用' : '启用'}}</label>|
					<label class="hzpedit" @click.prevent="editData(scope.$index, scope.row)">编辑</label>|
					<label class="hzpedit" @click.prevent="searchData(scope.$index, scope.row)">查找客户</label>
				</template>
			</el-table-column>
		</el-table>
		<!--分页-->
		<!-- <pagination :pageSize="pageSize" :pageNum="pageNum" :totalNum="totalNum" 
			@handlePageSizeChange="handlePageSizeChange" @handlePageCurrentChange="handlePageCurrentChange">
		</pagination> -->
		<!--编辑数据-->
        <el-dialog :title="dialogTitle + '数据'" :visible.sync="editDialogVisible" class="dialog-container" width="500px" @opened="openDialog">
			<el-form :model="formData" :rules="rules" ref="formData" label-width="120px" class="form-data" size="mini">
				<el-form-item label="团队名" prop="areaName">
					<el-input v-model="formData.areaName" placeholder="请输入" id="focus-ele"></el-input>
				</el-form-item>
				<el-form-item label="是否内部员工" prop="isinternal">
					<template>
						<el-radio v-model="formData.isinternal" label="yes" style="margin-left: 70px;">是</el-radio>
						<el-radio v-model="formData.isinternal" label="no">否</el-radio>
					</template>
				</el-form-item>
        	</el-form>
            <div slot="footer" class="dialog-footer">
              	<el-button @click="closeDialog" class="hzpbtn-close">关 闭</el-button>
                <el-button type="primary" @click="handleSubmit('formData')" v-loading="loading" class="hzpbtn-primary">保 存</el-button>
            </div>
        </el-dialog>
	</section>
</template>
<script>
	import { getAreaConfig } from '../../api/api';
	import { addArea } from '../../api/api';
	import { editArea } from '../../api/api';
	import { enableArea } from '../../api/api';
	import { deleteArea } from '../../api/api';
	import util from '../../common/js/util';
	import emEnum from '../../common/js/emEnum';
	// 分页组件
	import Pagination from '../Pagination';

	export default {
		components: {
			// Pagination
		},
		data() {
			return {
				sysCnName: emEnum.cnName,  // 中文名称
				sysEnName: emEnum.enName,  // 英文名称

				loading: false,
				containerHeight: 0,
				datas: [],
				// 分页
				// pageSize: 100,
				// pageNum: 1,
        		// totalNum: 0,

				dialogTitle: '',   // 对话框标题 
				editDialogVisible: false,  // 编辑对话框
				formData: {
					areaId: '',    // 区域ID
					areaName: '',  // 区域名称
					isinternal: 'no',// 内部员工
				},
				rules: {
					areaName: [
						{ required: true, message: '请输入区域名称', trigger: 'blur' }
					],
					isinternal: [
						{ required: true, message: '请选择是否内部员工', trigger: 'blur,change' }
					]
				}
			}
		},
		methods: {
			// 时间显示格式转换
			formatCreatTime: function (row, column) {
				if(!!row.creattime) {
					return util.formatDate.format(new Date(row.creattime.replace(/-/g, '/')), 'yyyy-MM-dd HH:mm:ss');
				} else return row.creattime;
			},
			// 是否内部员工
			formatIsinternal: function (row, column) {
				if(row.isinternal === 'yes') return "是";
				else if(row.isinternal === 'no') return "否";
				else return '';
			},
			// 获取用户列表
			getData: function () {
				let _this = this;
				// 执行操作
				_this.loading = true;
				_this.closeDialog();  // 关闭编辑对话框
				let para = {
					// pageNum: _this.pageNum,
					// pageSize: _this.pageSize
				};
				getAreaConfig(para, _this)
				.then(res=>res.data)
				.then(data => {
					_this.loading = false;
					let { code, msg } = data;
					if (code !== "0") {
						_this.$message({
							message: msg,
							type: 'error'
						});
					} else {
						// let { pageSize, pageNum, totalNum } = data.data;
						data = data.data.list;
						_this.datas = data;  // .filter(item => item.status === 0);
						// 分页
						// _this.pageSize = pageSize;
						// _this.pageNum = pageNum;
						// _this.totalNum = totalNum;
					}
				});
			},
			// 删除数据
			delData: function (index, row) {
				let _this = this;
				_this.$confirm('确认删除该区域？', '提示', {
					type: 'warning'
				}).then(() => {
					_this.loading = true;
					let para = {
						aid: row.id
					};
					deleteArea(para, _this)
					.then(res=>res.data)
					.then(data => {
						_this.loading = false;
						let { msg, code } = data;
						if (code !== "0") {
							_this.$message({
								message: msg,
								type: 'error'
							});
						} else {
							_this.$message({
								message: msg,
								type: 'success'
							});
							_this.getData();
						}
					});
				}).catch((error) => {
					if(error!=='cancel') {
						_this.$message({
							message: error.message? error.message : error,
							type: 'error'
						});
					}
				});
			},
			// 修改数据状态（禁用、启用）
			enableData: function (index, row) {
				let _this = this;
				_this.$confirm('确认' + (row.status === 0? '禁用' : '启用') + '该区域？', '提示', {
					type: 'warning'
				}).then(() => {
					_this.loading = true;
					let para = {
						aid: row.id,
						status: row.status === 0? '1' : '0'
					};
					enableArea(para, _this)
					.then(res=>res.data)
					.then(data => {
						_this.loading = false;
						let { msg, code } = data;
						if (code !== "0") {
							_this.$message({
								message: msg,
								type: 'error'
							});
						} else {
							_this.$message({
								message: msg,
								type: 'success'
							});
							_this.getData();
						}
					});
				}).catch((error) => {
					if(error!=='cancel') {
						_this.$message({
							message: error.message? error.message : error,
							type: 'error'
						});
					}
				});
			},
			// 重置输入
			resetForm: function (formName) {
				this.$refs[formName].resetFields();
			},
			// 编辑数据
			editData: function (index, row) {
				let formName = 'formData';
				// 打开对话框
				this.editDialogVisible = true;
				// 默认值
				if(!row) {
					this.dialogTitle = "添加";
					this.formData.areaId = "";
					this.formData.areaName = "";
					this.formData.isinternal = "no";
				} else {
					this.dialogTitle = "编辑";
					this.formData.areaId = row.id;
					this.formData.areaName = row.areaname;
					this.formData.isinternal = row.isinternal === "yes" ? "yes" : "no";
					// 校验输入
					if(this.$refs[formName]) this.$refs[formName].validate();
				}
			},
			// 打开编辑对话框
			openDialog: function () {
				// 焦点放到获取焦点的输入框
				let ele = document.getElementById('focus-ele');
				if(!!ele) {
					ele.focus();
				}
			},
			// 关闭编辑对话框
			closeDialog: function () {
				this.editDialogVisible = false;
			},
			// 提交排课
			handleSubmit: function (formName) {
				this.$refs[formName].validate((valid) => {
          			if (valid) {
						let _this = this;
						_this.$confirm('确认' + _this.dialogTitle + '区域？', '提示', {
							type: 'warning'
						}).then(() => {
							_this.loading = true;
							if(!_this.formData.areaId) {  // 添加
								let para = {
									areaname: _this.formData.areaName,
									isinternal: _this.formData.isinternal,
								};
								addArea(para, _this)
								.then(res=>res.data)
								.then(data => {
									_this.loading = false;
									let { msg, code } = data;
									if (code !== "0") {
										_this.$message({
											message: msg,
											type: 'error'
										});
									} else {
										_this.$message({
											message: msg,
											type: 'success'
										});
										_this.getData();
									}
								});
							} else {  // 编辑数据
								let para = {
									aid: _this.formData.areaId,
									areaname: _this.formData.areaName,
									isinternal: _this.formData.isinternal,
								};
								editArea(para, _this)
								.then(res=>res.data)
								.then(data => {
									_this.loading = false;
									let { msg, code } = data;
									if (code !== "0") {
										_this.$message({
											message: msg,
											type: 'error'
										});
									} else {
										_this.$message({
											message: msg,
											type: 'success'
										});
										_this.getData();
									}
								});
							}
						}).catch((error) => {
							if(error!=='cancel') {
								_this.$message({
									message: error.message? error.message : error,
									type: 'error'
								});
							}
						});
					} else {
						return false;
					}
				});
			},
			// 查找
			searchData: function (index, row) {
				this.$router.push({ path: '/client', query: {id: row.id}});
			},
			// 导出Execl
			exportExcel: function () {
				let _this = this;
				// 执行操作
				_this.loading = true;
				_this.closeDialog();  // 关闭编辑对话框
				let para = {};
				getAreaConfig(para, _this)
				.then(res=>res.data)
				.then(data => {
					_this.loading = false;
					let { code, msg } = data;
					if (code !== "0") {
						_this.$message({
							message: msg,
							type: 'error'
						});
					} else {
						data = data.data.list;
						_this.doExport(data);
					}
				});
			},
			// 执行导出
			doExport: function (tabData) {
				let _this = this;
				// 导出数据
				if(!!tabData && tabData.length > 0) {
					let i = 1;
					tabData.forEach(item => {
						item.id = i;
						item.creattime = _this.formatCreatTime(item);
						i++;
					});
					// 获取文件名
					let fileName = _this.sysCnName + "_" + util.formatDate.format(new Date(), "yyyy-MM-dd HH:mm:ss") + "_" + _this.$root.$route.name;
					// 表头和对应字段
					let tHeader = [ '序号', '团队名', '创建时间' ];
					let filterVal = [ 'id', 'areaname', 'creattime' ];
					util.exportJsonToExcel(fileName, JSON.stringify(tabData), tHeader, filterVal);
				} else {
					this.$message({
						message: "没有需要导出的数据",
						type: 'warning'
					});
				}
			},
			// 分页事件
			// handlePageSizeChange(val) {
			// 	this.pageSize = val;
			// 	this.getData();
			// },
			// handlePageCurrentChange(val) {
			// 	this.pageNum = val;
			// 	this.getData();
			// }
		},
		// 挂载到DOM后的钩子
		mounted: function () {
			let _this = this;
			_this.containerHeight = _this.$refs.areacontainer.offsetHeight;
			// 获取用户
			_this.getData();
			// 绑定窗口尺寸改变事件改变数据表的高度
			window.onresize = () => {
				return (() => {
					// 为了避免频繁触发resize函数导致页面卡顿，使用定时器
					if (!this.timer && !!_this.$refs.areacontainer) {
						this.timer = true
						_this.containerHeight = _this.$refs.areacontainer.offsetHeight;
						let that = this
						setTimeout(function () {
							that.timer = false
						}, 100);
					}
				})();
			}
		}
	};
</script>

<style lang="scss">
.area-container {
	height: calc(100vh - 120px);
}
</style>