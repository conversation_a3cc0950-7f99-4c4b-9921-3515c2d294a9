<template>
    <section class="form-container">
        <el-form :model="formData" :rules="rules" ref="formData" label-width="100px" class="form-data" size="mini" v-loading="loading">
            <el-switch style="display: block; left: 240px;" v-model="plaintextShow" active-text="密码明文显示"></el-switch>
            <el-form-item label="  原密码" prop="oldPwd">
                <el-input v-model="formData.oldPwd" placeholder="请输入原密码" :type="plaintextShow?'text':'password'" id="focus-ele"></el-input>
            </el-form-item>
            <el-form-item label="  新密码" prop="newPwd">
                <el-input v-model="formData.newPwd" placeholder="请输入新密码" :type="plaintextShow?'text':'password'"></el-input>
            </el-form-item>
            <el-form-item label="确认密码" prop="confirmPwd">
                <el-input v-model="formData.confirmPwd" placeholder="请再次输入新密码" :type="plaintextShow?'text':'password'"></el-input>
            </el-form-item>
            <el-form-item>
                <el-button type="primary" @click="submitForm('formData')"  style="margin-top: 30px; margin-left: -30px; width: 100px;">确认</el-button>
                <el-button @click="returnManagePage" style="margin-top: 30px; margin-left: 50px; width: 100px;">返回</el-button>
            </el-form-item>
            <el-link type="danger" class="tip-content">{{tipContent}}</el-link>
        </el-form>
    </section>
</template>

<script>
  import { changePassword } from '../../api/api';
	import emEnum from '../../common/js/emEnum';
	import util from '../../common/js/util';
  
  export default {
    data() {
      // 校验确认密码
      var checkConfirmPwd = (rule, value, callback) => {
        if (!value) {
          return callback(new Error('请输入确认密码'));
        } else if (value !== this.formData.newPwd) {
          return callback(new Error('两次密码输入不一致'));
        } else {
          callback();
        }
      };
      return {
        uid: '',  // 用户id
        loading: false,
        tipContent: '',  // 提示内容，初始密码登录时用
        plaintextShow: false, // 是否明文显示
        formData: {
          oldPwd: '',
          newPwd: '',
          confirmPwd: ''
        },
        rules: {
          oldPwd: [
            { required: true, message: '请输入原密码', trigger: 'blur' },
            // { min: 6, message: '长度必须大于等于 6 个字符', trigger: 'blur' }
          ],
          newPwd: [
            { required: true, message: '请输入新密码', trigger: 'blur' },
            { min: 6, message: '长度必须大于等于 6 个字符', trigger: 'blur' }
          ],
          confirmPwd: [
            { required: true, validator: checkConfirmPwd, trigger: 'blur'}
          ]
        }
      };
    },
    methods: {
      // 保存数据
      submitForm(formName) {
        this.$refs[formName].validate((valid) => {
          if (valid) {
            let _this = this;
            _this.loading = true;
            let para = {
              uid: _this.uid,
              oldPassword: _this.formData.oldPwd,
              newPassword: _this.formData.newPwd
            };
            changePassword(para, _this)
            .then(res=>res.data)
            .then(data => {
              _this.loading = false;
              let { code, msg } = data;
              if (code !== "0") {
                _this.$message({
                  message: msg,
                  type: 'error'
                });
                // 焦点放到出错时获取焦点的输入框
                let ele = document.getElementById('focus-ele');
                if(!!ele) {
                  ele.focus();
                }
              } else {
                util.userData.reLogin(_this, true, msg + '，请重新登录！');  // 重新登录
              }
            });
          } else {
            return false;
          }
        });
      },
      // 返回管理页
      returnManagePage: function(e, val) {
        this.$root.$router.back(-1);
      }
    },
    // 创建VUE实例后的钩子
		created: function() {
      
    },
    // 挂载到DOM后的钩子
    mounted() {
      // 焦点放到要获取焦点的输入框
      let ele = document.getElementById('focus-ele');
      if(!!ele) {
        ele.focus();
      }
      // 获取用户id
      this.uid = util.userData.getLoginUserFieldValue(emEnum.loginUserFileds.uid, this);
      // 获取用户密码类型
      let pwdOriginal = util.userData.getLoginUserFieldValue(emEnum.loginUserFileds.pwdOriginal, this);
      // 如果是初始密码登录，则提醒尽快修改密码
      if(!!pwdOriginal && pwdOriginal == 1) {
        this.tipContent = "警告：您的登录密码为初始密码，为了账号安全，请尽快修改密码！";
      }
    }
  }
</script>
<style lang="scss" scoped>
.form-container {
    width: 100%;
    height: calc(100vh - 100px);
	  overflow-y: auto;
	  background: #fff;
    white-space: pre-wrap;
    .form-data {
        width: 380px;
        margin-left: 10%;
        padding-top: 5vh;
    }
    .tip-content {
        width: 500px;
        font-size: 16px;
        font-weight: bold;
        margin-top: 30px;
        margin-left: -30px;
    }
}
</style>
