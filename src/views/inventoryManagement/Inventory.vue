<template>
	<section class="inventory-container common-container" ref="inventorycontainer">
		<!--操作-->
		<section class="data-oper">
			<el-input placeholder="快速搜索" clearable v-model="vagueContent" style="float: right; width: 300px;" @change="searchList" @keyup.native="searchList">
				<el-button slot="append" icon="el-icon-search"></el-button>
			</el-input>
			<div style="float: right; width: 300px; margin-right: 20px;">
				<el-select v-model="inventoryStatus" clearable placeholder="请选择禁用、启用状态" @change="classFilter">
					<el-option
						v-for="item in inventoryStatusOptions"
						:key="item.key"
						:label="item.value"
						:value="item.key">
					</el-option>
				</el-select>
			</div>
			<div style="float: right; width: 300px; margin-right: 20px;">
				<el-select v-model="sortCode" clearable placeholder="请选择商品分类" @change="classFilter">
					<el-option
						v-for="item in allClassDatas"
						:key="item.code"
						:label="item.name"
						:value="item.code">
					</el-option>
				</el-select>
			</div>
			<!-- <el-button type="primary" @click="exportExcel" class="hzpbtn-primary">导出</el-button> -->
			<!-- <el-button type="primary" @click="syncCommodity" class="hzpbtn-primary">同步商品库存</el-button> -->
			<el-button type="primary" @click="editData" class="hzpbtn-primary">新增商品</el-button>
			<el-button type="primary" @click="syncPlanQty" class="hzpbtn-primary">同步商品库存</el-button>
			<el-button type="primary" @click="showVolumeDialog" class="hzpbtn-primary">销量查询</el-button>
		</section>
		<!--列表-->
		<el-table 
			:data="datas" 
			:stripe="true" :height="containerHeight - 85" 
			highlight-current-row v-loading="loading" 
			id="out-table" class="data-content" border
			@selection-change="invsVolumeSelectionChange"
		>
			<el-table-column type="selection" align="center"></el-table-column>
			<af-table-column label="序号" align="center" type="index" width="60">
				<template slot-scope="scope">
    				<span>{{ ((!!pageNum && !!pageSize)? ((pageNum - 1) * pageSize) : 0) + scope.$index + 1}}</span>
				</template>
			</af-table-column>
			<af-table-column prop="name" label="商品名称" align="center">
			</af-table-column>
			<af-table-column prop="code" label="  商品编号  " align="center">
			</af-table-column>
			<af-table-column prop="free1" label="  云仓编号  " align="center">
			</af-table-column>
			<af-table-column prop="sortName" label="商品分类" align="center">
			</af-table-column>
			<el-table-column prop="" label="图片" align="center">
				<template slot-scope="scope">
					<el-image style="width: 50px; height: 50px" :src="scope.row.thumimage" fit="cover" v-show="!!scope.row.thumimage"></el-image>
				</template>
			</el-table-column>
			<af-table-column prop="ccomunitname" label="单位" align="center">
			</af-table-column>
			<af-table-column prop="refsaleprice" label="零售价" align="center">
			</af-table-column>
			<af-table-column prop="free3" label="规格" align="center">
			</af-table-column>
			<af-table-column prop="free2" label="预计库存" align="center">
			</af-table-column>
			<af-table-column prop="bbarcode" label="条形码" align="center">
			</af-table-column>
			<!-- <af-table-column prop="free4" label="同步云仓返回信息" align="center">
			</af-table-column> -->
			<el-table-column prop="free5" label="备注" align="center" min-width="150">
			</el-table-column>
			<af-table-column label="操作" align="center" width="300" fixed="right">
				<template slot-scope="scope">
					<label class="edit-content" @click.prevent="enableData(scope.$index, scope.row)">{{scope.row.status === 0? '禁用' : '启用'}}</label>|
					<el-dropdown trigger="hover" style="line-height: 0;">
						<label class="hzpedit">编辑</label>
						
						<el-dropdown-menu slot="dropdown" class="edit-more-content">
							<el-dropdown-item @click.native.prevent="editData(scope.$index, scope.row)">信息</el-dropdown-item>
							<el-dropdown-item @click.native.prevent="editImageData(scope.$index, scope.row)">图片</el-dropdown-item>
						</el-dropdown-menu>
					</el-dropdown>
					<el-dropdown trigger="hover" style="line-height: 0;">
						<label class="hzpedit">同步</label>
						<el-dropdown-menu slot="dropdown" class="edit-more-content">
							<el-dropdown-item @click.native.prevent="syncInventoryInfo(scope.$index, scope.row)">信息</el-dropdown-item>
							<el-dropdown-item @click.native.prevent="syncInventoryPlanQty(scope.$index, scope.row)">库存</el-dropdown-item>
						</el-dropdown-menu>
					</el-dropdown>
				</template>
			</af-table-column>
		</el-table>
		<!--分页-->
		<pagination :pageSize="pageSize" :pageNum="pageNum" :totalNum="totalNum" 
			@handlePageSizeChange="handlePageSizeChange" @handlePageCurrentChange="handlePageCurrentChange">
		</pagination>
		<!--编辑数据-->
        <el-dialog :title="dialogTitle + '数据'" :visible.sync="editImageDialogVisible" class="dialog-container" width="500px" @opened="openImageDialog">
			<el-form :model="formData" :rules="rules" ref="formData" label-width="100px" class="form-data" size="mini">
				<el-form-item label="商品名称" prop="" >
					<el-input v-model="formData.name" placeholder="请输入" id="focus-ele" :disabled="codeDisabled"></el-input>
				</el-form-item>
				<el-form-item label="商品编号" prop="">
					<el-input v-model="formData.code" placeholder="请输入" :disabled="codeDisabled"></el-input>
				</el-form-item>
				<!-- <el-form-item label="条形码" prop="">
					<el-input v-model="formData.free3" placeholder="请输入" :disabled="codeDisabled"></el-input>
				</el-form-item>
				<el-form-item label="商品分类" prop="">
					<el-select v-model="formData.sortObj" value-key="code" placeholder="请选择" @change="changeSortCodeSelect">
						<el-option
							v-for="item in classDatas"
							:key="item.code"
							:label="item.name"
							:value="item">
						</el-option>
					</el-select>
				</el-form-item>
				<el-form-item label="单位" prop="">
					<el-input v-model="formData.ccomunitname" placeholder="请输入"></el-input>
				</el-form-item>
				<el-form-item label="零售价" prop="">
					<el-input v-model="formData.refsaleprice" placeholder="请输入"></el-input>
				</el-form-item> -->
				<el-form-item label="图片" prop="">
					<UploadFile :imgUrl="formData.thumimage" @getFileUrl="getFileUrl" ref='UploadFile'></UploadFile>
				</el-form-item>
				<!-- <el-form-item label="备注" prop="">
					<el-input v-model="formData.remark" placeholder="请输入" type="textarea" :rows="4"></el-input>
				</el-form-item> -->
        	</el-form>
            <div slot="footer" class="dialog-footer">
              	<el-button @click="closeDialog" class="hzpbtn-close">关 闭</el-button>
                <el-button type="primary" @click="handleImageSubmit('formData')" v-loading="loading" class="hzpbtn-primary">保 存</el-button>
            </div>
        </el-dialog>

		<!--编辑数据-->
        <el-dialog :title="dialogTitle + '数据'" :visible.sync="editDialogVisible" class="dialog-container" width="500px" @opened="openDialog">
			<el-form :model="formData" :rules="rules" ref="formData" label-width="100px" class="form-data" size="mini">
				<el-form-item label="商品名称" prop="" >
					<el-input v-model="formData.name" placeholder="请输入" id="focus-ele" :disabled="codeDisabled"></el-input>
				</el-form-item>
				<el-form-item label="商品编号" prop="">
					<el-input v-model="formData.code" placeholder="请输入" :disabled="codeDisabled"></el-input>
				</el-form-item>
				<el-form-item label="条形码" prop="">
					<el-input v-model="formData.bbarcode" placeholder="请输入" ></el-input>
				</el-form-item>
				<el-form-item label="商品分类" prop="">
					<el-select v-model="formData.sortObj" value-key="code" placeholder="请选择" @change="changeSortCodeSelect">
						<el-option
							v-for="item in classDatas"
							:key="item.code"
							:label="item.name"
							:value="item">
						</el-option>
					</el-select>
				</el-form-item>
				<el-form-item label="单位" prop="">
					<el-input v-model="formData.ccomunitname" placeholder="请输入"></el-input>
				</el-form-item>
				<el-form-item label="零售价" prop="">
					<el-input v-model="formData.refsaleprice" placeholder="请输入"></el-input>
				</el-form-item>
				<el-form-item label="规格" prop="">
					<el-input v-model="formData.free3" placeholder="请输入"></el-input>
				</el-form-item>
				<!-- <el-form-item label="图片" prop="">
					<UploadFile :imgUrl="formData.thumimage" @getFileUrl="getFileUrl" ref='UploadFile'></UploadFile>
				</el-form-item> -->
				<el-form-item label="备注" prop="">
					<el-input v-model="formData.free5" placeholder="请输入" type="textarea" :rows="4"></el-input>
				</el-form-item>
        	</el-form>
            <div slot="footer" class="dialog-footer">
              	<el-button @click="closeDialog" class="hzpbtn-close">关 闭</el-button>
                <el-button type="primary" @click="handleSubmit('formData')" v-loading="loading" class="hzpbtn-primary">保 存</el-button>
            </div>
        </el-dialog>

		<!--编辑数据-->
    	<el-dialog title="增加库存" :visible.sync="planqtyDialogVisible" class="dialog-container" width="700px" @opened="openDialog">
			<el-form :model="planQtyFormData" ref="planQtyFormData" label-width="100px" class="form-data" size="mini">
				<el-form-item label="增加库存数" prop="" >
					<el-input v-model="planQtyFormData.plan_qty" placeholder="请输入" id="focus-ele"></el-input>
				</el-form-item>
				<div v-if="planQtyFormData.isTH">
					<div v-for="(item,index) of planQtyFormData.itemList" :key="index" style="display: flex;text-align: center;align-items: center;">
						<el-form-item label="商品名称" prop="" >
							<el-select style="width: 240px;" filterable v-model="item.itemCode" clearable placeholder="请选择" @change="selectSPChange(index)">
								<el-option
									v-for="item2 in selectSPList"
									:key="item2.code"
									:label="item2.name"
									:value="item2.code">
								</el-option>
							</el-select>
						</el-form-item>
						<el-form-item label="库存数" prop="" >
							<el-input v-model="item.itemSize" placeholder="请输入" id="focus-ele"></el-input>
						</el-form-item>
					</div>
					<el-button size="mini" @click="planQtyFormData.itemList.push({itemCode:'',itemName:'',itemSize:''})" class="hzpbtn-close" style="float: right; margin-bottom: 20px;">添加单品</el-button>
				</div>
				<el-form-item label="备注" prop="">
					<el-input v-model="planQtyFormData.remark" placeholder="请输入" type="textarea" :rows="4"></el-input>
				</el-form-item>
      		</el-form>
			<div slot="footer" class="dialog-footer">
					<el-button @click="planqtyDialogVisible=false;" class="hzpbtn-close">关 闭</el-button>
					<el-button type="primary" @click="handlePlanQtySubmit('planQtyFormData')" v-loading="loading" class="hzpbtn-primary">保 存</el-button>
			</div>
    	</el-dialog>

		<!--查询销量-->
    	<el-dialog title="查询销量" :visible.sync="volumeDialogVisible" class="dialog-container" width="1000px" @opened="openDialog">
			<el-form :model="volumeFormData" ref="volumeFormData" label-width="100px" class="form-data flex-row" size="mini">
				<!-- <el-col :span="2" align="left">
					<el-select v-model="volumeFormData.paystatus" style="width: 97%;" clearable placeholder="按订单状态搜索">
						<el-option
							v-for="item in orderStatusOptions"
							:key="item.key"
							:label="item.value"
							:value="item.key">
						</el-option>
					</el-select>
				</el-col> -->
				<div class="flex-row" style="margin-right: 20px;">
					<div style="width: 200px;">
						<el-date-picker v-model="volumeFormData.startime" type="date" clearable placeholder="订单日期起" :picker-options="startPickerOptions" @change="orderTimeChange('start')"> </el-date-picker>
					</div>
					<div style="width: 200px; margin-left: 10px;">
						<el-date-picker v-model="volumeFormData.endtime" type="date" clearable placeholder="订单日期止" :picker-options="endPickerOptions" @change="orderTimeChange('end')"> </el-date-picker>
					</div>
					<div style="margin-left: 10px;">
						<el-select v-model="volumeFormData.aagencyid" style="width: 97%;" clearable placeholder="按培训公司搜索">
							<el-option
								v-for="item in agencyDatas"
								:key="item.aagencyid"
								:label="item.aagencyname"
								:value="item.aagencyid">
							</el-option>
						</el-select>
					</div>
				</div>

				<el-button type="primary" @click="handleInvsVolumeSubmit('volumeFormData')" v-loading="loading" class="hzpbtn-primary">查 询</el-button>
      		</el-form>

			  <el-table 
			  	:data="volumeInvs" 
				:stripe="true" height="500px" 
				highlight-current-row v-loading="loading"
				:key="itemKey"
				border
			>
				<af-table-column label="序号" align="center" type="index" width="60">
					<template slot-scope="scope">
						<span>{{ scope.$index + 1}}</span>
					</template>
				</af-table-column>
				<af-table-column prop="name" label="商品名称" align="center">
				</af-table-column>
				<af-table-column prop="code" label="  商品编号  " align="center">
				</af-table-column>
				<af-table-column prop="free1" label="  云仓编号  " align="center">
				</af-table-column>
				<af-table-column prop="sortName" label="商品分类" align="center">
				</af-table-column>
				<af-table-column prop="total" label="销量" align="center">
				</af-table-column>
				<af-table-column prop="increase_qty" label="库存增加" align="center">
				</af-table-column>
				<af-table-column prop="decrease_qty" label="库存减少(非销售)" align="center">
				</af-table-column>
				<af-table-column label="操作" align="center" width="100" fixed="right">
				<template slot-scope="scope">
					<label class="edit-content" @click="showDetailDialog(scope.row.code)">库存明细</label>
				</template>
			</af-table-column>
			</el-table>
    	</el-dialog>

		<el-dialog title="库存明细" :visible.sync="detailDialogVisible" class="dialog-container" width="1000px" @opened="openDialog">
			<el-table 
			  	:data="detailLists" 
				:stripe="true" height="500px" 
				highlight-current-row 
				v-loading="loading"
				:key="itemKey"
				border
			>
				<af-table-column label="序号" align="center" type="index" width="60">
					<template slot-scope="scope">
						<span>{{ scope.$index + 1}}</span>
					</template>
				</af-table-column>
				<af-table-column label="操作个数" align="center">
					<template slot-scope="scope">
						<span style="color: red;" v-if="scope.row.act_type == '1'">{{ "-" + scope.row.plan_qty }}</span>
						<span style="color: #00B136;" v-else>{{ "+" + scope.row.plan_qty }}</span>
					</template>
				</af-table-column>
				
				<af-table-column prop="creat_time" label="操作时间" align="center">
					<template slot-scope="scope">
						<span>{{ formatTime(scope.row.creat_time) }}</span>
					</template>
				</af-table-column>
			</el-table>
    	</el-dialog>
	</section>
</template>
<script>
	import { getInventoryClassList } from '../../api/api';
	import { getInventoryList } from '../../api/api';
	import { inventoryVague, queryInvSalesVolume, queryQtyDetail } from '../../api/api';
	import { addInventoryimage } from '../../api/api';
	import { editInventoryimage, areabyroleList } from '../../api/api';
	import { enableInventory, queryInventoryPlanQty } from '../../api/api';
	import { updateInventory, addInventory, updateInventoryInfo, syncInventory, addInventoryPlanQty,addSetboxPlanQty } from '../../api/api';
	import util from '../../common/js/util';
	import emEnum from '../../common/js/emEnum';
	// 分页组件
	import Pagination from '../Pagination';
	// 上传文件组件
	import UploadFile from '../UploadFile';

	export default {
		components: {
			Pagination,
			UploadFile
		},
		data() {
			return {
				sysCnName: emEnum.cnName,  // 中文名称
				sysEnName: emEnum.enName,  // 英文名称

				loginId: '',   // 登录用户

				loading: false,
				containerHeight: 0,
				datas: [],
				// 分页
				pageSize: 100,
				pageNum: 1,
        		totalNum: 0,

				inventoryStatusOptions: emEnum.inventoryStatusOptions,  // 商品状态可选项
				spCodeOptions: emEnum.spCodeOptions,  // 存在商品code为套盒
				allClassDatas: [], // 带全部的商品分类
				classDatas: [],    // 商品分类
				dialogTitle: '',   // 对话框标题
				dialogBtnVal: '',  // 按钮值
				editDialogVisible: false,  // 编辑对话框
				editImageDialogVisible: false,
				codeDisabled: true,  // 编号不可编辑
				
				sortCode: '',        // 搜索分类
				inventoryStatus: '', // 商品状态
				oldVagueContent: '', // 原快速搜索内容
				vagueContent: '',    // 快速搜索内容

				planQtyFormData: {
					plan_qty: "",
					code: '',      // 商品编号
					invName: '',
					remark: '',
					uid: '',
					isTH: false,
					itemList:[],
				},
				planqtyDialogVisible: false,
				volumeDialogVisible: false,
				detailDialogVisible: false,
				volumeInvs: [],
				currInvcode: '',
				detailLists: [],
				itemKey: 0,
				
				volumeFormData: {
					startime: "",
					endtime: "",
					invcodeList: "",
					paystatus: "",
					aagencyid: "",
				},
				agencyDatas: [],
				startPickerOptions: {
					disabledDate: time => {
						if(!!this.volumeFormData.endtime) {
							return time.getTime() > new Date(this.volumeFormData.endtime);
						}
						return false;
					}
				},  // 订单日期始时间范围
				endPickerOptions: {
					disabledDate: time => {
						if(!!this.volumeFormData.startime) {
							return time.getTime() < new Date(this.volumeFormData.startime);
						}
						return false;
					}
				},  // 订单日期止时间范围

				formData: {
					id: '',        // 选中ID
					name: '',      // 商品名称
					code: '',      // 商品编号
					sortCode: '',   // 分类code
					sortName: '',   // 分类名称
					sortObj: {
						code: '',   // 分类code
						name: '',   // 分类名称
					},
					ccomunitname: '',  // 单位
					free3: '', // 规格
					bbarcode: '', // 条形码
					refsaleprice: '',   // 零售价
					highimage: '',	   // 高清图
					thumimage: '',     // 缩略图
					free5: ''         // 备注
				},
				rules: {
					name: [
						{ required: true, message: '请输入商品名称', trigger: 'blur' }
					],
					code: [
						{ required: true, message: '请输入商品编号', trigger: 'change' }
					],
					ccomunitname: [
						{ required: true, message: '请输入单位', trigger: 'change' }
					],
					free3: [
						{ required: true, message: '请输入规格', trigger: 'change' }
					],
					bbarcode: [
						{ required: true, message: '请输入条形码', trigger: 'change' }
					],
					sortCode: [
						{ required: true, message: '请选择分类', trigger: 'change' }
					],
					refsaleprice: [
						{ required: true, message: '请输入零售价', trigger: 'change' }
					]
				},
				selectSPList:[],
			}
		},
		methods: {
			selectSPChange(index){
				console.log(index)
				this.selectSPList.forEach(element => {
					if (element.code == this.planQtyFormData.itemList[index].itemCode) {
						this.planQtyFormData.itemList[index].itemName = element.name;
					}
				});
				console.log(this.planQtyFormData)
			},
			// 获取预售商品对应列表
			getSPData: function () {
				let _this = this;
				// 执行操作
				_this.loading = true;
				let para = {
					pageNum: 1,
					pageSize: 10000,
				};
				getInventoryList(para, _this)
				.then(res=>res.data)
				.then(data => {
					_this.loading = false;
					let { code, msg } = data;
					if (code !== "0") {
						_this.$message({
							message: msg,
							type: 'error'
						});
					} else {
						_this.selectSPList = data.data.list;
					}
				});
			},
			changeSortCodeSelect(event) {
				this.formData.sortObj.code = event.code;
				this.formData.sortObj.name = event.name;
				this.formData.sortCode = event.code;
				this.formData.sortName = event.name;
			},
			// 时间显示格式转换
			formatCreatTime: function (row, column) {
				if(!!row.creattime) {
					return util.formatDate.format(new Date(row.creattime.replace(/-/g, '/')), 'yyyy-MM-dd HH:mm:ss');
				} else return row.creattime;
			},
			// 时间显示格式转换
			formatTime: function (time) {
				return util.formatDate.format(new Date(time), 'yyyy-MM-dd HH:mm:ss');
			},
			// 搜索数据
			searchList: function () {
				let _this = this;
				if(_this.oldVagueContent !== _this.vagueContent) {
					_this.oldVagueContent = _this.vagueContent;
					// 如果有搜索数据，则模糊搜索
					if(!!_this.vagueContent) {
						// 执行操作
						_this.loading = true;
						_this.closeDialog();  // 关闭编辑对话框
						let para = {
							vague: _this.vagueContent,
							pageNum: !!_this.pageNum? _this.pageNum : 1,
							pageSize: !!_this.pageSize? _this.pageSize : 100
						};
						// 如果有分类筛选值，则按分类筛选
						if(!!_this.sortCode) para['code'] = _this.sortCode;
						// 如果有状态筛选值，则按状态筛选
						if(_this.inventoryStatus !== '') para['status'] = _this.inventoryStatus;

						inventoryVague(para, _this)
						.then(res=>res.data)
						.then(data => {
							_this.loading = false;
							let { code, msg } = data;
							if (code !== "0") {
								_this.$message({
									message: msg,
									type: 'error'
								});
							} else {
								let { pageSize, pageNum, totalNum } = data.data;
								data = data.data.list;
								_this.datas = data;
								// 分页
								_this.pageSize = pageSize;
								_this.pageNum = pageNum;
								_this.totalNum = totalNum;
							}
						});
					} else {
						_this.getData();
					}
				}
			},
			orderTimeChange: function (control) {
				let _this = this;
				// if (control === 'start') {
				// 	_this.volumeFormData.startime = util.formatDate.format(new Date(_this.volumeFormData.startime), 'yyyy-MM-dd') + ' 00:00:00';
				// }else if (control === 'end') {
				// 	_this.volumeFormData.endtime = util.formatDate.format(new Date(_this.volumeFormData.endtime), 'yyyy-MM-dd') + ' 00:00:00';
				// }
			},
			// 分类筛选
			classFilter: function () {
				let _this = this;
				if(!!_this.vagueContent) {
					_this.searchList();
				} else {
					_this.getData();
				}
			},
			// 获取列表
			getData: function () {
				let _this = this;
				// 执行操作
				_this.loading = true;
				_this.closeDialog();  // 关闭编辑对话框
				let para = {
					pageNum: !!_this.pageNum? _this.pageNum : 1,
					pageSize: !!_this.pageSize? _this.pageSize : 100
				};
				// 如果有分类筛选值，则按分类筛选
				if(!!_this.sortCode) para['sortCode'] = _this.sortCode;
				// 如果有状态筛选值，则按状态筛选
				if(_this.inventoryStatus !== '') para['status'] = _this.inventoryStatus;

				getInventoryList(para, _this)
				.then(res=>res.data)
				.then(data => {
					_this.loading = false;
					let { code, msg } = data;
					if (code !== "0") {
						_this.$message({
							message: msg,
							type: 'error'
						});
					} else {
						let { pageSize, pageNum, totalNum } = data.data;
						data = data.data.list;
						_this.datas = data;
						// 分页
						_this.pageSize = pageSize;
						_this.pageNum = pageNum;
						_this.totalNum = totalNum;
					}
				});
			},
			// 修改数据状态（禁用、启用）
			enableData: function (index, row) {
				let _this = this;
				_this.$confirm('确认' + (row.status === 0? '禁用' : '启用') + '该库存？', '提示', {
					type: 'warning'
				}).then(() => {
					_this.loading = true;
					let para = {
						code: row.code,
						status: row.status === 0? '1' : '0'
					};
					enableInventory(para, _this)
					.then(res=>res.data)
					.then(data => {
						_this.loading = false;
						let { msg, code } = data;
						if (code !== "0") {
							_this.$message({
								message: msg,
								type: 'error'
							});
						} else {
							_this.$message({
								message: msg,
								type: 'success'
							});
							_this.getData();
						}
					});
				}).catch((error) => {
					if(error!=='cancel') {
						_this.$message({
							message: error.message? error.message : error,
							type: 'error'
						});
					}
				});
			},
			// 重置输入
			resetForm: function (formName) {
				this.$refs[formName].resetFields();
			},
			// 编辑数据
			editData: function (index, row) {
				let formName = 'formData';
				// 打开对话框
				this.editDialogVisible = true;
				// 默认值
				if(!row) {
					this.codeDisabled = false;
					this.dialogTitle = "添加";
					if(this.$refs[formName]) this.$refs[formName].resetFields();
					this.formData.name = "";
					this.formData.code = "";
					this.formData.sortObj = {code: "", name: ""};
					this.formData.sortCode = "";
					this.formData.sortName = "";
					this.formData.ccomunitname = "";
					this.formData.refsaleprice = "";
					this.formData.free3 = "";
					this.formData.bbarcode = "";
					// this.formData.highimage = "";
					// this.formData.thumimage = "";
					this.formData.free5 = "";
					this.formData.bbarcode = "";
				} else {
					this.codeDisabled = row.free1 ? true : false;
					this.dialogTitle = "编辑";
					this.formData.id = row.id;
					this.formData.name = row.name;
					this.formData.code = row.code;
					this.formData.sortCode = row.sortCode;
					this.formData.sortName = row.sortName;
					// this.formData.sortObj.sortCode = row.code;
					// this.formData.sortObj.sortName = row.name;
					this.$set(this.formData, "sortObj", {code: row.sortCode, name: row.sortName})
					this.formData.ccomunitname = row.ccomunitname;
					this.formData.refsaleprice = row.refsaleprice;
					this.formData.free3 = row.free3;
					// this.formData.highimage = row.highimage;
					// this.formData.thumimage = row.thumimage;
					this.formData.free5 = row.free5;
					this.formData.bbarcode = row.bbarcode;
					// 校验输入
					if(this.$refs[formName]) this.$refs[formName].validate();
				}
			},
			editImageData(index, row) {
				let formName = 'formData';
				// 打开对话框
				this.editImageDialogVisible = true;
				// 默认值
				this.dialogTitle = "编辑";
				this.dialogBtnVal = "重新上传";
				this.formData.id = row.id;
				this.formData.name = row.name;
				this.formData.code = row.code;
				this.formData.highimage = row.highimage;
				this.formData.thumimage = row.thumimage;
			},
			// 打开编辑对话框
			openImageDialog: function () {
				// 改变组件的图片地址和按钮值
				this.$refs.UploadFile.changeFileUrl(this.formData.thumimage, this.dialogBtnVal);
			},
			// 打开编辑对话框
			openDialog: function () {
				// 焦点放到获取焦点的输入框
				let ele = document.getElementById('focus-ele');
				if(!!ele) {
					ele.focus();
				}
			},
			// 关闭编辑对话框
			closeDialog: function () {
				this.editDialogVisible = false;
			},
			// 提交数据
			handleImageSubmit: function (formName) {
				this.$refs[formName].validate((valid) => {
          			if (valid) {
						let _this = this;
						_this.$confirm('确认' + _this.dialogTitle + '库存图片？', '提示', {
							type: 'warning'
						}).then(() => {
							_this.loading = true;
							if(!_this.formData.code) {  // 添加
								let para = {
									inventorycode: _this.formData.code,
									highimage: _this.formData.highimage,
									thumimage: _this.formData.thumimage,
								};
								addInventoryimage(para, _this)
								.then(res=>res.data)
								.then(data => {
									_this.loading = false;
									let { msg, code } = data;
									if (code !== "0") {
										_this.$message({
											message: msg,
											type: 'error'
										});
									} else {
										_this.$message({
											message: msg,
											type: 'success'
										});
										_this.getData();
									}
								});
							} else {  // 编辑数据
								let para = {
									inventorycode: _this.formData.code,
									highimage: _this.formData.highimage,
									thumimage: _this.formData.thumimage,
								};
								editInventoryimage(para, _this)
								.then(res=>res.data)
								.then(data => {
									_this.loading = false;
									let { msg, code } = data;
									if (code !== "0") {
										_this.$message({
											message: msg,
											type: 'error'
										});
									} else {
										_this.$message({
											message: msg,
											type: 'success'
										});
										_this.getData();
									}
								});
							}
						}).catch((error) => {
							if(error!=='cancel') {
								_this.$message({
									message: error.message? error.message : error,
									type: 'error'
								});
							}
						});
					} else {
						return false;
					}
				});
			},
			handleSubmit(formName) {
				this.$refs[formName].validate((valid) => {
          			if (valid) {
						let _this = this;
						_this.$confirm('确认提交吗？', '提示', {
							type: 'warning'
						}).then(() => {
							_this.loading = true;
							if(_this.dialogTitle == '编辑') { 
								updateInventoryInfo(this.formData, _this)
								.then(res=>res.data)
								.then(data => {
									_this.loading = false;
									let { msg, code } = data;
									if (code !== "0") {
										_this.$message({
											message: msg,
											type: 'error'
										});
									} else {
										_this.$message({
											message: msg,
											type: 'success'
										});
										_this.getData();
									}
								});
							} else {  // 编辑数据
								addInventory(this.formData, _this)
								.then(res=>res.data)
								.then(data => {
									_this.loading = false;
									let { msg, code } = data;
									if (code !== "0") {
										_this.$message({
											message: msg,
											type: 'error'
										});
									} else {
										_this.$message({
											message: msg,
											type: 'success'
										});
										_this.getData();
									}
								});
							}
						}).catch((error) => {
							if(error!=='cancel') {
								_this.$message({
									message: error.message? error.message : error,
									type: 'error'
								});
							}
						});
					} else {
						return false;
					}
				});
			},
			// 导出Execl
			exportExcel: function () {
				let _this = this;
				// 获取所有商品
				_this.loading = true;
				_this.closeDialog();  // 关闭编辑对话框
				let para = {
					pageNum: 1,
					pageSize: _this.totalNum
				};
				// 如果有状态筛选值，则按状态筛选
				if(_this.inventoryStatus !== '') para['status'] = _this.inventoryStatus;
				// 如果有搜索数据，则模糊搜索
				if(!!_this.vagueContent) {
					para['vague'] = _this.vagueContent;
					// 如果有分类筛选值，则按分类筛选
					if(!!_this.sortCode) para['code'] = _this.sortCode;
					inventoryVague(para, _this)
					.then(res=>res.data)
					.then(data => {
						_this.loading = false;
						let { code, msg } = data;
						if (code !== "0") {
							_this.$message({
								message: msg,
								type: 'error'
							});
						} else {
							data = data.data.list;
							_this.doExport(data);
						}
					});
				} else {
					// 如果有分类筛选值，则按分类筛选
					if(!!_this.sortCode) para['sortCode'] = _this.sortCode;
					getInventoryList(para, _this)
					.then(res=>res.data)
					.then(data => {
						_this.loading = false;
						let { code, msg } = data;
						if (code !== "0") {
							_this.$message({
								message: msg,
								type: 'error'
							});
						} else {
							data = data.data.list;
							_this.doExport(data);
						}
					});
				}
			},
			// 执行导出
			doExport: function (tabData) {
				if(!!tabData && tabData.length > 0) {
					let i = 1;
					tabData.forEach(item => {
						item.id = i;
						item.status = item.status === 0? '启用' : '禁用';
						item.refsaleprice = parseFloat(item.refsaleprice);
						i++;
					});
					// 获取文件名
					let fileName = this.sysCnName + "_" + util.formatDate.format(new Date(), "yyyy-MM-dd HH:mm:ss") + "_" + this.$root.$route.name;
					// 表头和对应字段
					let tHeader = [ '序号', '商品名称', '商品编号', '商品分类', '单位', '零售价', '状态', '备注' ];
					let filterVal = [ 'id', 'name', 'code', 'sortCode', 'ccomunitname', 'refsaleprice', 'status', 'remark' ];
					util.exportJsonToExcel(fileName, JSON.stringify(tabData), tHeader, filterVal);
				} else {
					this.$message({
						message: "没有需要导出的数据",
						type: 'warning'
					});
				}
			},
			// 同步商品
			syncCommodity: function () {
				let _this = this;
				_this.$confirm('确认同步商品？', '提示', {
					type: 'warning'
				}).then(() => {
					_this.loading = true;
					let para = {};
					updateInventory(para, _this)
					.then(res=>res.data)
					.then(data => {
						let { code, msg } = data;
						if (code !== "0") {
							_this.$message({
								message: msg,
								type: 'error'
							});
						} else {
							_this.loading = false;
							_this.$message({
								message: '同步商品成功！',
								type: 'success'
							});
						}
					});
				}).catch((error) => {
					if(error!=='cancel') {
						_this.$message({
							message: error.message? error.message : error,
							type: 'error'
						});
					}
				});
			},
			// 获取区域
			getAllClass: function () {
				let _this = this;
				_this.loading = true;
				let para = {"pageNum":1,"pageSize":1000,"channel":"pc"};
				getInventoryClassList(para, _this)
				.then(res=>res.data)
				.then(data => {
					let { code, msg } = data;
					if (code !== "0") {
						_this.$message({
							message: msg,
							type: 'error'
						});
					} else {
						data = data.data.list;
						_this.classDatas = data;  //.filter(item => item.status === 0);
						// 处理带全部的商品分类
						//_this.allClassDatas.push({code: '', name: '全部'});
						_this.classDatas.forEach(item => {
							_this.allClassDatas.push({code: item.code, name: item.name});
						});
						_this.loading = false;
					}
				});
			},
			// 获取文件路径
			getFileUrl: function (high, thum) {
				this.formData.highimage = high;
				this.formData.thumimage = thum;
			},
			// 分页事件
			handlePageSizeChange(val) {
				this.pageSize = val;
				this.getData();
			},
			handlePageCurrentChange(val) {
				this.pageNum = val;
				this.getData();
			},

			syncInventoryInfo(index, row) {
				this.$confirm('确认同步商品吗？', '提示', {
					type: 'warning'
				}).then(() => {
					this.loading = true;
					syncInventory({
						code: row.code
					}, this)
					.then(res=>res.data)
					.then(data => {
						let { code, msg } = data;
						if (code !== "0") {
							this.$message({
								message: msg,
								type: 'error'
							});
						} else {
							this.loading = false;
							this.$message({
								message: '同步商品成功！',
								type: 'success'
							});

							this.getData();
						}
					});
				})
			},
			syncPlanQty() {
				this.$confirm('确认同步商品库存吗？', '提示', {
					type: 'warning'
				}).then(() => {
					this.loading = true;
					queryInventoryPlanQty({
						uid:this.loginId,
					}, this)
					.then(res=>res.data)
					.then(data => {
						this.loading = false;
						let { code, msg } = data;
						if (code !== "0") {
							this.$message({
								message: msg,
								type: 'error'
							});
						} else {
							this.$message({
								message: '同步商品成功！',
								type: 'success'
							});

							this.getData();
						}
					});
				})
			},
			syncInventoryPlanQty(index, row) {
				this.planQtyFormData.isTH = this.spCodeOptions.includes(row.code);
				this.planQtyFormData.itemList = [];
				this.planQtyFormData.code = row.code;
				this.planQtyFormData.invName = row.name;
				this.planQtyFormData.plan_qty = '';
				this.planQtyFormData.remark = '';
				this.planqtyDialogVisible = true;
			},

			handlePlanQtySubmit(formName) {
				this.$confirm('确认提交吗？', '提示', {
						type: 'warning'
					}).then(() => {
						this.loading = true;
						if(this.planQtyFormData.code) { 
							if (this.planQtyFormData.isTH && this.planQtyFormData.itemList.length > 0) {
								addSetboxPlanQty({
									...this.planQtyFormData,
									uid: this.loginId,
									updateuser: this.loginId,
								}, this)
								.then(res=>res.data)
								.then(data => {
									this.loading = false;
									let { msg, code } = data;
									if (code !== "0") {
										this.$message({
											message: msg,
											type: 'error'
										});
									} else {
										this.$message({
											message: msg,
											type: 'success'
										});
									}
								})	
							} else {
								addInventoryPlanQty({
									...this.planQtyFormData,
									uid: this.loginId
								}, this)
								.then(res=>res.data)
								.then(data => {
									this.loading = false;
									let { msg, code } = data;
									if (code !== "0") {
										this.$message({
											message: msg,
											type: 'error'
										});
									} else {
										this.$message({
											message: msg,
											type: 'success'
										});
									}
								})	
							}
						}

						this.planqtyDialogVisible = false;
					})
			},
			getOneDay(){
				let date=new Date();
				//获取第一天
				date.setDate(1);
				let y=date.getFullYear();
				let m=date.getMonth()+1;
				let d=date.getDate();
				m=m<10?"0"+m:m;
				d=d<10?"0"+d:d;
				let monthOneDay = y+"-"+m+"-"+d+` `+`00:00:00`
				return monthOneDay
			},
			getLastDay(){
				//获取最后一天
				let date = new Date();
				let currentMonth = date.getMonth();
				let nextMonth = ++currentMonth;
				let nextMonthFirstDay = new Date(date.getFullYear(), nextMonth, 1);
				let oneDay = 1000 * 60 * 60 * 24;
				let lastTime = new Date(nextMonthFirstDay - oneDay);
				let month = parseInt(lastTime.getMonth() + 1);
				let day = lastTime.getDate();
				if (month < 10) {
					month = '0' + month
				}
				if (day < 10) {
					day = '0' + day
				}
				let monthLastDay = date.getFullYear() + '-' + month + '-' + day+` `+`23:59:59`;
				return monthLastDay;
			},
			invsVolumeSelectionChange(val) {
				this.volumeInvs = [];
				if(!!val && val.length > 0) {
					val.forEach(a=>{
						a.total = 0;
						this.volumeInvs.push(a);
					});
				}
			},
			showVolumeDialog() {
				if (this.volumeInvs.length == 0) {
					this.$message({
						message: "请选择商品",
						type: 'error'
					});
					return;
				}
				this.volumeFormData = {
					startime: new Date(this.getOneDay()).valueOf(),
					endtime: new Date(this.getLastDay()).valueOf(),
					invcodeList: "",
					aagencyid: "",
					paystatus: "",
				};
				this.volumeDialogVisible = true
			},
			// 查询商品效率
			handleInvsVolumeSubmit() {
				this.loading = true;
				let parma = {
					aagencyid: this.volumeFormData.aagencyid,
					startime: this.volumeFormData.startime ? this.volumeFormData.startime.valueOf() : "",
					endtime: this.volumeFormData.endtime ? this.volumeFormData.endtime.valueOf() : "",
				}
				let invcodeList = [];
				this.volumeInvs.map((item, index) => {
					invcodeList.push(item.code);
				});
				queryInvSalesVolume({
					...parma,
					invcode: invcodeList.join(","),
				}, this)
				.then(res => {
					this.loading = false;
					let { msg, code, data } = res.data;
					if (code !== "0") {
						this.$message({
							message: msg,
							type: 'error'
						});
					} else {
						let volumeInvs = [].concat(this.volumeInvs);
						volumeInvs.map((item, index) => {
							item.total = data.volumes[index] || 0;
							item.decrease_qty = data.qtys[index].decrease_qty || 0;
							item.increase_qty = data.qtys[index].increase_qty || 0;
						});

						this.volumeInvs = volumeInvs;
						this.itemKey = Math.random()
					}
				})	
			},

			showDetailDialog(currInvcode) {
				this.detailDialogVisible = true;
				this.currInvcode = currInvcode;
				this.queryQtyDetailResp();
			},

			queryQtyDetailResp() {
				this.loading = true;
				let parma = {
					startime: this.volumeFormData.startime ? this.volumeFormData.startime.valueOf() : "",
					endtime: this.volumeFormData.endtime ? this.volumeFormData.endtime.valueOf() : "",
					invcode: this.currInvcode
				}
				queryQtyDetail(parma, this).then(res => {
					this.loading = false;
					let { msg, code, data } = res.data;
					if (code !== "0") {
						this.$message({
							message: msg,
							type: 'error'
						});
					} else {
						this.detailLists = data.list;
					}
				})
			},

			// 获取培训公司
			getAllAgencys: function () {
				let _this = this;
				let para = {};
				areabyroleList(para, _this)
				.then(res=>res.data)
				.then(data => {
					let { code, msg } = data;
					if (code !== "0") {
						_this.$message({
							message: msg,
							type: 'error'
						});
					} else {
						let agencyData = data.data.list;
						// 培训公司
						_this.agencyDatas = agencyData;
					}
				});
			},
		},
		// 创建VUE实例后的钩子
		created: function () {
			// 登录ID
			this.loginId = util.userData.getLoginUserFieldValue(emEnum.loginUserFileds.uid, this);
			// 获取区域
			this.getAllClass();
			this.getAllAgencys();
		},
		// 挂载到DOM后的钩子
		mounted: function () {
			let _this = this;
			_this.containerHeight = _this.$refs.inventorycontainer.offsetHeight;
			// 获取参数值
			let query = _this.$route.query;
			_this.sortCode = query.code;
			// 获取用户
			_this.getData();
			_this.getSPData();
			// 绑定窗口尺寸改变事件改变数据表的高度
			window.onresize = () => {
				return (() => {
					// 为了避免频繁触发resize函数导致页面卡顿，使用定时器
					if (!this.timer && !!_this.$refs.inventorycontainer) {
						this.timer = true
						_this.containerHeight = _this.$refs.inventorycontainer.offsetHeight;
						let that = this
						setTimeout(function () {
							that.timer = false
						}, 100);
					}
				})();
			}
		}
	};
</script>

<style lang="scss">
.inventory-container {
	height: calc(100vh - 120px);
}
.edit-content {
	color: #3ca2ff;
	margin-left: 10px;
	margin-right: 10px;
	cursor: pointer;
}
.edit-more::after {
	// content: ' ﹀';
	font-size: 12px;
	height: 12px;
	line-height: 12px;
	vertical-align: baseline;
}
.edit-more:hover::after {
	// content: ' ︿';
	font-size: 12px;
	height: 12px;
	line-height: 12px;
	vertical-align: text-top;
}
</style>