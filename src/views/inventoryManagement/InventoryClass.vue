<template>
	<section class="inventoryclass-container common-container" ref="inventoryclasscontainer">
		<!--操作-->
		<section class="data-oper">
			<el-button type="primary" @click="exportExcel" class="hzpbtn-primary">导出</el-button>
			<el-button type="primary" @click="dealShopType('new',null)" class="hzpbtn-primary">新增</el-button>
			<!-- <el-button type="primary" @click="exportExcel" class="hzpbtn-primary">导出</el-button> -->
		</section>
		<!--列表-->
		<el-table :data="datas" :stripe="true" :height="containerHeight - 85" highlight-current-row v-loading="loading" id="out-table" class="data-content" border>
			<el-table-column label="序号" align="center" type="index" width="60">
				<template slot-scope="scope">
    				<span>{{(pageNum - 1) * pageSize + scope.$index + 1}}</span>
				</template>
			</el-table-column>
			<el-table-column prop="name" label="名称" align="center" min-width="50%">
			</el-table-column>
			<el-table-column prop="code" label="编号" align="center" min-width="50%">
			</el-table-column>
			<el-table-column label="操作" align="center" width="180">
				<template slot-scope="scope">
					<label class="hzpedit" @click.prevent="dealShopType('mod',scope.row)">编辑</label>
					<label class="hzpedit" @click.prevent="searchData(scope.$index, scope.row)">查找商品</label>
				</template>
			</el-table-column>
		</el-table>
		<!--分页-->
		<pagination :pageSize="pageSize" :pageNum="pageNum" :totalNum="totalNum" 
			@handlePageSizeChange="handlePageSizeChange" @handlePageCurrentChange="handlePageCurrentChange">
		</pagination>
		<!--编辑数据-->
        <el-dialog :title="dialogTitle + '数据'" :visible.sync="editDialogVisible" class="dialog-container" width="500px" height="500px" @opened="openDialog">
			<el-form :model="formData" :rules="rules" ref="formData" label-width="100px" class="form-data" size="mini">
				<el-form-item label="商品编码" prop="code">
					<el-input v-model="formData.code" placeholder="请输入" :disabled="dialogTitle=='编辑'"></el-input>
				</el-form-item>
				<el-form-item label="商品名称" prop="name" id="focus-ele">
					<el-input v-model="formData.name" placeholder="请输入"></el-input>
				</el-form-item>
        	</el-form>
            <div slot="footer" class="dialog-footer">
              	<el-button @click="closeDialog" class="hzpbtn-close">关 闭</el-button>
                <el-button type="primary" @click="handleSubmit('formData')" v-loading="loading" class="hzpbtn-primary">保 存</el-button>
            </div>
        </el-dialog>
	</section>
</template>
<script>
	import { getInventoryClassList,updateInventoryClass,addInventoryClass } from '../../api/api';
	import util from '../../common/js/util';
	import emEnum from '../../common/js/emEnum';
	// 分页组件
	import Pagination from '../Pagination';

	export default {
		components: {
			Pagination
		},
		data() {
			return {
				sysCnName: emEnum.cnName,  // 中文名称
				sysEnName: emEnum.enName,  // 英文名称
				loginId:'',
				loading: false,
				containerHeight: 0,
				datas: [],
				// 分页
				pageSize: 100,
				pageNum: 1,
        		totalNum: 0,
				editDialogVisible:false,
				dialogTitle:'',
				formData:{
					name:'',
					code:'',
				},
				rules: {
					code: [
						{ required: true, message: '请输入商品分类编号', trigger: 'blur' }
					],
					name: [
						{ required: true, message: '请输入商品分类名称', trigger: 'blur' }
					],
				},
			}
		},
		methods: {
			// 打开编辑对话框
			openDialog: function () {
				// 焦点放到获取焦点的输入框
				let ele = document.getElementById('focus-ele');
				if(!!ele) {
					ele.focus();
				}
			},
			// 关闭编辑对话框
			closeDialog: function () {
				this.editDialogVisible = false;
			},
			// 提交数据
			handleSubmit: function (formName) {
				this.$refs[formName].validate((valid) => {
          			if (valid) {
						let _this = this;
						_this.$confirm('确认' + _this.dialogTitle + '商品分类？', '提示', {
							type: 'warning'
						}).then(() => {
							_this.loading = true;
							if(_this.dialogTitle == '新增') {  // 添加
								let para = {
									uid:_this.loginId,
									code:_this.formData.code,
									name:_this.formData.name,
								};
								addInventoryClass(para, _this)
								.then(res=>res.data)
								.then(data => {
									_this.loading = false;
									let { msg, code } = data;
									if (code !== "0") {
										_this.$message({
											message: msg,
											type: 'error'
										});
									} else {
										_this.$message({
											message: msg,
											type: 'success'
										});
										_this.getData();
									}
								});
							} else {  // 编辑数据
								let para = {
									uid:_this.loginId,
									code:_this.formData.code,
									name:_this.formData.name,
								};
								updateInventoryClass(para, _this)
								.then(res=>res.data)
								.then(data => {
									_this.loading = false;
									let { msg, code } = data;
									if (code !== "0") {
										_this.$message({
											message: msg,
											type: 'error'
										});
									} else {
										_this.$message({
											message: msg,
											type: 'success'
										});
										_this.getData();
									}
								});
							}
						}).catch((error) => {
							if(error!=='cancel') {
								_this.$message({
									message: error.message? error.message : error,
									type: 'error'
								});
							}
						});
					} else {
						return false;
					}
				});
			},
			dealShopType(type,row){
				switch (type) {
					case 'new':
						this.dialogTitle = '新增'
						this.formData.name = '';
						this.formData.code = '';
						break;
					case 'mod':
						this.dialogTitle = '编辑'
						this.formData.name = row.name;
						this.formData.code = row.code;
						break;
				
					default:
						break;
				}
				this.editDialogVisible = true;
			},
			// 获取列表
			getData: function () {
				let _this = this;
				_this.closeDialog();
				// 执行操作
				_this.loading = true;
				let para = {
					pageNum: _this.pageNum,
					pageSize: _this.pageSize
				};
				getInventoryClassList(para, _this)
				.then(res=>res.data)
				.then(data => {
					_this.loading = false;
					let { code, msg } = data;
					if (code !== "0") {
						_this.$message({
							message: msg,
							type: 'error'
						});
					} else {
						let { pageSize, pageNum, totalNum } = data.data;
						data = data.data.list;
						_this.datas = data;
						// 分页
						_this.pageSize = pageSize;
						_this.pageNum = pageNum;
						_this.totalNum = totalNum;
					}
				});
			},
			// 查找商品
			searchData: function (index, row) {
				this.$router.push({ path: '/inventory', query: {code: row.code}});
			},
			// 导出Execl
			exportExcel: function () {
				let _this = this;
				// 执行操作
				_this.loading = true;
				let para = {
					pageNum: 1,
					pageSize: _this.totalNum
				};
				getInventoryClassList(para, _this)
				.then(res=>res.data)
				.then(data => {
					_this.loading = false;
					let { code, msg } = data;
					if (code !== "0") {
						_this.$message({
							message: msg,
							type: 'error'
						});
					} else {
						data = data.data.list;
						_this.doExport(data);
					}
				});
			},
			// 执行导出
			doExport: function (tabData) {
				let _this = this;
				// 导出数据
				if(!!tabData && tabData.length > 0) {
					let i = 1;
					tabData.forEach(item => {
						item.id = i;
						i++;
					});
					// 获取文件名
					let fileName = _this.sysCnName + "_" + util.formatDate.format(new Date(), "yyyy-MM-dd HH:mm:ss") + "_" + _this.$root.$route.name;
					// 表头和对应字段
					let tHeader = [ '序号', '名称', '编号' ];
					let filterVal = [ 'id', 'name', 'code' ];
					util.exportJsonToExcel(fileName, JSON.stringify(tabData), tHeader, filterVal);
				} else {
					this.$message({
						message: "没有需要导出的数据",
						type: 'warning'
					});
				}
			},
			// 分页事件
			handlePageSizeChange(val) {
				this.pageSize = val;
				this.getData();
			},
			handlePageCurrentChange(val) {
				this.pageNum = val;
				this.getData();
			}
		},
		// 创建VUE实例后的钩子
		created: function () {
			// 登录ID
			this.loginId = util.userData.getLoginUserFieldValue(emEnum.loginUserFileds.uid, this);
		},
		// 挂载到DOM后的钩子
		mounted: function () {
			let _this = this;
			_this.containerHeight = _this.$refs.inventoryclasscontainer.offsetHeight;
			// 获取用户
			_this.getData();
			// 绑定窗口尺寸改变事件改变数据表的高度
			window.onresize = () => {
				return (() => {
					// 为了避免频繁触发resize函数导致页面卡顿，使用定时器
					if (!this.timer && !!_this.$refs.inventoryclasscontainer) {
						this.timer = true
						_this.containerHeight = _this.$refs.inventoryclasscontainer.offsetHeight;
						let that = this
						setTimeout(function () {
							that.timer = false
						}, 100);
					}
				})();
			}
		}
	};
</script>

<style lang="scss">
.inventoryclass-container {
	height: calc(100vh - 120px);
}
</style>