<template>
	<section class="combination-container common-container" ref="combinationcontainer">
		<!--操作-->
		<section class="data-oper">
			<el-button type="primary" @click="editData" class="hzpbtn-primary" v-loading="loading">添加</el-button>
			<el-button type="primary" @click="exportExcel" class="hzpbtn-primary">导出</el-button>
			<el-button type="info" plain @click="pagePositionClick" class="hzpbtn-info" style="float: right; margin-right: 2px;">{{ pagePosition }}</el-button>
			<div style="float: right; width: 100px; font-size: 20px; margin-top: 8px; margin-right: -15px;">
				<label>上级位置：</label>
			</div>
		</section>
		<!--列表-->
		<el-table :data="datas" :stripe="true" :height="containerHeight - 85" highlight-current-row v-loading="loading" id="out-table" 
			class="data-content" border row-key="id" @row-click="handleRowChange">
			<el-table-column label="序号" align="center" type="index" width="80">
				<template slot-scope="scope">
    				<span>{{(pageNum - 1) * pageSize + scope.$index + 1}}</span>
				</template>
			</el-table-column>
			<el-table-column prop="name" label="名称" align="center" min-width="20%">
			</el-table-column>
			<!-- <el-table-column prop="" label="价格" align="center" min-width="20%">
			</el-table-column> -->
			<el-table-column prop="thumimage" label="图片" align="center" min-width="20%">
				<template slot-scope="scope">
					<el-image style="width: 50px; height: 50px" :src="scope.row.thumimage" fit="cover" v-show="!!scope.row.thumimage"></el-image>
				</template>
			</el-table-column>
			<el-table-column prop="status" label="状态" :formatter="formatStatus" align="center" min-width="20%">
			</el-table-column>
			<el-table-column prop="remark" label="备注" align="center" min-width="20%">
			</el-table-column>
			<el-table-column label="操作" align="center" width="250">
				<template slot-scope="scope">
					<label class="hzpedit" @click.prevent="enableData(scope.$index, scope.row)">{{scope.row.status==="0"? '禁用' : '启用'}}</label>|
					<label class="hzpedit" @click.prevent="editData(scope.$index, scope.row)">编辑</label>|
					<label class="hzpedit" @click.prevent="nextStep(scope.$index, scope.row)">组合商品</label>
				</template>
			</el-table-column>
		</el-table>
		<!--分页-->
		<pagination :pageSize="pageSize" :pageNum="pageNum" :totalNum="totalNum" 
			@handlePageSizeChange="handlePageSizeChange" @handlePageCurrentChange="handlePageCurrentChange">
		</pagination>
		<!--编辑数据-->
        <el-dialog :title="dialogTitle + '数据'" :visible.sync="editDialogVisible" class="dialog-container" width="500px" @opened="openDialog">
			<el-form :model="formData" :rules="rules" ref="formData" label-width="100px" class="form-data" size="mini">
				<el-form-item label="名    称" prop="name">
					<el-input v-model="formData.name" placeholder="请输入" id="focus-ele"></el-input>
				</el-form-item>
				<!-- <el-form-item label="价    格" prop="price">
					<el-input v-model="formData.price" placeholder="请输入" @keyup.native="cash"></el-input>
				</el-form-item> -->
				<el-form-item label="图    片" prop="">
					<UploadFile :imgUrl="formData.thumimage" @getFileUrl="getFileUrl" ref='UploadFile'></UploadFile>
				</el-form-item>
				<el-form-item label="备    注" prop="">
					<el-input v-model="formData.remark" placeholder="请输入" type="textarea" :rows="4"></el-input>
				</el-form-item>
        	</el-form>
            <div slot="footer" class="dialog-footer">
              	<el-button @click="closeDialog" class="hzpbtn-close">关 闭</el-button>
                <el-button type="primary" @click="handleSubmit('formData')" v-loading="loading" class="hzpbtn-primary">保 存</el-button>
            </div>
        </el-dialog>
	</section>
</template>
<script>
	import { combinationList } from '../../api/api';
	import { addCombination } from '../../api/api';
	import { editPromotion } from '../../api/api';
	import { enableCombination } from '../../api/api';
	import util from '../../common/js/util';
	import emEnum from '../../common/js/emEnum';
	// 分页组件
	import Pagination from '../Pagination';
	// 上传文件组件
	import UploadFile from '../UploadFile';

	export default {
		components: {
			Pagination,
			UploadFile
		},
		data() {
			return {
				sysCnName: emEnum.cnName,  // 中文名称
				sysEnName: emEnum.enName,  // 英文名称

				loginId: '',   // 登录用户

				submitSortDisabled: true,  // 提交排序不可用

				pagePosition: '>',// 页面位置

				loading: false,
				containerHeight: 0,
				datas: [],
				// 分页
				pageSize: 100,
				pageNum: 1,
        		totalNum: 0,

				dialogTitle: '',   // 对话框标题 
				dialogBtnVal: '',  // 按钮值
				editDialogVisible: false,  // 编辑对话框
				formData: {
					cid: '',           
					name:'',           // 名称
					price: '',         // 价格
					highimage: '',     // 高清图
					thumimage: '',     // 缩略图
					remark: '',        // 备注
				},
				rules: {
					name: [
						{ required: true, message: '请输入组合名称', trigger: 'blur' }
					],
					price: [
						{ required: true, message: '请输入组合价格', trigger: 'blur' }
					]
				}
			}
		},
		methods: {
			// 状态显示转换
			formatStatus: function (row, column) {
				return row.status === "0"? "启用" : (row.status === "1"? "禁用" : "")
			},
			// 金额校验
			cash: function (){
				this.formData.price = this.formData.price.replace(/[^\.\d]/g,'');   // 清除"数字"和"."以外的字符
				this.formData.price = this.formData.price.replace(/^\./g, '');      // 第一个字符不能为.
				let temp = this.formData.price.replace(/\./g,'');  // 出现多个点则只去掉最后一个点
				if(this.formData.price.length >= temp.length + 2) {
					this.formData.price = this.formData.price.substr(0, this.formData.price.length - 1);
				}
			},
			// 获取列表
			getData: function () {
				let _this = this;
				// 执行操作
				_this.loading = true;
				_this.closeDialog();  // 关闭编辑对话框
				let para = {
					pageNum: _this.pageNum,
					pageSize: _this.pageSize
				};
				combinationList(para, _this)
				.then(res=>res.data)
				.then(data => {
					_this.loading = false;
					let { code, msg } = data;
					if (code !== "0") {
						_this.$message({
							message: msg,
							type: 'error'
						});
					} else {
						let { pageSize, pageNum, totalNum } = data.data;
						data = data.data.list;
						_this.datas = data;
						// 分页
						_this.pageSize = pageSize;
						_this.pageNum = pageNum;
						_this.totalNum = totalNum;
					}
				});
			},
			// 修改数据状态（禁用、启用）
			enableData: function (index, row) {
				let _this = this;
				_this.$confirm('确认' + (row.status === "0"? '禁用' : '启用') + '该组合？', '提示', {
					type: 'warning'
				}).then(() => {
					_this.loading = true;
					let para = {
						cid: row.id,
						status: row.status === "0"? '1' : '0'
					};
					enableCombination(para, _this)
					.then(res=>res.data)
					.then(data => {
						_this.loading = false;
						let { msg, code } = data;
						if (code !== "0") {
							_this.$message({
								message: msg,
								type: 'error'
							});
						} else {
							_this.$message({
								message: msg,
								type: 'success'
							});
							_this.getData();
						}
					});
				}).catch((error) => {
					if(error!=='cancel') {
						_this.$message({
							message: error.message? error.message : error,
							type: 'error'
						});
					}
				});
			},
			// 重置输入
			resetForm: function (formName) {
				this.$refs[formName].resetFields();
			},
			// 编辑数据
			editData: function (index, row) {
				let formName = 'formData';
				// 打开对话框
				this.editDialogVisible = true;
				// 默认值
				if(!row) {
					this.dialogTitle = "添加";
					this.dialogBtnVal = "上传图片";
					if(this.$refs[formName]) this.$refs[formName].resetFields();
					this.formData.cid = "";
					this.formData.name = "";
					// this.formData.price = "";
					this.formData.highimage = "";
					this.formData.thumimage = "";
					this.formData.remark = "";
				} else {
					this.dialogTitle = "编辑";
					this.dialogBtnVal = "重新上传";
					this.formData.cid = row.id;
					this.formData.name = row.name;
					// this.formData.price = row.price;  // 根据实际字段修改
					this.formData.highimage = row.highimage;
					this.formData.thumimage = row.thumimage;
					this.formData.remark = row.remark;
				}
			},
			// 打开编辑对话框
			openDialog: function () {
				// 改变组件的图片地址和按钮值
				this.$refs.UploadFile.changeFileUrl(this.formData.thumimage, this.dialogBtnVal);
				// 焦点放到获取焦点的输入框
				let ele = document.getElementById('focus-ele');
				if(!!ele) {
					ele.focus();
				}
			},
			// 关闭编辑对话框
			closeDialog: function () {
				this.editDialogVisible = false;
			},
			// 提交数据
			handleSubmit: function (formName) {
				this.$refs[formName].validate((valid) => {
          			if (valid) {
						let _this = this;
						_this.$confirm('确认' + _this.dialogTitle + '组合？', '提示', {
							type: 'warning'
						}).then(() => {
							_this.loading = true;
							let para = {
								name: _this.formData.name,
								// price: _this.formData.price,  // 根据实际字段修改
								highimage: _this.formData.highimage,
								thumimage: _this.formData.thumimage,
								remark: _this.formData.remark,
							};
							if(!!_this.formData.cid) para["cid"] = _this.formData.cid;  // 组合商品编号(传入为修改，不传为新增)

							addCombination(para, _this)
							.then(res=>res.data)
							.then(data => {
								_this.loading = false;
								let { msg, code } = data;
								if (code !== "0") {
									_this.$message({
										message: msg,
										type: 'error'
									});
								} else {
									_this.$message({
										message: msg,
										type: 'success'
									});
									_this.getData();
								}
							});
						}).catch((error) => {
							if(error!=='cancel') {
								_this.$message({
									message: error.message? error.message : error,
									type: 'error'
								});
							}
						});
					} else {
						return false;
					}
				});
			},
			// table 行改变事件
			handleRowChange: function (row, event, column) {
				//this.pagePosition = row.title + '>';
			},
			// 页面位置点击
			pagePositionClick: function () {
				return false;
			},
			// 下一步骤
			nextStep: function (index, row) {
				this.pagePosition = row.name + '>';
				this.$router.push({ path: '/combinationInventory', query: {cid: row.id, pp: this.pagePosition, ps: this.pageSize, pn: this.pageNum}});
			},
			// 导出Execl
			exportExcel: function () {
				let outTable = document.querySelector("#out-table");
				if(!!outTable) {
					// 获取文件名
					let fileName = this.sysCnName + "_" + util.formatDate.format(new Date(), "yyyy-MM-dd HH:mm:ss") + "_" + this.$root.$route.name;
					// 导出Execl
					util.exportExcel(fileName, outTable);
				} else {
					this.$message({
						message: "未找到需要导出数据的表格",
						type: 'warning'
					});
				}
			},
			// 选择图片
			choseImage: function (file) {
				this.formData.thumimage = file.url;
			},
			// 获取文件路径
			getFileUrl: function (high, thum) {
				this.formData.highimage = high;
				this.formData.thumimage = thum;
			},
			// 分页事件
			handlePageSizeChange(val) {
				this.pageSize = val;
				this.getData();
			},
			handlePageCurrentChange(val) {
				this.pageNum = val;
				this.getData();
			}
		},
		// 创建VUE实例后的钩子
		created: function () {
			// 登录ID
			this.loginId = util.userData.getLoginUserFieldValue(emEnum.loginUserFileds.uid, this);
			// 获取参数值
			let query = this.$route.query;
			if(!!query && !!query.ps && !!query.pn) {
				this.pageSize = parseInt(query.ps);
				this.pageNum = parseInt(query.pn);
			}
		},
		// 挂载到DOM后的钩子
		mounted: function () {
			let _this = this;
			_this.containerHeight = _this.$refs.combinationcontainer.offsetHeight;
			// 获取用户
			_this.getData();
			// 绑定窗口尺寸改变事件改变数据表的高度
			window.onresize = () => {
				return (() => {
					// 为了避免频繁触发resize函数导致页面卡顿，使用定时器
					if (!this.timer && !!_this.$refs.combinationcontainer) {
						this.timer = true
						_this.containerHeight = _this.$refs.combinationcontainer.offsetHeight;
						let that = this
						setTimeout(function () {
							that.timer = false
						}, 100);
					}
				})();
			}
		}
	};
</script>

<style lang="scss">
.combination-container {
	height: calc(100vh - 120px);
}
</style>