<template>
	<section class="combinationInventory-container common-container" ref="combinationInventorycontainer">
		<!--编辑促销标签-->
		<el-row style="width: 100%; height: 30px;">
			<el-col :span="12">
				<el-button type="primary" @click="preStep" class="hzpbtn-primary" style="height: 35px; line-height: 10px;">返回到组合清单</el-button>
			</el-col>
			<el-col :span="12">
				<el-button type="info" plain @click="pagePositionClick" class="hzpbtn-info" style="float: right;height: 35px; line-height: 10px; margin-right: 2px;">{{ pagePosition }}</el-button>
				<div style="float: right; width: 100px; font-size: 20px; margin-top: 4px; margin-right: -5px;">
					<label>上级位置：</label>
				</div>
			</el-col>
		</el-row>
		<!--增加标签商品-->
		<el-form :model="addFormData" :rules="addRules" ref="addFormData" label-width="100px" class="form-data-nopadding" size="mini">
			<el-row style="width: 100%; height: 40px; line-height: 40px;">
				<el-col :span="24">
					<el-form-item label="选择商品" prop="spcode">
						<el-row>
							<el-col :span="22">
								<el-select v-model="addFormData.spcode" placeholder="请选择" filterable remote :remote-method="searchSpList">
									<el-option
										v-for="item in spDatas"
										:key="item.code"
										:label="item.name"
										:value="item.code">
									</el-option>
								</el-select>
							</el-col>
							<el-col :span="2">
								<el-tooltip content="清除选择的商品" placement="top" effect="light">
									<el-button @click="clearInventory('sp')" icon="el-icon-close"></el-button>
								</el-tooltip>
							</el-col>
						</el-row>
					</el-form-item>
				</el-col>
				<!-- <el-col :span="6">
					<el-form-item label="商品数量" prop="spsize">
						<el-input v-model="addFormData.spsize" placeholder="请输入" @keyup.native="number"></el-input>
					</el-form-item>
				</el-col> -->
			</el-row>
			<el-row style="width: 100%; height: 40px; line-height: 40px;">
				<el-col :span="12">
					<el-form-item label="" prop="">
						<el-button type="primary" @click="handleAddSubmit('addFormData')" class="hzpbtn-primary" v-loading="loading" :disabled="!cid">添加</el-button>
						<el-button type="primary" @click="exportExcel" class="hzpbtn-primary" style="margin-left: 50px;">导出</el-button>
					</el-form-item>
				</el-col>
				<el-col :span="11">
					<el-form-item label="" prop="">
						<el-input placeholder="商品快速搜索" v-model="vagueContent" style="" @change="searchList">
							<el-button slot="append" icon="el-icon-search"></el-button>
						</el-input>
					</el-form-item>
				</el-col>
			</el-row>
		</el-form>
		<!--列表-->
		<el-table :data="datas" :stripe="true" :height="containerHeight - 55 - 95" highlight-current-row v-loading="loading" id="out-table" class="data-content" border row-key="id">
			<el-table-column label="序号" align="center" type="index" width="80">
				<template slot-scope="scope">
    				<span>{{(pageNum - 1) * pageSize + scope.$index + 1}}</span>
				</template>
			</el-table-column>
			<el-table-column prop="spcode" label="商品编码" align="center" min-width="20%">
			</el-table-column>
			<el-table-column prop="spname" label="商品名称" align="center" min-width="20%">
			</el-table-column>
			<el-table-column prop="spmoney" label="商品价格" align="center" min-width="20%">
			</el-table-column>
			<el-table-column label="操作" align="center" width="150">
				<template slot-scope="scope">
					<label class="hzpedit" @click.prevent="enableData(scope.$index, scope.row)">{{scope.row.status==='0'? '禁用' : '启用'}}</label>|
					<label class="hzpedit" @click.prevent="editData(scope.$index, scope.row)">编辑</label>
				</template>
			</el-table-column>
		</el-table>
		<!--编辑数据-->
        <el-dialog :title="dialogTitle + '数据'" :visible.sync="editDialogVisible" class="dialog-container" width="500px" @opened="openDialog">
			<el-form :model="formData" :rules="rules" ref="formData" label-width="100px" class="form-data" size="mini">
				<el-form-item label="选择商品" prop="spcode">
					<el-row>
						<el-col :span="20">
							<el-select v-model="formData.spcode" placeholder="请选择" filterable remote :remote-method="searchSpList" id="focus-ele">
								<el-option
									v-for="item in spDatas"
									:key="item.code"
									:label="item.name"
									:value="item.code">
								</el-option>
							</el-select>
						</el-col>
						<el-col :span="4" style="width: 20px;">
							<el-tooltip content="清除选择的商品" placement="top" effect="light">
								<el-button @click="clearInventory('editsp')" icon="el-icon-close"></el-button>
							</el-tooltip>
						</el-col>
					</el-row>
				</el-form-item>
				<!-- <el-form-item label="商品数量" prop="spsize">
					<el-input v-model="formData.spsize" placeholder="请输入" @keyup.native="editnumber"></el-input>
				</el-form-item> -->
        	</el-form>
            <div slot="footer" class="dialog-footer">
              	<el-button @click="closeDialog" class="hzpbtn-close">关 闭</el-button>
                <el-button type="primary" @click="handleSubmit('formData')" v-loading="loading" class="hzpbtn-primary">保 存</el-button>
            </div>
        </el-dialog>
	</section>
</template>
<script>
	// 标签商品
	import { combinationinventoryList } from '../../api/api';
	import { addCombinationinventory } from '../../api/api';
	import { editProcesslabelInventory } from '../../api/api';
	import { enablecombinationinventory } from '../../api/api';
	// 商品筛选
	import { inventoryVague } from '../../api/api';
	import util from '../../common/js/util';
	import emEnum from '../../common/js/emEnum';
	// 行列拖拽
	import Sortable from 'sortablejs';

	export default {
		data() {
			return {
				sysCnName: emEnum.cnName,  // 中文名称
				sysEnName: emEnum.enName,  // 英文名称

				loginId: '',   // 登录用户

				cid: '',       // 组合id
				prePagePosition: '', // 前序页面位置
				cpageSize: 100,
				cPageNum: 1,
				pagePosition: '>',  // 页面位置

				loading: false,
				containerHeight: 0,
				datas: [],     // 显示数据
				allDatas: [],  // 全数据

				// 添加标签商品
				addFormData: {
					ciid: '',
					cid:'',
					spcode: '',  // 商品code
					spsize: '',  // 商品size
				},
				addRules: {
					spcode: [
						{ required: true, message: '请选择商品', trigger: 'change' }
					],
					spsize: [
						{ required: true, message: '请输入商品数量', trigger: 'blur' }
					]
				},
				vagueContentSp: '',  // 商品筛选
				spDatas: [],         // 商品集合
				// 编辑标签商品
				dialogTitle: '',     // 对话框标题 
				editDialogVisible: false,  // 编辑对话框
				formData: {
					ciid: '',
					cid: '',
					spcode:'',      // 商品code
					spsize:'',      // 商品size
				},
				rules: {
					spcode: [
						{ required: true, message: '请选择商品', trigger: 'change' }
					],
					spsize: [
						{ required: true, message: '请输入商品数量', trigger: 'blur' }
					]
				},

				vagueContent: '',    // 快速搜索内容
			}
		},
		methods: {
			// 数字校验
			number(){
				this.addFormData.spsize = this.addFormData.spsize.replace(/[^\.\d]/g,'');
				this.addFormData.spsize = this.addFormData.spsize.replace('.','');
				if(!!this.addFormData.spsize && parseInt(this.addFormData.spsize) === 0) {  // 数量必须大于0
					this.addFormData.spsize = 1;
				}
			},
			editnumber(){
				this.formData.spsize = this.formData.spsize.replace(/[^\.\d]/g,'');
				this.formData.spsize = this.formData.spsize.replace('.','');
				if(!!this.formData.spsize && parseInt(this.formData.spsize) === 0) {  // 数量必须大于0
					this.formData.spsize = 1;
				}
			},
			// 获取列表
			getData: function () {
				let _this = this;
				if(!!_this.cid) {
					// 执行操作
					_this.loading = true;
					_this.closeDialog();  // 关闭编辑对话框
					let para = {
						cid: _this.cid,
					};
					combinationinventoryList(para, _this)
					.then(res=>res.data)
					.then(data => {
						_this.loading = false;
						let { code, msg } = data;
						if (code !== "0") {
							_this.$message({
								message: msg,
								type: 'error'
							});
						} else {
							data = data.data.list;
							_this.allDatas = data;
							_this.datas = _this.allDatas;
						}
					});
				}
			},
			// 搜索数据
			searchSpList: function (query) {
				let _this = this;
				// 执行操作
				_this.vagueContentSp = query;
				_this.loading = true;
				let para = {
					vague: _this.vagueContentSp,
				};
				inventoryVague(para, _this)
				.then(res=>res.data)
				.then(data => {
					_this.loading = false;
					let { code, msg } = data;
					if (code !== "0") {
						_this.$message({
							message: msg,
							type: 'error'
						});
					} else {
						data = data.data.list;
						_this.spDatas = data.filter(item => item.status === 0);
					}
				});
			},
			// 前端搜索数据
			searchList: function () {
				let _this = this;
				// 如果有搜索数据，则模糊搜索
				if(!!_this.vagueContent) {
					_this.datas = _this.allDatas.filter(item => item.spname.indexOf(_this.vagueContent) >= 0 || item.spcode.indexOf(_this.vagueContent) >= 0);
				} else {
					_this.datas = _this.allDatas;
				}
			},
			// 修改数据状态（禁用、启用）
			enableData: function (index, row) {
				let _this = this;
				_this.$confirm('确认' + (row.status === '0'? '禁用' : '启用') + '该组合商品？', '提示', {
					type: 'warning'
				}).then(() => {
					_this.loading = true;
					let para = {
						ciid: row.id,
						status: row.status === '0'? '1' : '0'
					};
					enablecombinationinventory(para, _this)
					.then(res=>res.data)
					.then(data => {
						_this.loading = false;
						let { msg, code } = data;
						if (code !== "0") {
							_this.$message({
								message: msg,
								type: 'error'
							});
						} else {
							_this.$message({
								message: msg,
								type: 'success'
							});
							_this.getData();
						}
					});
				}).catch((error) => {
					if(error!=='cancel') {
						_this.$message({
							message: error.message? error.message : error,
							type: 'error'
						});
					}
				});
			},
			// 重置输入
			resetForm: function (formName) {
				this.$refs[formName].resetFields();
			},
			// 编辑数据
			editData: function (index, row) {
				let formName = 'formData';
				// 打开对话框
				this.editDialogVisible = true;
				// 默认值
				if(!row) {
					this.dialogTitle = "添加";
					if(this.$refs[formName]) this.$refs[formName].resetFields();
					this.formData.ciid = "";
					this.formData.cid = "";
					this.formData.spcode = "";
				} else {
					this.dialogTitle = "编辑";
					this.formData.ciid = row.id;
					this.formData.cid = this.cid;
					this.formData.spcode = row.spcode;
					// 如果没有则在集合中新增
					if(!!row.spcode && !this.spDatas.some(a => a.code === row.spcode)) {
						this.spDatas.push({code: row.spcode, name: row.spname});
					}
				}
			},
			// 打开编辑对话框
			openDialog: function () {
				// 焦点放到获取焦点的输入框
				let ele = document.getElementById('focus-ele');
				if(!!ele) {
					ele.focus();
				}
			},
			// 关闭编辑对话框
			closeDialog: function () {
				this.editDialogVisible = false;
			},
			// 提交数据
			// 新增
			handleAddSubmit: function (formName) {
				this.$refs[formName].validate((valid) => {
          			if (valid) {
						let _this = this;
						_this.$confirm('确认添加组合商品？', '提示', {
							type: 'warning'
						}).then(() => {
							_this.loading = true;
							let para = {
								cid: _this.cid,
								spcode: _this.addFormData.spcode,
								// spsize: _this.addFormData.spsize,  // 根据实际数据修改
							};
							addCombinationinventory(para, _this)
							.then(res=>res.data)
							.then(data => {
								_this.loading = false;
								let { msg, code } = data;
								if (code !== "0") {
									_this.$message({
										message: msg,
										type: 'error'
									});
								} else {
									_this.$message({
										message: msg,
										type: 'success'
									});
									// 清空数据
									_this.addFormData.ciid = '';
									_this.addFormData.spcode = '';
									_this.addFormData.spsize = '';
									// 获取数据
									_this.getData();
								}
							});
						}).catch((error) => {
							if(error!=='cancel') {
								_this.$message({
									message: error.message? error.message : error,
									type: 'error'
								});
							}
						});
					} else {
						return false;
					}
				});
			},
			// 编辑
			handleSubmit: function (formName) {
				this.$refs[formName].validate((valid) => {
          			if (valid) {
						let _this = this;
						_this.$confirm('确认' + _this.dialogTitle + '组合商品？', '提示', {
							type: 'warning'
						}).then(() => {
							_this.loading = true;
							let para = {
								cid: _this.cid,
								spcode: _this.formData.spcode,
								// spsize: _this.formData.spsize,   // 根据实际数据修改
							};
							if(!!_this.formData.ciid) para["ciid"] = _this.formData.ciid;  // 商品组合商品编号(传入为修改，不传为新增)

							addCombinationinventory(para, _this)
							.then(res=>res.data)
							.then(data => {
								_this.loading = false;
								let { msg, code } = data;
								if (code !== "0") {
									_this.$message({
										message: msg,
										type: 'error'
									});
								} else {
									_this.$message({
										message: msg,
										type: 'success'
									});
									_this.getData();
								}
							});
						}).catch((error) => {
							if(error!=='cancel') {
								_this.$message({
									message: error.message? error.message : error,
									type: 'error'
								});
							}
						});
					} else {
						return false;
					}
				});
			},
			// 页面位置点击
			pagePositionClick: function () {
				return false;
			},
			// 上一步骤
			preStep: function () {
				this.$router.push({ path: '/combination', query: {ps: this.cPageSize, pn: this.cPageNum}});
			},
			// 导出Execl
			exportExcel: function () {
				let outTable = document.querySelector("#out-table");
				if(!!outTable) {
					// 获取文件名
					let fileName = this.sysCnName + "_" + util.formatDate.format(new Date(), "yyyy-MM-dd HH:mm:ss") + "_" + this.$root.$route.name;
					// 导出Execl
					util.exportExcel(fileName, outTable);
				} else {
					this.$message({
						message: "未找到需要导出数据的表格",
						type: 'warning'
					});
				}
			},
			// 清除选择的商品
			clearInventory: function (val) {
				if(val == 'sp') {
					this.addFormData.spcode = '';
				} else if(val == 'editsp') {
					this.formData.spcode = '';
				}
			}
		},
		// 创建VUE实例后的钩子
		created: function () {
			// 登录ID
			this.loginId = util.userData.getLoginUserFieldValue(emEnum.loginUserFileds.uid, this);
			// 获取参数值
			let query = this.$route.query;
			this.cid = query.cid;
			this.prePagePosition = query.pp;
			this.pagePosition = this.prePagePosition;
			this.cPageSize = query.ps;
			this.cPageNum = query.pn;
		},
		// 挂载到DOM后的钩子
		mounted: function () {
			let _this = this;
			_this.containerHeight = _this.$refs.combinationInventorycontainer.offsetHeight;
			// 获取用户
			_this.getData();
			// 绑定窗口尺寸改变事件改变数据表的高度
			window.onresize = () => {
				return (() => {
					// 为了避免频繁触发resize函数导致页面卡顿，使用定时器
					if (!this.timer && !!_this.$refs.combinationInventorycontainer) {
						this.timer = true
						_this.containerHeight = _this.$refs.combinationInventorycontainer.offsetHeight;
						let that = this
						setTimeout(function () {
							that.timer = false
						}, 100);
					}
				})();
			}
		}
	};
</script>

<style lang="scss">
.combinationInventory-container {
	height: calc(100vh - 120px);
	.plTitle {
		font-size: 18px;
	}
}
</style>