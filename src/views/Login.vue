<template>
  <el-form :model="ruleForm" :rules="rules" ref="ruleForm" label-position="left" label-width="0px" class="login-container" :loading="logining">
    <div class="login-title">
      <div class="login-platname">
        <div class="login-cn-platname">{{sysCnName}}</div>
        <div class="login-en-platname">{{sysEnName}}</div>
      </div>
    </div>
    <div class="login-content">
      <el-form-item prop="account" style="margin-bottom: 35px;">
        <i class="el-icon-user login-icon"></i>
        <el-input v-model="ruleForm.account" placeholder="账户" id="focus-ele" class="login-input"></el-input>
      </el-form-item>
      <el-form-item prop="checkPass">
        <i class="el-icon-lock login-icon"></i>
        <el-input type="password" v-model="ruleForm.checkPass" placeholder="密码" class="login-input"></el-input>
      </el-form-item>
      <el-form-item prop="">
        <el-checkbox v-model="ruleForm.autoLogin" class="login-auto">自动登录</el-checkbox>
      </el-form-item>
      <el-form-item style="width:100%;">
        <el-button type="primary" class="login-btn" @click.native.prevent="handleSubmit" @keyup.enter.native="handleSubmit" :loading="logining">登 录</el-button>
      </el-form-item>
    </div>
  </el-form>
</template>

<script>
  import { requestLogin } from '../api/api';
  import util from '../common/js/util';
  import emEnum from '../common/js/emEnum';

  export default {
    data() {
      return {
        sysCnName: emEnum.cnName,  // 中文名称
				sysEnName: emEnum.enName,  // 英文名称

        logining: false,
        ruleForm: {
          account: '',
          checkPass: '',
          autoLogin: false  // 是否自动登录
        },
        rules: {
          account: [
            { required: true, message: '请输入用户名', trigger: 'blur' },
          ],
          checkPass: [
            { required: true, message: '请输入密码', trigger: 'blur' },
          ]
        }
      };
    },
    methods: {
      // 重置
      handleReset() {
        this.$refs.ruleForm.resetFields();
        // 焦点放到要获取焦点的输入框
        let ele = document.getElementById('focus-ele');
        if(!!ele) {
          ele.focus();
        }
      },
      // 登录
      handleSubmit(ev) {
        var _this = this;
        if(!!_this.$refs.ruleForm) {
          _this.$refs.ruleForm.validate((valid) => {
            if (valid) {
              _this.logining = true;
              // 如果user存在则移除再等录
              util.userData.clearSessionStorage();
              // 等录参数
              var loginParams = { 
                username: _this.ruleForm.account, 
                password: _this.ruleForm.checkPass 
              };
              requestLogin(loginParams, _this)
              .then(res=>res.data)
              .then(data => {
                _this.logining = false;
                let { msg, code } = data;
                if (code !== "0") {
                  _this.$message({
                    message: msg,
                    type: 'error'
                  });
                } else {
                  data = data.data;
                  // 判断是否可以登录
                  if(emEnum.allowLoginRole.indexOf(data.role) > -1) {
                    sessionStorage.setItem('user', JSON.stringify(data));  // 记录user
                    // 根据用户信息中密码是否为初始密码决定打开修改密码还是打开用户管理
                    if(!!data.pwdOriginal && data.pwdOriginal == "1") {
                      _this.$router.push({ path: '/changePwd' });
                    } else {
                      if(data.role === 1) _this.$router.push({ path: '/user' });
                      else if(data.role === 2) _this.$router.push({ path: '/order' });
                      else if(data.role === 3) _this.$router.push({ path: '/order' });
                      else if(data.role === 4) _this.$router.push({ path: '/logistics' });
                    }
                  } else {
                    _this.$message({
                      message: '当前角色不允许登录，请联系管理员！',
                      type: 'warning'
                    });
                  }
                }
              });
            } else {
              return false;
            }
          });
        }
      }
    },
    mounted(){
      // 重置输入
      this.handleReset();      
    },
    created:function(){
      // 登录添加键盘事件,注意,不能直接在焦点事件上添加回车
      let _this = this;
      document.onkeydown = function (e) {
        let key = window.event.keyCode;
        if (key === 13){
          _this.handleSubmit();//方法
        }
      }
    }
  }
</script>

<style lang="scss" scoped>
  .login-container {
    -webkit-border-radius: 5px;
    border-radius: 5px;
    -moz-border-radius: 5px;
    background-clip: padding-box;
    margin: 180px auto auto auto;
    width: 500px;    
    padding: 15px 35px;
    background: transparent;
    .login-title {
      text-align: center;
      color: #505458;
      font-weight: bold;
      padding-bottom: 80px;
      letter-spacing: 1.2px;
      .login-platname {
        width: 100%;
        height: 50px;
        line-height: 65px;
        color: #000;
      }
      .login-cn-platname {
        width: 100%;
        height: 30px;
        font-size: 40px;
        padding-bottom: 10px;
      }
      .login-en-platname {
        width: 100%;
        height: 30px;
        font-size: 20px;
      }
    }
    .login-content {
      width: 80%;
      margin-left: 10%;
      .login-icon {
        position: absolute;
        top: 11px;
        font-size: 18px;
        color: #C0C4CC;
        padding-left: 10px;
        z-index: 99;
      }
      .login-auto {
      }
      .login-input{
      }
      .login-btn {
        width: 100%;
        background: #2935D5;
      }
    }
  }
</style>