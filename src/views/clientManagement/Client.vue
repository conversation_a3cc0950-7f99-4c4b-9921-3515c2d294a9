<template>
	<section class="client-container common-container" ref="clientcontainer">
		<!--操作-->
		<section class="data-oper">
			<el-row>
				<el-col :span="6" align="left">
					<el-button type="primary" @click="editData" class="hzpbtn-primary" v-loading="loading">添加</el-button>
					<el-button type="primary" @click="exportExcel" class="hzpbtn-primary">导出</el-button>
					<el-button type="primary" @click="editCol" class="hzpbtn-primary" style="margin-left: 30px;">设置显示列</el-button>
				</el-col>
				<el-col :span="2" align="left">
					<el-select style="width: 98%;" v-model="sortAreaId" placeholder="请选择区域" @change="areaFilter">
						<el-option
							v-for="item in allAreaDatas"
							:key="item.id"
							:label="item.areaname"
							:value="item.id">
						</el-option>
					</el-select>
				</el-col>
				<el-col :span="3" align="right">
					<el-date-picker clearable style="width: 98%;" v-model="startTime" type="date" placeholder="注册时间起" :picker-options="startPickerOptions" @change="createTimeChange"> </el-date-picker>
				</el-col>
				<el-col :span="3" align="left">
					<el-date-picker clearable style="width: 98%;" v-model="endTime" type="date" placeholder="注册时间止" :picker-options="endPickerOptions" @change="createTimeChange"> </el-date-picker>
				</el-col>
				<el-col :span="2" align="right">
					<el-select clearable style="width: 98%;" v-model="statusKey" placeholder="请选择启用、禁用状态" @change="areaFilter">
						<el-option
							v-for="item in statusOptions"
							:key="item.key"
							:label="item.value"
							:value="item.key">
						</el-option>
					</el-select>
				</el-col>
				<el-col :span="3" align="right">
					<el-input clearable style="width: 98%;" placeholder="快速搜索" v-model="vagueContent" @change="searchList">
						<el-button slot="append" icon="el-icon-search"></el-button>
					</el-input>
				</el-col>
				<el-col :span="3" align="right">
					<el-button type="primary" @click="downloadTemplate" class="hzpbtn-primary" v-loading="loading">下载导入模板</el-button>
				</el-col>
				<el-col :span="2" align="center">
					<a href="javascript:;" class="file importFile">导入Excel
						<input id="file" type="file" @change='importExcel($event)' accept=".xlsx, .xls, .csv" />
					</a>
				</el-col>
			</el-row>
		</section>
		<!--列表-->
		<el-table :data="datas" :stripe="true" :height="containerHeight - 85" highlight-current-row v-loading="loading" id="out-table" class="data-content" border style="width: 100%">
			<af-table-column label="序号" align="center" type="index" width="60">
				<template slot-scope="scope">
    				<span>{{(pageNum - 1) * pageSize + scope.$index + 1}}</span>
				</template>
			</af-table-column>
			<af-table-column prop="code" label="客户编码" align="center" v-if="colUnShowKeys.indexOf('code') === -1">
			</af-table-column>
			<af-table-column prop="reallyname" label="客户姓名" align="center" v-if="colUnShowKeys.indexOf('reallyname') === -1">
			</af-table-column>
			<af-table-column prop="areaname" label="所属团队" align="center" v-if="colUnShowKeys.indexOf('areaname') === -1">
			</af-table-column>
			<af-table-column prop="aagencyname" label="培训公司" align="center" v-if="colUnShowKeys.indexOf('aagencyname') === -1">
			</af-table-column>
			<af-table-column prop="pagencyname" label="销售公司" align="center" v-if="colUnShowKeys.indexOf('pagencyname') === -1">
			</af-table-column>
			<af-table-column prop="cagencyname" label="代理1" align="center" v-if="colUnShowKeys.indexOf('cagencyname') === -1">
			</af-table-column>
			<af-table-column prop="tagencyname" label="代理2" align="center" v-if="colUnShowKeys.indexOf('tagencyname') === -1">
			</af-table-column>
			<af-table-column prop="province" label="省" align="center" v-if="colUnShowKeys.indexOf('province') === -1">
			</af-table-column>
			<af-table-column prop="city" label="市" align="center" v-if="colUnShowKeys.indexOf('city') === -1">
			</af-table-column>
			<af-table-column prop="district" label="县（区）" align="center" v-if="colUnShowKeys.indexOf('district') === -1">
			</af-table-column>
			<af-table-column prop="address" label="详细地址" align="center" v-if="colUnShowKeys.indexOf('address') === -1">
			</af-table-column>
			<af-table-column prop="linkphone" label="联系电话" align="center" v-if="colUnShowKeys.indexOf('linkphone') === -1">
			</af-table-column>
			<af-table-column prop="agencyrole" label="代理级别" :formatter="formatAgencyRole" align="center" v-if="colUnShowKeys.indexOf('agencyrole') === -1">
			</af-table-column>
			<af-table-column prop="status" label=" 状态 " :formatter="formatStatus" align="center" v-if="colUnShowKeys.indexOf('status') === -1">
			</af-table-column>
			<af-table-column prop="creattime" label="注册时间" :formatter="formatCreatTime" align="center" v-if="colUnShowKeys.indexOf('creattime') === -1">
			</af-table-column>
			<af-table-column label="操作" align="center" width="200" fixed="right" :key="Math.random()">
				<template slot-scope="scope">
					<label class="hzpedit" @click.prevent="delData(scope.$index, scope.row)" v-if="role === 1 || (scope.row.status !== 0 && scope.row.status !== 1)">删除</label>
					<span v-if="role === 1 || (scope.row.status !== 0 && scope.row.status !== 1)">|</span>
					<label class="hzpedit" @click.prevent="enableData(scope.$index, scope.row)">{{ (scope.row.status===1 || scope.row.status===2? '启用' : '禁用') }}</label>|
					<label class="hzpedit" @click.prevent="editData(scope.$index, scope.row)">编辑</label>
				</template>
			</af-table-column>
		</el-table>
		<!--分页-->
		<pagination :pageSize="pageSize" :pageNum="pageNum" :totalNum="totalNum" 
			@handlePageSizeChange="handlePageSizeChange" @handlePageCurrentChange="handlePageCurrentChange">
		</pagination>
		<!--编辑数据-->
        <el-dialog title="设置显示列" :visible.sync="colDialogVisible" class="dialog-container" width="600px" @close="closeColSet">
			<el-transfer v-model="colUnShowKeys" :data="colDatas" :titles="['显示列', '不显示列']" style="width: 500px; margin: 15px auto;"></el-transfer>
        </el-dialog>
		<!--编辑数据-->
        <el-dialog :title="dialogTitle + '数据'" :visible.sync="editDialogVisible" class="dialog-container" width="500px" height="500px" @opened="openDialog">
			<el-form :model="formData" :rules="rules" ref="formData" label-width="100px" class="form-data" size="mini">
				<el-form-item label="客户编码" prop="">
					<el-input v-model="formData.code" placeholder="请输入" :disabled="codeDisabled"></el-input>
				</el-form-item>
				<el-form-item label="客户姓名" prop="reallyname">
					<el-input v-model="formData.reallyname" placeholder="请输入" id="focus-ele"></el-input>
				</el-form-item>
				<el-form-item label="所属团队" prop="areaid">
					<el-select v-model="formData.areaid" placeholder="请选择">
						<el-option
							v-for="item in areaDatas"
							:key="item.areaId"
							:label="item.areaName"
							:value="item.areaId">
						</el-option>
					</el-select>
				</el-form-item>
				<el-form-item label="代理级别" prop="agencyrole">
					<el-select clearable v-model="formData.agencyrole" placeholder="请选择" @change="roleChange">
						<el-option
							v-for="item in agencyRoleOptions"
							:key="item.key"
							:label="item.value"
							:value="item.key">
						</el-option>
					</el-select>
				</el-form-item>
				<el-form-item label="培训公司" prop="aagencyid" v-show="agencyShow">
					<el-select v-model="formData.aagencyid" placeholder="请选择">
						<el-option
							v-for="item in agencyDatas"
							:key="item.aagencyid"
							:label="item.aagencyname"
							:value="item.aagencyid">
						</el-option>
					</el-select>
				</el-form-item>
				<el-form-item label="销售公司" prop="pagencyname" v-show="pagencyShow">
					<el-input v-model="formData.pagencyname" placeholder="请输入"></el-input>
				</el-form-item>
				<el-form-item label="代 理 1" prop="cagencyname" v-show="cagencyShow">
					<el-input v-model="formData.cagencyname" placeholder="请输入"></el-input>
				</el-form-item>
				<el-form-item label="代 理 2" prop="tagencyname" v-show="tagencyShow">
					<el-input v-model="formData.tagencyname" placeholder="请输入"></el-input>
				</el-form-item>
				<el-form-item label="联系电话" prop="linkphone">
					<el-input v-model="formData.linkphone" placeholder="请输入"></el-input>
				</el-form-item>
				<el-form-item label="省市县(区)" prop="">
					<region-picker :placeholder="{province: '选择省份', city: '选择市', district: '选择县（区）'}" 
						:province="formData.region.province" :city="formData.region.city" :district="formData.region.district" @onchange="regionChange">
					</region-picker>
				</el-form-item>
				<el-form-item label="详细地址" prop="">
					<el-input v-model="formData.address" placeholder="请输入" type="textarea" :rows="4"></el-input>
				</el-form-item>
        	</el-form>
            <div slot="footer" class="dialog-footer">
              	<el-button @click="closeDialog" class="hzpbtn-close">关 闭</el-button>
                <el-button type="primary" @click="handleSubmit('formData')" v-loading="loading" class="hzpbtn-primary">保 存</el-button>
            </div>
        </el-dialog>
	</section>
</template>
<script>
	import { getAreaConfig } from '../../api/api';
	import { areabyroleList } from '../../api/api';
	import { clientVague } from '../../api/api';
	import { getClientList } from '../../api/api';
	import { addClient } from '../../api/api';
	import { editClient } from '../../api/api';
	import { enableClient } from '../../api/api';
	import { deleteClient } from '../../api/api';
	import { batchAddClient, getClientImportTemplate } from '../../api/api';
	import util from '../../common/js/util';
	import emEnum from '../../common/js/emEnum';
	// 分页组件
	import Pagination from '../Pagination';

	export default {
		components: {
			Pagination
		},
		data() {
			return {
				sysCnName: emEnum.cnName,  // 中文名称
				sysEnName: emEnum.enName,  // 英文名称
				pageName: 'client',  // 页面名称

				role: '',      // 登录用户权限
				loginId: '',   // 登录用户
				loginAreaid: '',  // 登录区域

				loading: false,
				containerHeight: 0,
				datas: [],
				// 分页
				pageSize: 100,
				pageNum: 1,
				totalNum: 0,
				
				allAreaDatas: [],  // 带全部的区域
				areaDatas: [],     // 区域数据
				agencyDatas: [],   // 培训公司数据
				agencyRoleOptions: emEnum.agencyRoleOptions, // 代理2角色集合
				statusOptions: emEnum.statusOptions,         // 状态集合
				statusKey: '',     // 状态

				startTime: '',  // 注册时间起
				endTime: '',    // 注册时间止
				orderStartStamp: '',  // 开始时间戳
				orderEndStamp: '',    // 结束时间戳
				startPickerOptions: {
					disabledDate: time => {
						if(!!this.endTime) {
							return time.getTime() > new Date(this.endTime);
						}
						return false;
					}
				},  // 注册时间起时间范围
				endPickerOptions: {
					disabledDate: time => {
						if(!!this.startTime) {
							return time.getTime() < new Date(this.startTime);
						}
						return false;
					}
				},  // 注册时间止时间范围

				dialogTitle: '',   // 对话框标题 
				editDialogVisible: false,  // 编辑对话框
				codeDisabled: true,  // 编码不可编辑

				agencyShow: true,    // 培训公司是否可见
				pagencyShow: true,   // 销售公司是否可见
				cagencyShow: true,   // 代理1是否可见
				tagencyShow: true,   // 代理2是否可见
				
				sortAreaId: '',       // 搜索区域
				vagueContent: '',    // 快速搜索内容

				formData: {
					cid: '',         // 选中客户ID
					code: '',        // 客户编码
					reallyname: '',  // 客户名称
					areaid: '',      // 区域id
					aagencyid: '',   // 区域代理uid
					agencyrole: '',  // 代理级别
					aagencyname: '', // 培训公司
					pagencyname: '', // 销售公司
					cagencyname: '', // 代理1
					tagencyname: '', // 代理2
					linkphone: '',   // 联系电话
					region: {
						province: '',  // 省份
						city: '',      // 市
						district: '',  // 县（区）
					},  // 省市县（区）
					address: '',     // 详细地址
				},
				rules: {
					code: [
						{ required: true, message: '请输入客户编码', trigger: 'blur' }
					],
					reallyname: [
						{ required: true, message: '请输入客户名称', trigger: 'blur' }
					],
					areaid: [
						{ required: true, message: '请选择所属区域', trigger: 'change' }
					],
					// agencyrole: [
					// 	{ required: true, message: '请选择代理级别', trigger: 'change' }
					// ],
					aagencyid: [
						{ required: true, message: '请选择培训公司', trigger: 'blur,change' }
					],
					pagencyname: [
						{ required: true, message: '请输入销售公司', trigger: 'blur' }
					],
					cagencyname: [
						{ required: true, message: '请输入代理1', trigger: 'blur' }
					],
					tagencyname: [
						{ required: true, message: '请输入代理2', trigger: 'blur' }
					],
					linkphone: [
						{ required: true, message: '请输入联系电话', trigger: 'blur' }
					],
				},
				fixedPos: 'right',  // 停靠位置
				// 显示列选择相关
				colDialogVisible: false,  // 设置显示列对话框
				colDatas: [{key: 'code', label: '客户编码'},
						{key: 'reallyname', label: '客户姓名'},
						{key: 'areaname', label: '所属区域'},
						{key: 'aagencyname', label: '培训公司'},
						{key: 'pagencyname', label: '销售公司'},
						{key: 'cagencyname', label: '代理1'},
						{key: 'tagencyname', label: '代理2'},
						{key: 'province', label: '省'},
						{key: 'city', label: '市'},
						{key: 'district', label: '县（区）'},
						{key: 'address', label: '详细地址'},
						{key: 'linkphone', label: '联系电话'},
						{key: 'agencyrole', label: '代理级别'},
						{key: 'status', label: '状态'},
						{key: 'creattime', label: '注册时间'}],  // 列数据
				colUnShowKeys: [],  // 不显示列的键集合
			}
		},
		methods: {
			// 用户状态显示转换
			formatStatus: function (row, column) {
				return row.status === 0? "启用" : (row.status === 1? "禁用" : "待审核");
			},
			// 用户代理2角色显示转换
			formatAgencyRole: function (row, column) {
				let roleOp = emEnum.agencyRoleOptions.find(item=>{
					return item.key === row.agencyrole;
				});
				if(!roleOp) return '';
				else return roleOp.value;
			},
			// 时间显示格式转换
			formatCreatTime: function (row, column) {
				if(isNaN(row.creattime) && !isNaN(Date.parse(row.creattime))){
			　　	return util.formatDate.format(new Date(row.creattime.replace(/-/g, '/')), 'yyyy-MM-dd HH:mm:ss');
				} else return row.creattime;
				// if(!!row.creattime) {
				// 	return util.formatDate.format(new Date(row.creattime.replace(/-/g, '/')), 'yyyy-MM-dd HH:mm:ss');
				// } else return row.creattime;
			},
			// 注册时间改变事件
			createTimeChange: function () {
				let _this = this;
				_this.orderStartStamp = '';
				_this.orderEndStamp = '';
				if(!!_this.startTime && !!_this.endTime) {
					_this.startTime = util.formatDate.format(new Date(_this.startTime), 'yyyy-MM-dd') + ' 00:00:00';
					_this.endTime = util.formatDate.format(new Date(_this.endTime), 'yyyy-MM-dd') + ' 23:59:59';
					_this.orderStartStamp = new Date(_this.startTime).getTime();
					_this.orderEndStamp = new Date(_this.endTime).getTime();
				}
				// 获取列表
				_this.areaFilter();
			},
			// 搜索数据
			searchList: function () {
				let _this = this;
				// 执行操作
				_this.loading = true;
				_this.closeDialog();  // 关闭编辑对话框
				let para = {
					vague: _this.vagueContent,
					pageNum: _this.pageNum,
					pageSize: _this.pageSize
				};
				// 如果有区域筛选值，则按区域筛选
				if(!!_this.sortAreaId) para['areaid'] = _this.sortAreaId;
				// 是否增加注册时间筛选
				if(!!_this.orderStartStamp && !!_this.orderEndStamp) {
					para['starTime'] = _this.orderStartStamp;
					para['endTime'] = _this.orderEndStamp;
				}
				// 是否增加状态筛选
				if(_this.statusKey !== '') para['status'] = _this.statusKey;

				clientVague(para, _this)
				.then(res=>res.data)
				.then(data => {
					_this.loading = false;
					let { code, msg } = data;
					if (code !== "0") {
						_this.$message({
							message: msg,
							type: 'error'
						});
					} else {
						let { pageSize, pageNum, totalNum } = data.data;
						data = data.data.list;
						_this.datas = data;
						// 分页
						_this.pageSize = pageSize;
						_this.pageNum = pageNum;
						_this.totalNum = totalNum;
					}
				});
			},
			// 分类筛选
			areaFilter: function () {
				let _this = this;
				if(!!_this.vagueContent) {
					_this.searchList();
				} else {
					_this.getData();
				}
			},
			// 下载导入模板
			downloadTemplate: function () {
				this.loading = true;
				getClientImportTemplate({"uid": this.loginId}).then(res => {
					this.loading = false;
					let { code, msg, data } = res.data;
					if (code !== "0") {
						this.$message({
							message: msg,
							type: 'error'
						});
					} else {
						let { download_url } = data;
						window.open(download_url);
					}
				})
			},
			// 获取列表
			getData: function () {
				let _this = this;
				// 执行操作
				_this.loading = true;
				_this.closeDialog();  // 关闭编辑对话框
				let para = {
					pageNum: _this.pageNum,
					pageSize: _this.pageSize
				};
				// 如果有区域筛选值，则按区域筛选
				if(!!_this.sortAreaId) para['areaid'] = _this.sortAreaId;
				// 是否增加注册时间筛选
				if(!!_this.orderStartStamp && !!_this.orderEndStamp) {
					para['starTime'] = _this.orderStartStamp;
					para['endTime'] = _this.orderEndStamp;
				}
				// 是否增加状态筛选
				if(_this.statusKey !== '') para['status'] = _this.statusKey;

				getClientList(para, _this)
				.then(res=>res.data)
				.then(data => {
					_this.loading = false;
					let { code, msg } = data;
					if (code !== "0") {
						_this.$message({
							message: msg,
							type: 'error'
						});
					} else {
						let { pageSize, pageNum, totalNum } = data.data;
						data = data.data.list;
						_this.datas = data;
						// 分页
						_this.pageSize = pageSize;
						_this.pageNum = pageNum;
						_this.totalNum = totalNum;
					}
				});
			},
			// 删除数据
			delData: function (index, row) {
				let _this = this;
				_this.$confirm('确认删除该客户？', '提示', {
					type: 'warning'
				}).then(() => {
					_this.loading = true;
					let para = {
						cid: row.id
					};
					deleteClient(para, _this)
					.then(res=>res.data)
					.then(data => {
						_this.loading = false;
						let { msg, code } = data;
						if (code !== "0") {
							_this.$message({
								message: msg,
								type: 'error'
							});
						} else {
							_this.$message({
								message: msg,
								type: 'success'
							});
							_this.getData();
						}
					});
				}).catch((error) => {
					if(error!=='cancel') {
						_this.$message({
							message: error.message? error.message : error,
							type: 'error'
						});
					}
				});
			},
			// 修改数据状态（禁用、启用）
			enableData: function (index, row) {
				let _this = this;
				_this.$confirm('确认' + (row.status === 1 || row.status === 2? '启用' : '禁用') + '该客户？', '提示', {
					type: 'warning'
				}).then(() => {
					_this.loading = true;
					let para = {
						cid: row.id,
						status: row.status === 1 || row.status === 2? '0' : '1'
					};
					enableClient(para, _this)
					.then(res=>res.data)
					.then(data => {
						_this.loading = false;
						let { msg, code } = data;
						if (code !== "0") {
							_this.$message({
								message: msg,
								type: 'error'
							});
						} else {
							_this.$message({
								message: msg,
								type: 'success'
							});
							_this.getData();
						}
					});
				}).catch((error) => {
					if(error!=='cancel') {
						_this.$message({
							message: error.message? error.message : error,
							type: 'error'
						});
					}
				});
			},
			// 重置输入
			resetForm: function (formName) {
				this.$refs[formName].resetFields();
			},
			// 编辑数据
			editData: function (index, row) {
				let formName = 'formData';
				// 打开对话框
				this.editDialogVisible = true;
				// 默认值
				if(!row) {
					this.dialogTitle = "添加";
					if(this.$refs[formName]) this.$refs[formName].resetFields();
					this.formData.cid = "";
					this.formData.code = "";
					this.formData.reallyname = "";
					// 区域默认选中第一个
					if(!!this.areaDatas && this.areaDatas.length > 0) {
						this.formData.areaid = this.areaDatas[0].areaId;
					}
					// 代理级别默认选中第一个
					this.formData.agencyrole = "";  //this.agencyRoleOptions[0].key;
					// 培训公司默认选中第一个
					if(!!this.agencyDatas && this.agencyDatas.length > 0) {
						this.formData.aagencyid = this.agencyDatas[0].aagencyid;
					}
					this.formData.pagencyname = "";
					this.formData.cagencyname = "";
					this.formData.tagencyname = "";
					this.formData.linkphone = "";
					this.formData.region.province = "";
					this.formData.region.city = "";
					this.formData.region.district = "";
					this.formData.address = "";
				} else {
					this.dialogTitle = "编辑";
					this.formData.cid = row.id;
					this.formData.code = row.code;
					this.formData.reallyname = row.reallyname;
					// 区域
					let areaOp = this.areaDatas.find(item=>{
						return item.areaId === row.areaid;
					});
					if(!areaOp) {
						// 区域默认选中第一个
						this.formData.areaid = this.areaDatas[0].areaId;
					} else {
						this.formData.areaid = row.areaid;
					}
					// 代理级别
					this.formData.agencyrole = row.agencyrole;
					// 培训公司
					let agencyOp = this.agencyDatas.find(item=>{
						return item.aagencyid === row.aagencyid + '';
					});
					if(!agencyOp) {
						// 培训公司默认选中第一个
						this.formData.aagencyid = this.areaDatas[0].aagencyid;
					} else {
						this.formData.aagencyid = row.aagencyid + '';
					}
					this.formData.aagencyname = row.aagencyname;
					this.formData.pagencyname = row.pagencyname;
					this.formData.cagencyname = row.cagencyname;
					this.formData.tagencyname = row.tagencyname;
					this.formData.linkphone = row.linkphone;
					this.formData.region.province = row.province;
					this.formData.region.city = row.city;
					this.formData.region.district = row.district;
					this.formData.address = row.address;
					
					// 校验输入
					if(this.$refs[formName]) this.$refs[formName].validate();
				}
				// 用户角色改变事件修改区域可用状态
				this.roleChange();
			},
			// 打开编辑对话框
			openDialog: function () {
				// 焦点放到获取焦点的输入框
				let ele = document.getElementById('focus-ele');
				if(!!ele) {
					ele.focus();
				}
			},
			// 关闭编辑对话框
			closeDialog: function () {
				this.editDialogVisible = false;
			},
			// 提交数据
			handleSubmit: function (formName) {
				this.$refs[formName].validate((valid) => {
          			if (valid) {
						let _this = this;
						_this.$confirm('确认' + _this.dialogTitle + '客户？', '提示', {
							type: 'warning'
						}).then(() => {
							_this.loading = true;
							if(!_this.formData.cid) {  // 添加
								let para = {
									reallyname: _this.formData.reallyname,  // 用户真实名字
									linkphone: _this.formData.linkphone,    // 联系电话
									address: _this.formData.address,        // 详细地址
									areaid: _this.formData.areaid,          // 区域Id
									aagencyid: _this.formData.agencyrole === 1? '' : _this.formData.aagencyid,    // 区域代理uid
									agencyrole: _this.formData.agencyrole,  // 1:培训公司2:销售公司3:代理14:代理2
									//aagencyname: _this.formData.agencyrole === 1? _this.formData.reallyname : _this.formData.aagencyname,  // 区域代理人姓名
									pagencyname: _this.formData.agencyrole === 2? _this.formData.reallyname : _this.formData.pagencyname,  // 销售公司用户名字
									cagencyname: _this.formData.agencyrole === 3? _this.formData.reallyname : _this.formData.cagencyname,  // 代理1用户名字
									tagencyname: _this.formData.agencyrole === 4? _this.formData.reallyname : _this.formData.tagencyname,  // 代理2用户名字
									province: _this.formData.region.province,  // 省
									city: _this.formData.region.city,          // 市
									district: _this.formData.region.district,  // 县（区）
								};
								addClient(para, _this)
								.then(res=>res.data)
								.then(data => {
									_this.loading = false;
									let { msg, code } = data;
									if (code !== "0") {
										_this.$message({
											message: msg,
											type: 'error'
										});
									} else {
										_this.$message({
											message: msg,
											type: 'success'
										});
										_this.getData();
									}
								});
							} else {  // 编辑数据
								let para = {
									cid: _this.formData.cid,
									reallyname: _this.formData.reallyname,  // 用户真实名字
									linkphone: _this.formData.linkphone,    // 联系电话
									address: _this.formData.address,        // 详细地址
									areaid: _this.formData.areaid,          // 区域Id
									aagencyid:  _this.formData.agencyrole === 1? _this.formData.cid : _this.formData.aagencyid,    // 区域代理uid
									agencyrole: _this.formData.agencyrole,  // 1:培训公司2:销售公司3:代理14:代理2
									//aagencyname: _this.formData.agencyrole === 1? _this.formData.reallyname : _this.formData.aagencyname,  // 区域代理人姓名
									pagencyname: _this.formData.agencyrole === 2? _this.formData.reallyname : _this.formData.pagencyname,  // 销售公司用户名字
									cagencyname: _this.formData.agencyrole === 3? _this.formData.reallyname : _this.formData.cagencyname,  // 代理1用户名字
									tagencyname: _this.formData.agencyrole === 4? _this.formData.reallyname : _this.formData.tagencyname,  // 代理2用户名字
									province: _this.formData.region.province,  // 省
									city: _this.formData.region.city,          // 市
									district: _this.formData.region.district,  // 县（区）
								};
								editClient(para, _this)
								.then(res=>res.data)
								.then(data => {
									_this.loading = false;
									let { msg, code } = data;
									if (code !== "0") {
										_this.$message({
											message: msg,
											type: 'error'
										});
									} else {
										_this.$message({
											message: msg,
											type: 'success'
										});
										_this.getData();
									}
								});
							}
						}).catch((error) => {
							if(error!=='cancel') {
								_this.$message({
									message: error.message? error.message : error,
									type: 'error'
								});
							}
						});
					} else {
						return false;
					}
				});
			},
			// 导入Excel
			importExcel: function (e) {
				let file = e.target.files[0];
				// 判断文件
				if (!file) {
					this.$message({
						message: '请选择导入的文件！',
						type: 'warning'
					});
					return;
				}
				// 判断格式
				const types = file.name.split('.')[1];
				const fileType = ['xlsx', 'xls', 'csv'].some(item => item === types);
				if (!fileType) {
					this.$message({
						message: '格式错误，请重新选择！',
						type: 'warning'
					});
					return;
				} else {
					let _this = this;
					// 处理数据
					let xlsxJson;
					// 提交数据
					let dataArray = [];
					util.file2Xce(file).then(tabJson => {
						// 清空选中文件
						let f = document.getElementById('file');
						f.value = '';
						// 处理数据
						if (tabJson && tabJson.length > 0) {
							xlsxJson = tabJson;
							// 处理要提交的数据
							if(!!xlsxJson && xlsxJson.length > 0) {
								//xlsxJson.forEach(sheetItem => {
								let sheetItem = xlsxJson[0];
								if(!!sheetItem && !!sheetItem.sheet && sheetItem.sheet.length > 0) {
									sheetItem.sheet.forEach(item => {
										let clientData = {};
										let i = 0;
										Object.keys(item).forEach(col => {
											if(i === 1) clientData['reallyname'] = item[col];
											else if(i === 2) clientData['areaid'] = item[col];
											else if(i === 3) clientData['agencyrole'] = item[col];
											else if(i === 4) clientData['aagencyid'] = item[col];
											else if(i === 5) clientData['pagencyname'] = item[col];
											else if(i === 6) clientData['cagencyname'] = item[col];
											else if(i === 7) clientData['tagencyname'] = item[col];
											else if(i === 8) clientData['linkphone'] = item[col];
											else if(i === 9) clientData['province'] = item[col];
											else if(i === 10) clientData['city'] = item[col];
											else if(i === 11) clientData['district'] = item[col];
											else if(i === 12) clientData['address'] = item[col];
											// 序号加1
											i++;
										});
										dataArray.push(clientData);
									});
								}
								//});
								if(!!dataArray && dataArray.length > 0) {
									// 执行操作
									_this.loading = true;
									let para = {
										list: dataArray
									};
									batchAddClient(para, _this)
									.then(res=>res.data)
									.then(data => {
										_this.loading = false;
										let { code, msg } = data;
										if (code !== "0") {
											_this.$message({
												message: msg,
												type: 'error'
											});
										} else {
											_this.getData();
										}
									});
								} else {
									_this.$message({
										message: '未读取到有效数据，请确认数据格式后重新导入！',
										type: 'warning'
									});
								}
							}
						} else {
							_this.$message({
								message: '导入文件的数据为空！',
								type: 'warning'
							});
						}
					});
				}
			},
			// 导出Execl
			exportExcel: function () {
				let _this = this;
				// 执行操作
				_this.loading = true;
				_this.closeDialog();  // 关闭编辑对话框
				let para = {
					pageNum: 1,
					pageSize: _this.totalNum
				};
				// 如果有区域筛选值，则按区域筛选
				if(!!_this.sortAreaId) para['areaid'] = _this.sortAreaId;
				// 是否增加注册时间筛选
				if(!!_this.orderStartStamp && !!_this.orderEndStamp) {
					para['starTime'] = _this.orderStartStamp;
					para['endTime'] = _this.orderEndStamp;
				}
				// 是否增加状态筛选
				if(_this.statusKey !== '') para['status'] = _this.statusKey;
				// 快速搜索和正常搜索方法区分
				if(!!_this.vagueContent) {
					para['vague'] = _this.vagueContent;
					clientVague(para, _this)
					.then(res=>res.data)
					.then(data => {
						_this.loading = false;
						let { code, msg } = data;
						if (code !== "0") {
							_this.$message({
								message: msg,
								type: 'error'
							});
						} else {
							data = data.data.list;
							_this.doExport(data);
						}
					});
				} else {
					getClientList(para, _this)
					.then(res=>res.data)
					.then(data => {
						_this.loading = false;
						let { code, msg } = data;
						if (code !== "0") {
							_this.$message({
								message: msg,
								type: 'error'
							});
						} else {
							data = data.data.list;
							_this.doExport(data);
						}
					});
				}
			},
			// 执行导出
			doExport: function (tabData) {
				let _this = this;
				// 导出数据
				if(!!tabData && tabData.length > 0) {
					let i = 1;
					tabData.forEach(item => {
						item.id = i;
						item.agencyrole = _this.formatAgencyRole(item);
						item.status = _this.formatStatus(item);
						item.creattime = _this.formatCreatTime(item);
						i++;
					});
					// 获取文件名
					let fileName = _this.sysCnName + "_" + util.formatDate.format(new Date(), "yyyy-MM-dd HH:mm:ss") + "_" + _this.$root.$route.name;
					// 表头和对应字段
					let tHeader = [ '序号', '客户编码', '客户姓名', '所属团队', '培训公司', '销售公司', 
						'代理1', '代理2', '省', '市', '县（区）', '详细地址', '联系电话', '代理级别', '状态', '注册时间' ];
					let filterVal = [ 'id', 'code', 'reallyname', 'areaname', 'aagencyname', 'pagencyname', 
						'cagencyname', 'tagencyname', 'province', 'city', 'district', 'address', 'linkphone', 'agencyrole', 'status', 'creattime' ];
					util.exportJsonToExcel(fileName, JSON.stringify(tabData), tHeader, filterVal);
				} else {
					this.$message({
						message: "没有需要导出的数据",
						type: 'warning'
					});
				}
			},
			// 设置显示列
			editCol: function () {
				this.colDialogVisible = true;
			},
			// 关闭设置显示列
			closeColSet: function () {
				this.fixedPos = "none";
				// 存储不显示列
				util.editStore.SetUnShowCols(this.pageName, this.colUnShowKeys);
				this.fixedPos = "right";
			},
			// 用户角色改变
			roleChange: function () {
				this.formData.aagencyid = this.formData.agencyrole > 1 ? this.formData.aagencyid : '';
				this.formData.pagencyname = this.formData.agencyrole > 2? this.formData.pagencyname : '';
				this.formData.cagencyname = this.formData.agencyrole > 3? this.formData.cagencyname : '';
				this.formData.tagencyname = this.formData.agencyrole > 4? this.formData.tagencyname : '';
				this.agencyShow = this.formData.agencyrole > 1;
				this.pagencyShow = this.formData.agencyrole > 2;
				this.cagencyShow = this.formData.agencyrole > 3;
				this.tagencyShow = this.formData.agencyrole > 4;
				this.rules.aagencyid[0].required = this.formData.agencyrole > 1;
				this.rules.pagencyname[0].required = this.formData.agencyrole > 2;
				this.rules.cagencyname[0].required = this.formData.agencyrole > 3;
				this.rules.tagencyname[0].required = this.formData.agencyrole > 4;
			},
			// 省市县（区）改变
			regionChange: function (val) {
				this.formData.region.province = val.province;
				this.formData.region.city = val.city;
				this.formData.region.district = val.district;
			},
			// 获取区域
			getAllAreas: function () {
				let _this = this;
				_this.loading = true;
				let para = {};
				// getAreaConfig(para, _this)
				areabyroleList(para, _this)
				.then(res=>res.data)
				.then(data => {
					let { code, msg } = data;
					if (code !== "0") {
						_this.$message({
							message: msg,
							type: 'error'
						});
					} else {
						let areaData = data.data.list2;
						let agencyData = data.data.list;
						// 区域
						_this.areaDatas = areaData.filter(item => item.status === 0);
						// 处理带全部的区域
						if(!!_this.loginAreaid) {
							let areas = _this.loginAreaid.split(',');
							_this.areaDatas.forEach(item => {
								areas.forEach(a => {
									if((a + '') === (item.areaId + '')) {
										_this.allAreaDatas.push({id: item.areaId, areaname: item.areaName});
									}
								});
							});
							if(!!_this.allAreaDatas && _this.allAreaDatas.length > 0) {
								_this.sortAreaId = _this.allAreaDatas[0].id;
							}
						} else {
							_this.allAreaDatas.push({id: '', areaname: '全部'});
							_this.areaDatas.forEach(item => {
								// _this.allAreaDatas.push({id: item.id, areaname: item.areaname});
								_this.allAreaDatas.push({id: item.areaId, areaname: item.areaName});
							});
						}
						// 培训公司
						_this.agencyDatas = agencyData;
						_this.loading = false;
						
						// 获取用户
						_this.getData();
					}
				});
			},
			// 分页事件
			handlePageSizeChange(val) {
				this.pageSize = val;
				this.getData();
			},
			handlePageCurrentChange(val) {
				this.pageNum = val;
				this.getData();
			}
		},
		// 创建VUE实例后的钩子
		created: function () {
			// 用户权限
			this.role = util.userData.getLoginUserFieldValue(emEnum.loginUserFileds.role, this);
			// 登录ID
			this.loginId = util.userData.getLoginUserFieldValue(emEnum.loginUserFileds.uid, this);
			// 用户区域ID
			this.loginAreaid = util.userData.getLoginUserFieldValue(emEnum.loginUserFileds.areaid, this);
			// 获取不显示列
			let unShowCols = util.editStore.GetUnShowCols(this.pageName);
			if(!!unShowCols && unShowCols !== 'none') {
				this.colUnShowKeys = unShowCols;
			}
		},
		// 挂载到DOM后的钩子
		mounted: function () {
			let _this = this;
			_this.containerHeight = _this.$refs.clientcontainer.offsetHeight;
			// 获取参数值
			let query = _this.$route.query;
			if(!!query && !!query.id) {
				_this.sortAreaId = parseInt(query.id);
			}
			// 获取区域
			this.getAllAreas();
			// 绑定窗口尺寸改变事件改变数据表的高度
			window.onresize = () => {
				return (() => {
					// 为了避免频繁触发resize函数导致页面卡顿，使用定时器
					if (!this.timer && !!_this.$refs.clientcontainer) {
						this.timer = true
						_this.containerHeight = _this.$refs.clientcontainer.offsetHeight;
						let that = this
						setTimeout(function () {
							that.timer = false
						}, 100);
					}
				})();
			}
		}
	};
</script>

<style lang="scss">
.client-container {
	height: calc(100vh - 120px);
	.region-picker select {
		color: #606266;
		font-size: inherit;
		padding: 3px 0;
		border: 1px solid #DCDFE6;
		border-radius: 4px;
		box-sizing: border-box;
		outline: 0;
	}
	.region-picker select:focus {
		    border-color: #409EFF;
	}
}
</style>