<template>
    <section class="page-container">
        <p class="page-header">404</p>
        <p class="page-body">抱歉！您访问的页面不存在，请重新加载......</p>
        <el-link type="primary" @click="returnHomePage" class="page-return">&lt; 返回主页</el-link>
    </section>
</template>
<script>
export default {
    methods: {
        // 返回管理页
      returnHomePage() {
        this.$router.push({ path: '/home' });
      }
    }
}
</script>

<style lang="scss" scoped>
    .page-container {
        position: relative;
        top: 20vh;
        font-size: 20px;
        text-align: center;
        color: rgb(192, 204, 218);
        cursor: pointer;
        .page-header {
            font-size: 80px;
            letter-spacing: 10px;
        }
        .page-return {
            margin-top: 20px;
            font-size: 20px;
        }
    }
</style>