<template>
	<el-row>
		<el-col :span="3">
			<label class="layer" @click.prevent="rootClick">{{ rootLayer }}</label>
		</el-col>
		<el-col :span="1" class="layer-partition">
			<span>>></span>
		</el-col>
		<el-col :span="6">
			<el-select class="layer promLayer" v-model="firstLayerVal" @change="firstLayerChange"
				:disabled="!firstLayerData || firstLayerData.length === 0" placeholder="请选择促销活动">
				<el-option
					v-for="item in firstLayerData"
					:key="item.id"
					:label="item.title"
					:value="item.id">
				</el-option>
			</el-select>
		</el-col>
		<el-col :span="1" class="layer-partition">
			<span>>></span>
		</el-col>
		<el-col :span="6">
			<el-select class="layer promLayer" v-model="secondLayerVal" @change="secondLayerChange"
				:disabled="!secondLayerData || secondLayerData.length === 0" placeholder="请选择促销步骤">
				<el-option
					v-for="item in secondLayerData"
					:key="item.id"
					:label="item.steptitle"
					:value="item.id">
				</el-option>
			</el-select>
		</el-col>
		<el-col :span="1" class="layer-partition">
			<span>>></span>
		</el-col>
		<el-col :span="6">
			<el-select class="layer promLayer" v-model="thirdLayerVal" @change="thirdLayerChange"
				:disabled="!thirdLayerData || thirdLayerData.length === 0" placeholder="请选择促销标签">
				<el-option
					v-for="item in thirdLayerData"
					:key="item.id"
					:label="item.labelname"
					:value="item.id">
				</el-option>
			</el-select>
		</el-col>
	</el-row>
</template>

<script>
export default {
	data(){
		return{
			// 顶层
			rootLayer: '促销管理',  // 顶层促销管理
			// 第一层
			firstLayerData: [],  // 可选值
			firstLayerVal: '',   // 值
			// 第二层
			secondLayerData: [],  // 可选值
			secondLayerVal: '',   // 值
			// 第三层
			thirdLayerData: [],  // 可选值
			thirdLayerVal: '',   // 值
			// 回调
			callFunc: false,  // 是否调用方法
		}
	},
	methods: {
		// 顶层单击事件
		rootClick: function () {
			// 打开促销管理
			this.$router.push({ path: '/promotion' });
		},
		// 第一层改变事件
		firstLayerChange: function (rowId) {
			let _this = this;
			let row = _this.firstLayerData.find(item=>{
				return item.id === rowId;
			});
			// 执行操作
			_this.callFunc = false;
			_this.$emit("promotionChange", row.id);
			if(!_this.callFunc) {
				_this.$router.push({ path: '/promotionStep', query: {pid: row.id}});
			}
		},
		// 第二层改变事件
		secondLayerChange: function (rowId) {
			let _this = this;
			let row = _this.secondLayerData.find(item=>{
				return item.id === rowId;
			});
			// 执行操作
			_this.callFunc = false;
			_this.$emit("promotionStepChange", _this.firstLayerVal, row.id, row.steptitle, row.steptype);
			if(!_this.callFunc) {
				this.$router.push({ path: '/processLabel', query: {pid: _this.firstLayerVal, sid: row.id, st: row.steptitle, sp: row.steptype}});
			}
		},
		// 第三层改变事件
		thirdLayerChange: function (rowId) {
			let _this = this;
			let row = _this.thirdLayerData.find(item=>{
				return item.id === rowId;
			});
			let rowsec = _this.secondLayerData.find(item=>{
				return item.id === _this.secondLayerVal;
			});
			// 执行操作
			_this.callFunc = false;
			_this.$emit("promotionLabelChange", _this.firstLayerVal, _this.secondLayerVal, row.steptitle, rowsec.steptype, row.id, row.labelname, row.startime, row.endtime, row.remark);
			if(!_this.callFunc) {
				// 参数
				let q = {
					pid: _this.firstLayerVal, 
					sid: _this.secondLayerVal, 
					st: row.steptitle,
					sp: rowsec.steptype,
					plid: row.id, 
					ln: row.labelname, 
					// c: row.code,
					s: row.startime,
					e: row.endtime,
					r: row.remark
				};
				this.$router.push({ path: '/processLabelInventory', query: q});
			}
		},
		// 触发回调
		callBack: function () {
			this.callFunc = true;
		},
		// 改变数据集
		// layer：0,顶层；1,第一层；2,第二层；3,第三层
		// data：数据集合
		changeData: function (layer, data) {
			if(layer === 1) {  // 第一层
				this.firstLayerData = data;
				sessionStorage.setItem('firstLayerData', JSON.stringify(data));
			} else if(layer === 2) {  // 第二层
				this.secondLayerData = data;
				sessionStorage.setItem('secondLayerData', JSON.stringify(data));
			} else if(layer === 3) {  // 第三层
				this.thirdLayerData = data;
				sessionStorage.setItem('thirdLayerData', JSON.stringify(data));
			}
		},
		// 改变值
		// layer：0,顶层；1,第一层；2,第二层；3,第三层
		// val：当前值
		changeVal: function (layer, val) {
			if(layer ===0) {  // 顶层
				rootLayer = val;
			} else if(layer === 1) {  // 第一层
				this.firstLayerVal = val;
			} else if(layer === 2) {  // 第二层
				this.secondLayerVal = val;
			} else if(layer === 3) {  // 第三层
				this.thirdLayerVal = val;
			}
		}
	},
	// 创建VUE实例后的钩子
	created: function () {
		// 获取session storage中的数据
		let fl = sessionStorage.getItem('firstLayerData');
		if(!!fl) {
			this.firstLayerData = JSON.parse(fl);
		}
		let sl = sessionStorage.getItem('secondLayerData');
		if(!!sl) {
			this.secondLayerData = JSON.parse(sl);
		}
		let tl = sessionStorage.getItem('thirdLayerData');
		if(!!tl) {
			this.thirdLayerData = JSON.parse(tl);
		}
	},
	// 挂载到DOM后的钩子
	mounted: function () {

	}
}
</script>

<style lang="scss">
.layer {
    cursor: pointer;
	color: #409EFF;
	font-size: 17px;
}
.layer-partition {
	margin-top: 3px;
}
.promLayer {
	margin-top: -10px;
}
</style>