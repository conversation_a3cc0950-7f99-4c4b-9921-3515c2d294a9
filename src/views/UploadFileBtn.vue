<template>
	<div class="flex-row" style="position: relative">
		<el-image
			v-if="imgUrl"
			style="width: 100px; height: 100px;"
			:src="imgUrl"
			:preview-src-list="[imgUrl]">
		</el-image>
		<div v-else style="position: relative">
				<el-image
					:src="btnImageUrl"
					style="width: 100px; height: 100px;"
					>
				</el-image>
				<input id="file" class="add-view" style="position: absolute; width: 100%; height: 100%; opacity: 0; top: 0; left: 0" type="file" @change='uploadFile($event)' accept="image/gif, image/jpg, image/jpeg, image/png, image/bmp" />
		</div>
		<i v-if="imgUrl" @click="deleteImageAction" class="el-icon-delete" style="color: gray; position: absolute; right: 0; top: 0; font-size: 20px"></i>
	</div>
</template>

<script>
import COS from 'cos-js-sdk-v5';

var Bucket ='kzm-prod-1256701284';          // 腾讯云对象储存桶名
var Region = 'ap-shanghai';                    // 对象储存你所处的地区编号
var Thum = '?imageView2/1/w/100/h/100/q/85!';  // 缩略图后缀
var MaxMSize = 3;                              // 图片最大M数
var cos = new COS({
	SecretId: 'AKIDiDWaOw20A4i6PSqsTotiHchW5QWMjWBc',  // 密钥ID	
	SecretKey: 'KOFtISQ8crqdFckgNq43qPimwqXzTlsl',     // 密钥的钥匙
});

export default {
	props:["imgUrl"],  //props用来接收父组件传过来的数据
	data(){
		return{
			loading: false,
			btnImageUrl: require("@/assets/images/btn_add_img.png"),
			addImg: require('@/assets/images/addImg.png'),   // 新增图片地址
			fileUrl: require('@/assets/images/addImg.png'),  // 对象地址
			btnText: '上传图片',  // 按钮文字
		}
	},
	methods: {
		// 上传文件
		uploadFile: function (e) {
			var file = e.target.files[0];
			if (!file) return;
			const timeStamp = (new Date()).valueOf();
      		const copyFile = new File([file], `${timeStamp}_${file.name}`);

			// 清空选中文件
			let f = document.getElementById('file');
			f.value = '';
			
			let _this = this;
			// 限制图片最大10M
			let isLimit = copyFile.size / 1024 / 1024 > MaxMSize;
			if(isLimit) {
				this.$message({
					message: '图片过大，请选择大小在 ' + MaxMSize + 'M 内的图片！',
					type: 'error'
				});
				return;
			}
			// 分片上传文件
			cos.putObject({
				Bucket: Bucket,
				Region: Region,
				Key: copyFile.name,
				Body: copyFile,
				onProgress: function (progressData, callback) {
					// logger.log(JSON.stringify(progressData));
					// console.log(progressData);
					_this.loading = true;
					if(progressData.percent === 1) {
						_this.loading = false;
						// 获取上传成功后的Url地址，通过这个地址查看、下载上传的文件	
						cos.getObjectUrl({
							Bucket: Bucket,
							Region: Region,
							Key: copyFile.name,
							Sign: false,
						}, function (err, data) {
							_this.fileUrl = data.Url + Thum;
							// 与父组件通信
							_this.$emit("getFileUrl", {
								highimage: data.Url, 
								thumimage: _this.fileUrl
							});
						});
					}
				},
			}, function (err, data) {
				// console.log(err, data);
				_this.loading = false;
			});
		},
		// 改变文件路径
		changeFileUrl: function (url, txt) {
			if(!url) {
				this.fileUrl = this.addImg;
			} else {
				this.fileUrl = url;
			}
			this.btnText = txt;
		},

		deleteImageAction() {
			this.$emit("deleteImage");
		}
	},
	// 创建VUE实例后的钩子
	created: function () {

	},
	// 挂载到DOM后的钩子
	mounted: function () {

	}
}
</script>

<style lang="scss">
// .file {
//     display: inline-block;
//     position: relative;
//     width: 110px;
//     height: 40px;
//     line-height: 40px;
//     color: #FFF;
//     font-size: 18px;
//     text-align: center;
// 	margin-top: 20px;
//     margin-left: 20px;
//     border: 1px solid #4D5AFF;
//     border-radius: 4px;
//     background: #4D5AFF;
//     text-indent: 0;
//     text-decoration: none;
//     overflow: hidden;
// }
// .file input {
//     position: absolute;
//     font-size: 100px;
//     right: 0;
//     top: 0;
//     opacity: 0;
// }
// .file:hover {
//     background: #66b1ff;
//     border-color: #66b1ff;
//     text-decoration: none;
// }
.add-view:hover {
	cursor: pointer;
}
</style>