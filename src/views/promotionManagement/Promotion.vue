<template>
	<section class="promotion-container common-container" ref="promotioncontainer">
		<!--操作-->
		<section class="data-oper">
			<el-button type="primary" @click="editData" class="hzpbtn-primary" v-loading="loading">添加</el-button>
			<el-button type="primary" @click="copyData" class="hzpbtn-primary" v-loading="loading" :disabled="copyDisabled">复制</el-button>
			<!-- <el-button type="primary" @click="exportExcel" class="hzpbtn-primary">导出</el-button> -->
			<el-button type="primary" @click="submitSort" class="hzpbtn-primary" v-loading="loading" :disabled="submitSortDisabled">提交排序</el-button>
			<!--位置显示-->
			<LayerPath class="layerPath" ref='LayerPath'></LayerPath>
		</section>
		<!--列表-->
		<el-table :data="datas" :stripe="true" :height="containerHeight - 55" highlight-current-row v-loading="loading" id="out-table" 
			class="data-content" border row-key="id" @row-click="handleRowChange">
			<af-table-column label="序号" align="center" type="index" width="60">
			</af-table-column>
			<!-- <af-table-column prop="code" label="编号" align="center">
			</af-table-column> -->
			<af-table-column prop="title" label="标题" align="center">
			</af-table-column>
			<el-table-column prop="thumimage" label="背景图" align="center">
				<template slot-scope="scope">
					<el-image style="width: 50px; height: 50px" :src="scope.row.thumimage" fit="cover" v-show="!!scope.row.thumimage"></el-image>
				</template>
			</el-table-column>
			<af-table-column prop="promotiontype" label="促销类型" :formatter="formatPromotionType" align="center">
			</af-table-column>
			<af-table-column prop="startime" label="开始时间" :formatter="formatStartime" align="center">
			</af-table-column>
			<af-table-column prop="endtime" label="结束时间" :formatter="formatEndtime" align="center">
			</af-table-column>
			<af-table-column prop="cashpromote" label="现金促销金额" align="center">
			</af-table-column>
			<!-- <af-table-column prop="usevoucher" label="可用返单券" :formatter="formatUseVoucher" align="center">
			</af-table-column>
			<af-table-column prop="voucherrate" label="返单券比例" align="center">
			</af-table-column>
			<af-table-column prop="voucheramount" label="返单券金额" align="center">
			</af-table-column> -->
			<af-table-column prop="status" label="是否发布" :formatter="formatStatus" align="center">
			</af-table-column>
			<af-table-column label="操作" align="center" width="270" fixed="right">
				<template slot-scope="scope">
					<label class="hzpedit" @click.prevent="delData(scope.$index, scope.row)">删除</label>|
					<label class="hzpedit" @click.prevent="enableData(scope.$index, scope.row)">{{scope.row.status===0? '下架' : '发布'}}</label>|
					<label class="hzpedit" @click.prevent="editData(scope.$index, scope.row)">编辑</label>|
					<label class="hzpedit" @click.prevent="nextStep(scope.$index, scope.row)">促销步骤</label>
				</template>
			</af-table-column>
		</el-table>
		<!--编辑数据-->
        <el-dialog :title="dialogTitle + '数据'" :visible.sync="editDialogVisible" class="dialog-container" width="500px" @opened="openDialog">
			<el-form :model="formData" :rules="rules" ref="formData" label-width="100px" class="form-data" size="mini">
				<!-- <el-form-item label="编    号" prop="code">
					<el-input v-model="formData.code" placeholder="请输入" :disabled="codeDisabled" id="focus-ele"></el-input>
				</el-form-item> -->
				<el-form-item label="标    题" prop="title">
					<el-input v-model="formData.title" placeholder="请输入" id="focus-ele"></el-input>
				</el-form-item>
				<el-form-item label="促销类型" prop="promotiontype">
					<el-select v-model="formData.promotiontype" placeholder="请选择">
						<el-option
							v-for="item in promotionTypeOptions"
							:key="item.key"
							:label="item.value"
							:value="item.key">
						</el-option>
					</el-select>
				</el-form-item>
				<el-form-item label="现金促销" prop="cashpromote">
					<el-input v-model="formData.cashpromote" placeholder="请输入"></el-input><!-- @keyup.native="cash"-->
				</el-form-item>
				<el-form-item label="图    片" prop="">
					<UploadFile :imgUrl="formData.thumimage" @getFileUrl="getFileUrl" ref='UploadFile'></UploadFile>
				</el-form-item>
				<!-- <el-row class="order-edit-row">
					<el-col :span="12">
						<el-form-item label="可用返单券" prop="usevoucher">
							<el-select v-model="formData.usevoucher" placeholder="请选择" @change="usevoucherChange">
								<el-option
									v-for="item in useVoucherOptions"
									:key="item.key"
									:label="item.value"
									:value="item.key">
								</el-option>
							</el-select>
						</el-form-item>
					</el-col>
					<el-col :span="11">
						<el-form-item label="返单券金额" prop="voucheramount">
							<el-input v-model="formData.voucheramount" placeholder="请输入" :disabled="voucherUnEditable"></el-input>
						</el-form-item>
					</el-col>
				</el-row> -->
				<!-- <el-form-item label="返单券比例" prop="voucherrate">
					<el-slider v-model="formData.voucherrate" :disabled="voucherUnEditable" :max="1000" show-input></el-slider>
				</el-form-item> -->
				<el-form-item label="开始时间" prop="startime">
					<el-date-picker v-model="formData.startime" type="datetime" value-format="yyyy-MM-dd HH:mm:ss" format="yyyy-MM-dd HH:mm:ss" placeholder="请选择开始时间"> </el-date-picker>
				</el-form-item>
				<el-form-item label="结束时间" prop="endtime">
					<el-date-picker v-model="formData.endtime" type="datetime" value-format="yyyy-MM-dd HH:mm:ss" format="yyyy-MM-dd HH:mm:ss" placeholder="请选择结束时间"> </el-date-picker>
				</el-form-item>
        	</el-form>
            <div slot="footer" class="dialog-footer">
              	<el-button @click="closeDialog" class="hzpbtn-close">关 闭</el-button>
                <el-button type="primary" @click="handleSubmit('formData')" v-loading="loading" class="hzpbtn-primary">保 存</el-button>
            </div>
        </el-dialog>
	</section>
</template>
<script>
	import { getPromotionList } from '../../api/api';
	import { addPromotion } from '../../api/api';
	import { editPromotion } from '../../api/api';
	import { deletePromotion, deletePromotion_new } from '../../api/api';
	import { enablePromotion } from '../../api/api';
	import { sortPromotion } from '../../api/api';
	import { promotionCopy } from '../../api/api';
	import util from '../../common/js/util';
	import emEnum from '../../common/js/emEnum';
	// 行列拖拽
	import Sortable from 'sortablejs';
	// 上传文件组件
	import UploadFile from '../UploadFile';
	// 层级组件
	import LayerPath from '../LayerPath';

	export default {
		components: {
			UploadFile,
			LayerPath
		},
		data() {
			return {
				sysCnName: emEnum.cnName,  // 中文名称
				sysEnName: emEnum.enName,  // 英文名称
				
				layer: 1,  // 层级

				loginId: '',   // 登录用户

				submitSortDisabled: true,  // 提交排序不可用
				idArr: [],        // 编号
				sequenceArr: [],  // 序号

				loading: false,
				containerHeight: 0,
				datas: [],

				copyDisabled: true,  // 复制是否可用
				copyId: '',          // 复制的促销活动Id
				copyTitle: '',       // 复制的标题

				codeDisabled: true,  // code是否可编辑
				roleOptions: emEnum.roleOptions, // 角色集合
				promotionTypeOptions: emEnum.promotionTypeOptions, // 促销类型集合
				useVoucherOptions: emEnum.useVoucherOptions,       // 是否可用返单券
				dialogTitle: '',   // 对话框标题
				dialogBtnVal: '',  // 按钮值
				editDialogVisible: false,  // 编辑对话框

				// 返单券可编辑
				voucherUnEditable: false,

				formData: {
					pid: '',           // 促销活动编号
					// code:'',           // 促销活动code
					startime: '',      // 活动开始日期
					endtime:'',        // 活动结束日期
					title:'',          // 促销活动标题
					cashpromote:'',    // 现金促销（暂不明含义）
					promotiontype:'',  // 促销活动类型
					highimage: '',     // 高清图
					thumimage: '',     // 缩略图
					// usevoucher: '',    // 可用返单券
					// voucherrate: '',   // 返单券比例
					// voucheramount: '', // 返单券金额
				},
				rules: {
					// code: [
					// 	{ required: true, message: '请输入促销编号', trigger: 'blur' }
					// ],
					title: [
						{ required: true, message: '请输入促销标题', trigger: 'blur' }
					],
					promotiontype: [
						{ required: true, message: '请选择促销类型', trigger: 'change' }
					],
					cashpromote: [
						{ required: true, message: '请输入现金促销', trigger: 'blur' }
					],
					// usevoucher: [
					// 	{ required: true, message: '请选择是否可用返单券', trigger: 'change' }
					// ],
					// voucherrate: [
					// 	{ required: true, message: '请选择返单券比例', trigger: 'change' }
					// ],
					// voucheramount: [
					// 	{ required: true, message: '请输入返单券金额', trigger: 'blur' }
					// ],
					startime: [
						{ required: true, message: '请选择促销开始日期', trigger: 'change' }
					],
					endtime: [
						{ required: true, message: '请选择促销结束日期', trigger: 'change' }
					]
				}
			}
		},
		methods: {
			// 状态显示转换
			formatStatus: function (row, column) {
				return row.status === 0? "是" : (row.status === 1? "否" : "待审核")
			},
			// 开始时间显示格式转换
			formatStartime: function (row, column) {
				if(isNaN(row.startime) && !isNaN(Date.parse(row.startime))){
			　　	return util.formatDate.format(new Date(row.startime.replace(/-/g, '/')), 'yyyy-MM-dd HH:mm:ss');
				} else return row.startime;
				// if(!!row.startime) {
				// 	return util.formatDate.format(new Date(row.startime.replace(/-/g, '/')), 'yyyy-MM-dd HH:mm:ss');
				// } else return row.startime;
			},
			// 结束时间显示格式转换
			formatEndtime: function (row, column) {
				if(isNaN(row.endtime) && !isNaN(Date.parse(row.endtime))){
			　　	return util.formatDate.format(new Date(row.endtime.replace(/-/g, '/')), 'yyyy-MM-dd HH:mm:ss');
				} else return row.endtime;
				// if(!!row.endtime) {
				// 	return util.formatDate.format(new Date(row.endtime.replace(/-/g, '/')), 'yyyy-MM-dd HH:mm:ss');
				// } else return row.endtime;
			},
			// 促销类型显示转换
			formatPromotionType: function (row, column) {
				let typeOp = emEnum.promotionTypeOptions.find(item=>{
					return item.key === row.promotiontype;
				});
				if(!typeOp) return '';
				else return typeOp.value;
			},
			// 可用返单券显示转换
			formatUseVoucher: function (row, column) {
				let useOp = emEnum.useVoucherOptions.find(item=>{
					return item.key === row.usevoucher;
				});
				if(!useOp) return '';
				else return useOp.value;
			},
			// 金额校验
			cash: function () {
				this.formData.cashpromote = this.formData.cashpromote.replace(/[^\.\d]/g,'');   // 清除"数字"和"."以外的字符
				this.formData.cashpromote = this.formData.cashpromote.replace(/^\./g, '');      // 第一个字符不能为.
				let temp = this.formData.cashpromote.replace(/\./g,'');  // 出现多个点则只去掉最后一个点
				if(this.formData.cashpromote.length >= temp.length + 2) {
					this.formData.cashpromote = this.formData.cashpromote.substr(0, this.formData.cashpromote.length - 1);
				}
			},
			// 可用返单券修改事件
			usevoucherChange: function () {
				if(this.formData.usevoucher === 0) {
					this.voucherUnEditable = true;
				} else {
					this.voucherUnEditable = false;
				}
			},
			// 获取列表
			getData: function () {
				let _this = this;
				// 执行操作
				_this.loading = true;
				_this.closeDialog();  // 关闭编辑对话框
				let para = {};
				getPromotionList(para, _this)
				.then(res=>res.data)
				.then(data => {
					_this.loading = false;
					let { code, msg } = data;
					if (code !== "0") {
						_this.$message({
							message: msg,
							type: 'error'
						});
					} else {
						data = data.data.list;
						// 改变组件的值
						this.$refs.LayerPath.changeData(this.layer, data);
						this.$refs.LayerPath.changeData(this.layer + 1, '');
						this.$refs.LayerPath.changeData(this.layer + 2, '');

						_this.datas = data;
						// 先清空，防止出错
						_this.idArr = [];
						_this.sequenceArr = [];
						_this.datas.forEach(item => {
							_this.sequenceArr.push(item.sequence);
						});
					}
				});
			},
			// 删除数据
			delData: function (index, row) {
				let _this = this;
				_this.$confirm('确认删除该促销活动？', '提示', {
					type: 'warning'
				}).then(() => {
					_this.loading = true;
					let para = {
						pid: row.id,
						uid: this.loginId
					};
					deletePromotion(para, _this)
					.then(res=>res.data)
					.then(data => {
						_this.loading = false;
						let { msg, code } = data;
						if (code !== "0") {
							_this.$message({
								message: msg,
								type: 'error'
							});
						} else {
							_this.$message({
								message: msg,
								type: 'success'
							});
							_this.getData();
						}
					});
				}).catch((error) => {
					if(error!=='cancel') {
						_this.$message({
							message: error.message? error.message : error,
							type: 'error'
						});
					}
				});
			},
			// 修改数据状态（禁用、启用）
			enableData: function (index, row) {
				let _this = this;
				_this.$confirm('确认' + (row.status === 0? '下架' : '发布') + '该促销活动？', '提示', {
					type: 'warning'
				}).then(() => {
					_this.loading = true;
					let para = {
						pid: row.id,
						status: row.status === 0? '1' : '0'
					};
					enablePromotion(para, _this)
					.then(res=>res.data)
					.then(data => {
						_this.loading = false;
						let { msg, code } = data;
						if (code !== "0") {
							_this.$message({
								message: msg,
								type: 'error'
							});
						} else {
							_this.$message({
								message: msg,
								type: 'success'
							});
							_this.getData();
						}
					});
				}).catch((error) => {
					if(error!=='cancel') {
						_this.$message({
							message: error.message? error.message : error,
							type: 'error'
						});
					}
				});
			},
			// 重置输入
			resetForm: function (formName) {
				this.$refs[formName].resetFields();
			},
			// 编辑数据
			editData: function (index, row) {
				let formName = 'formData';
				// 打开对话框
				this.editDialogVisible = true;
				// 默认值
				if(!row) {
					this.dialogTitle = "添加";
					this.dialogBtnVal = "上传图片";
					this.codeDisabled = false;
					if(this.$refs[formName]) this.$refs[formName].resetFields();
					this.formData.pid = "";
					// this.formData.code = "";
					this.formData.startime = "";
					this.formData.endtime = "";
					this.formData.title = "";
					this.formData.cashpromote = "";
					// 促销类型默认选中第一个
					this.formData.promotiontype = this.promotionTypeOptions[0].key;
					this.formData.highimage = "";
					this.formData.thumimage = "";

					this.formData.usevoucher = 1;
					this.formData.voucherrate = 0;
					this.formData.voucheramount = "";
				} else {
					this.dialogTitle = "编辑";
					this.dialogBtnVal = "重新上传";
					this.codeDisabled = true;
					this.formData.pid = row.id;
					// this.formData.code = row.code;
					this.formData.startime = row.startime;
					this.formData.endtime = row.endtime;
					this.formData.title = row.title;
					this.formData.cashpromote = row.cashpromote;
					// 促销类型
					this.formData.promotiontype = row.promotiontype;
					this.formData.highimage = row.highimage;
					this.formData.thumimage = row.thumimage;

					this.formData.usevoucher = row.usevoucher;
					
					let rate = parseInt(row.voucherrate);
					if(isNaN(rate)) {
						this.formData.voucherrate = 0;
					} else {
						this.formData.voucherrate = rate;
					}
					this.formData.voucheramount = row.voucheramount;
				}
			},
			// 复制
			copyData: function (index, row) {
				let _this = this;
				if (!!_this.copyId) {
					_this.$confirm('确认复制促销活动：'+ _this.copyTitle +'？', '提示', {
						type: 'warning'
					}).then(() => {
						_this.loading = true;
						let para = {
							pid: _this.copyId,
						};
						_this.loading = false;
						promotionCopy(para, _this)
						.then(res=>res.data)
						.then(data => {
							_this.loading = false;
							let { msg, code } = data;
							if (code !== "0") {
								_this.$message({
									message: msg,
									type: 'error'
								});
							} else {
								_this.$message({
									message: msg,
									type: 'success'
								});
								_this.getData();
							}
						});
					}).catch((error) => {
						if(error!=='cancel') {
							_this.$message({
								message: error.message? error.message : error,
								type: 'error'
							});
						}
					});
				} else {
					_this.$message({
						message: '请先选择要复制的促销活动！',
						type: 'warning'
					});
				}
			},
			// 打开编辑对话框
			openDialog: function () {
				// 改变组件的图片地址和按钮值
				this.$refs.UploadFile.changeFileUrl(this.formData.thumimage, this.dialogBtnVal);
				// 焦点放到获取焦点的输入框
				let ele = document.getElementById('focus-ele');
				if(!!ele) {
					ele.focus();
				}
			},
			// 关闭编辑对话框
			closeDialog: function () {
				this.editDialogVisible = false;
			},
			// 提交数据
			handleSubmit: function (formName) {
				this.$refs[formName].validate((valid) => {
          			if (valid) {
						let _this = this;
						_this.$confirm('确认' + _this.dialogTitle + '促销活动？', '提示', {
							type: 'warning'
						}).then(() => {
							_this.loading = true;
							if(!_this.formData.pid) {  // 添加
								let para = {
									// code: _this.formData.code,
									startime: _this.formData.startime,//util.formatDate.format(new Date(_this.formData.startime.replace(/-/g, '/')), 'yyyy-MM-dd HH:mm:ss'),
									endtime: _this.formData.endtime,//util.formatDate.format(new Date(_this.formData.endtime.replace(/-/g, '/')), 'yyyy-MM-dd HH:mm:ss'),
									title: _this.formData.title,
									cashpromote: _this.formData.cashpromote,
									promotiontype: _this.formData.promotiontype,
									highimage: _this.formData.highimage,
									thumimage: _this.formData.highimage,  // _this.formData.thumimage,

									usevoucher: _this.formData.usevoucher,
									voucherrate: _this.formData.voucherrate,
									voucheramount: _this.formData.voucheramount,
								};
								addPromotion(para, _this)
								.then(res=>res.data)
								.then(data => {
									_this.loading = false;
									let { msg, code } = data;
									if (code !== "0") {
										_this.$message({
											message: msg,
											type: 'error'
										});
									} else {
										_this.$message({
											message: msg,
											type: 'success'
										});
										_this.getData();
									}
								});
							} else {  // 编辑数据
								let para = {
									pid: _this.formData.pid,
									// code: _this.formData.code,
									startime: _this.formData.startime,//util.formatDate.format(new Date(_this.formData.startime.replace(/-/g, '/')), 'yyyy-MM-dd HH:mm:ss'),
									endtime: _this.formData.endtime,//util.formatDate.format(new Date(_this.formData.endtime.replace(/-/g, '/')), 'yyyy-MM-dd HH:mm:ss'),
									title: _this.formData.title,
									cashpromote: _this.formData.cashpromote,
									promotiontype: _this.formData.promotiontype,
									highimage: _this.formData.highimage,
									thumimage: _this.formData.highimage,  // _this.formData.thumimage,

									usevoucher: _this.formData.usevoucher,
									voucherrate: _this.formData.voucherrate,
									voucheramount: _this.formData.voucheramount,
								};
								editPromotion(para, _this)
								.then(res=>res.data)
								.then(data => {
									_this.loading = false;
									let { msg, code } = data;
									if (code !== "0") {
										_this.$message({
											message: msg,
											type: 'error'
										});
									} else {
										_this.$message({
											message: msg,
											type: 'success'
										});
										_this.getData();
									}
								});
							}
						}).catch((error) => {
							if(error!=='cancel') {
								_this.$message({
									message: error.message? error.message : error,
									type: 'error'
								});
							}
						});
					} else {
						return false;
					}
				});
			},
			// 行拖拽
			rowDrop: function () {
				const tbody = document.querySelector('.el-table__body-wrapper tbody');
				const _this = this;
				Sortable.create(tbody, {
					onEnd({ newIndex, oldIndex }) {
						if(_this.sequenceArr.length > 1) {  // 大于1行才有排序的必要
							const currRow = _this.datas.splice(oldIndex, 1)[0];
							_this.datas.splice(newIndex, 0, currRow);
							_this.submitSortDisabled = false;
							// 设置序号
							for(let i = 0; i < _this.datas.length; i++) {
								_this.datas[i].sequence = _this.sequenceArr[i];
							}
						}
					}
				});
			},
			// 提交排序
			submitSort: function () {
				let _this = this;
				// 编号
				_this.idArr = _this.datas.map(item => {
					return item.id;
				});
				// 提交数据
				if(!!_this.idArr && !!_this.sequenceArr && _this.idArr.length === _this.sequenceArr.length) {				
					_this.$confirm('确认提交排序结果？', '提示', {
						type: 'warning'
					}).then(() => {
						_this.loading = true;
						let para = {
							pid: _this.idArr,
							sequence: _this.sequenceArr
						};
						sortPromotion(para, _this)
						.then(res=>res.data)
						.then(data => {
							_this.loading = false;
							let { msg, code } = data;
							if (code !== "0") {
								_this.$message({
									message: msg,
									type: 'error'
								});
							} else {
								_this.$message({
									message: msg,
									type: 'success'
								});
								_this.getData();
							}
						});
					}).catch((error) => {
						if(error!=='cancel') {
							_this.$message({
								message: error.message? error.message : error,
								type: 'error'
							});
						}
					});
				} else {
					_this.$message({
						message: '排序数据出现问题，请刷新后重试！',
						type: 'error'
					});
				}
			},
			// table 行改变事件
			handleRowChange: function (row, event, column) {
				if(!row.id) {
					this.copyDisabled = true;
					this.copyId = '';
					this.copyTitle = '';
				} else {
					this.copyDisabled = false;
					this.copyId = row.id;
					this.copyTitle = row.title;
				}
			},
			// 下一步骤
			nextStep: function (index, row) {
				this.$router.push({ path: '/promotionStep', query: {pid: row.id}});
			},
			// 导出Execl
			exportExcel: function () {
				let _this = this;
				// 执行操作
				_this.loading = true;
				_this.closeDialog();  // 关闭编辑对话框
				let para = {};
				getPromotionList(para, _this)
				.then(res=>res.data)
				.then(data => {
					_this.loading = false;
					let { code, msg } = data;
					if (code !== "0") {
						_this.$message({
							message: msg,
							type: 'error'
						});
					} else {
						data = data.data.list;
						_this.doExport(data);
					}
				});
			},
			// 执行导出
			doExport: function (tabData) {
				let _this = this;
				// 导出数据
				if(!!tabData && tabData.length > 0) {
					let i = 1;
					tabData.forEach(item => {
						item.id = i;
						item.promotiontype = _this.formatPromotionType(item);
						item.startime = _this.formatStartime(item);
						item.endtime = _this.formatEndtime(item);
						item.status = _this.formatStatus(item);
						item.cashpromote = parseFloat(item.cashpromote);
						item.usevoucher = _this.formatUseVoucher(item);
						item.voucheramount = parseFloat(item.voucheramount);
						i++;
					});
					// 获取文件名
					let fileName = _this.sysCnName + "_" + util.formatDate.format(new Date(), "yyyy-MM-dd HH:mm:ss") + "_" + _this.$root.$route.name;
					// 表头和对应字段
					let tHeader = [ '序号', '标题', '促销类型', '开始时间', '结束时间', '现金促销金额', '可用返单券', '返单券比例', '返单券金额', '是否发布' ];
					let filterVal = [ 'id', 'title', 'promotiontype', 'startime', 'endtime', 'cashpromote', 'usevoucher', 'voucherrate', 'voucheramount', 'status' ];
					util.exportJsonToExcel(fileName, JSON.stringify(tabData), tHeader, filterVal);
				} else {
					this.$message({
						message: "没有需要导出的数据",
						type: 'warning'
					});
				}
			},
			// 选择图片
			choseImage: function (file) {
				this.formData.thumimage = file.url;
			},
			// 获取文件路径
			getFileUrl: function (high, thum) {
				this.formData.highimage = high;
				this.formData.thumimage = thum;
			},
		},
		// 创建VUE实例后的钩子
		created: function () {
			// 登录ID
			this.loginId = util.userData.getLoginUserFieldValue(emEnum.loginUserFileds.uid, this);
		},
		// 挂载到DOM后的钩子
		mounted: function () {
			let _this = this;
			_this.containerHeight = _this.$refs.promotioncontainer.offsetHeight;
			// 获取数据
			_this.getData();
			
			// 阻止默认行为
			document.body.ondrop = function (event) {
				event.preventDefault();
				event.stopPropagation();
			};
			// 行拖拽
			_this.rowDrop()
			// 绑定窗口尺寸改变事件改变数据表的高度
			window.onresize = () => {
				return (() => {
					// 为了避免频繁触发resize函数导致页面卡顿，使用定时器
					if (!this.timer && !!_this.$refs.promotioncontainer) {
						this.timer = true
						_this.containerHeight = _this.$refs.promotioncontainer.offsetHeight;
						let that = this
						setTimeout(function () {
							that.timer = false
						}, 100);
					}
				})();
			}
		}
	};
</script>

<style lang="scss">
.promotion-container {
	height: calc(100vh - 120px);
	.current-row > td {
		background: #409EFF !important;
	}
}
</style>