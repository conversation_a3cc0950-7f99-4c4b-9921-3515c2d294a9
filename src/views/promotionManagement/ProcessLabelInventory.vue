<template>
	<section class="processLabelInventory-container common-container" ref="processLabelInventorycontainer">
		<!--编辑促销标签-->
		<el-row style="width: 100%; height: 30px;">
			<el-col :span="12">
				<el-button type="primary" @click="preStep" class="hzpbtn-primary" style="height: 35px; line-height: 10px; margin-right: 20px;">返回到促销标签</el-button>
				<label class="plTitle">编辑促销标签</label>
			</el-col>
			<el-col :span="12">
				<!--位置显示-->
				<LayerPath class="layerPath" @promotionLabelChange='promotionLabelChange' ref='LayerPath'></LayerPath>
			</el-col>
		</el-row>
		<el-form :model="labelFormData" :rules="labelRules" ref="labelFormData" label-width="100px" class="form-data-nopadding" style="padding: 0px 20px; padding-top: 20px; border-bottom: 1px solid;" size="mini">
			<el-row style="width: 100%; height: 40px; line-height: 40px;">
				<!-- <el-col :span="6">
					<el-form-item label="编    号" prop="code">
						<el-input v-model="labelFormData.code" placeholder="请输入" :disabled="labelCodeDisabled"></el-input>
					</el-form-item>
				</el-col> -->
				<!-- <el-col :span="6">
					<el-form-item label="开始日" prop="startime">
						<el-date-picker v-model="labelFormData.startime" type="datetime" value-format="yyyy-MM-dd HH:mm:ss" format="yyyy-MM-dd HH:mm:ss" placeholder="请选择开始日"> </el-date-picker>
					</el-form-item>
				</el-col>
				<el-col :span="6">
					<el-form-item label="结束日" prop="endtime">
						<el-date-picker v-model="labelFormData.endtime" type="datetime" value-format="yyyy-MM-dd HH:mm:ss" format="yyyy-MM-dd HH:mm:ss" placeholder="请选择结束日"> </el-date-picker>
					</el-form-item>
				</el-col> -->
				<el-col :span="6">
					<el-form-item label="名    称" prop="labelname">
						<el-input v-model="labelFormData.labelname" placeholder="请输入"></el-input>
					</el-form-item>
				</el-col>
				<el-col :span="5">
					<el-form-item label="步骤名称" prop="">
						<el-input v-model="steptitle" placeholder="请输入" :disabled="steptitleDisabled"></el-input>
					</el-form-item>
				</el-col>
				<el-col :span="8">
					<el-form-item label="备    注" prop="">
						<el-input v-model="labelFormData.remark" placeholder="请输入"></el-input>
					</el-form-item>
				</el-col>
				<el-col :span="5" align="left">
					<el-form-item label="" prop="">
						<el-button type="primary" @click="handleLabelSubmit('labelFormData')" class="hzpbtn-primary" v-loading="loading" :disabled="submitDisabled">{{ !!labelFormData.plid? '提交' : '添加' }}</el-button>
					</el-form-item>
				</el-col>
			</el-row>
		</el-form>
		<!--增加标签商品-->
		<el-form :model="addFormData" :rules="addRules" ref="addFormData" label-width="100px" class="form-data-nopadding" size="mini">
			<el-row style="width: 100%; height: 40px; line-height: 40px;">
				<el-col :span="6">
					<el-form-item label="选择商品" prop="icode" v-show="steptype == 1 || steptype == 4">
						<el-row>
							<el-col :span="22">
								<el-select v-model="addFormData.icode" placeholder="请选择" filterable remote :remote-method="searchSpList">
									<el-option
										v-for="item in spDatas"
										:key="item.code"
										:label="item.name"
										:value="item.code">
									</el-option>
								</el-select>
							</el-col>
							<el-col :span="2">
								<el-tooltip content="清除选择的商品" placement="top" effect="light">
									<el-button @click="clearInventory('sp')" icon="el-icon-close"></el-button>
								</el-tooltip>
							</el-col>
						</el-row>
					</el-form-item>
				</el-col>
				<el-col :span="5">
					<el-form-item label="商品数量" prop="" v-show="steptype == 1 || steptype == 4">
						<el-input v-model="addFormData.icodesize" placeholder="请输入" @keyup.native="number" @change="codeSizeChange('add')"></el-input>
					</el-form-item>
				</el-col>
				<el-col :span="(steptype == 1 || steptype == 4) ? 8 : 15">
					<el-form-item label="选择赠品" prop="zpcode">
						<el-row>
							<el-col :span="17">
								<el-select v-model="addFormData.zpcode" placeholder="请选择" filterable remote :remote-method="searchZpList" :disabled="addZpDisabled">
									<el-option
										v-for="item in zpDatas"
										:key="item.code"
										:label="item.name"
										:value="item.code">
									</el-option>
								</el-select>
							</el-col>
							<el-col :span="2">
								<el-tooltip content="清除选择的赠品" placement="top" effect="light">
									<el-button @click="clearInventory('zp')" icon="el-icon-close" :disabled="addZpDisabled"></el-button>
								</el-tooltip>
							</el-col>
							<el-col :span="5" v-show="steptype == 1 || steptype == 4">
								<el-tooltip content="赠品跟随商品" placement="top" effect="light">
									<el-button type="primary" @click="followInventory('zp')" :disabled="addZpDisabled">跟随商品</el-button>
								</el-tooltip>
							</el-col>
						</el-row>
					</el-form-item>
				</el-col>
				<el-col :span="(steptype == 1 || steptype == 4) ? 5 : 9">
					<el-form-item label="赠品数量" prop="zpcodesize">
						<el-input v-model="addFormData.zpcodesize" placeholder="请输入" @keyup.native="zpnumber" :disabled="addZpDisabled"></el-input>
					</el-form-item>
				</el-col>
			</el-row>
			<el-row style="width: 100%; height: 40px; line-height: 40px;">
				<el-col :span="6">
					<el-form-item label="备    注" prop="">
						<el-input v-model="addFormData.remark" placeholder="请输入"></el-input>
					</el-form-item>
				</el-col>
				<el-col :span="9">
					<el-form-item label="" prop="">
						<el-button type="primary" @click="handleAddSubmit('addFormData')" class="hzpbtn-primary" v-loading="loading" :disabled="!labelFormData.plid">添加</el-button>
						<el-button type="primary" @click="exportExcel" class="hzpbtn-primary" style="margin-left: 50px;">导出</el-button>
					</el-form-item>
				</el-col>
				<el-col :span="9">
					<el-form-item label="" prop="">
						<el-input placeholder="商品快速搜索" clearable v-model="vagueContent" style="" @change="searchList" @keyup.native="searchList">
							<el-button slot="append" icon="el-icon-search"></el-button>
						</el-input>
					</el-form-item>
				</el-col>
			</el-row>
		</el-form>
		<!--列表-->
		<el-table :data="datas" :stripe="true" :height="containerHeight - 55 - 158" highlight-current-row v-loading="loading" id="out-table" class="data-content" border row-key="id">
			<el-table-column label="序号" align="center" type="index" width="60">
			</el-table-column>
			<el-table-column prop="name" label="商品名称" align="center" min-width="20%">
			</el-table-column>
			<el-table-column prop="thumimage" label="图片" align="center" min-width="20%">
				<template slot-scope="scope">
					<el-image style="width: 50px; height: 50px" :src="scope.row.thumimage" fit="cover" v-show="!!scope.row.thumimage"></el-image>
				</template>
			</el-table-column>
			<el-table-column prop="refsaleprice" label="单价" align="center" min-width="20%">
			</el-table-column>
			<el-table-column prop="icodesize" label="数量" :formatter="formatIcodesize" align="center" min-width="20%">
			</el-table-column>
			<el-table-column prop="zpname" label="赠品商品" :formatter="formatZpname" align="center" min-width="20%">
			</el-table-column>
			<el-table-column prop="zpcodesize" label="赠品数量" :formatter="formatZpcodesize" align="center" min-width="20%">
			</el-table-column>
			<el-table-column prop="remark" label="备注" align="center" min-width="20%">
			</el-table-column>
			<el-table-column label="操作" align="center" width="200">
				<template slot-scope="scope">
					<label class="hzpedit" @click.prevent="delData(scope.$index, scope.row)">删除</label>|
					<label class="hzpedit" @click.prevent="enableData(scope.$index, scope.row)">{{scope.row.status===0? '禁用' : '启用'}}</label>|
					<label class="hzpedit" @click.prevent="editData(scope.$index, scope.row)">编辑</label>
				</template>
			</el-table-column>
		</el-table>
		<!--编辑数据-->
        <el-dialog :title="dialogTitle + '数据'" :visible.sync="editDialogVisible" class="dialog-container" width="500px" @opened="openDialog">
			<el-form :model="formData" :rules="rules" ref="formData" label-width="100px" class="form-data" size="mini">
				<el-form-item label="选择商品" prop="icode" v-show="steptype == 1 || steptype == 4">
					<el-row>
						<el-col :span="20">
							<el-select v-model="formData.icode" placeholder="请选择" filterable remote :remote-method="searchSpList">
								<el-option
									v-for="item in spDatas"
									:key="item.code"
									:label="item.name"
									:value="item.code">
								</el-option>
							</el-select>
						</el-col>
						<el-col :span="4" style="width: 20px;">
							<el-tooltip content="清除选择的商品" placement="top" effect="light">
								<el-button @click="clearInventory('editsp')" icon="el-icon-close"></el-button>
							</el-tooltip>
						</el-col>
					</el-row>
				</el-form-item>
				<el-form-item label="商品数量" prop="" v-show="steptype == 1 || steptype == 4">
					<el-input v-model="formData.icodesize" placeholder="请输入" @keyup.native="editnumber" @change="codeSizeChange('edit')"></el-input>
				</el-form-item>
				<el-form-item label="选择赠品" prop="zpcode">
					<el-row>
						<el-col :span="20">
							<el-select v-model="formData.zpcode" placeholder="请选择" filterable remote :remote-method="searchZpList" :disabled="editZpDisabled">
								<el-option
									v-for="item in zpDatas"
									:key="item.code"
									:label="item.name"
									:value="item.code">
								</el-option>
							</el-select>
						</el-col>
						<el-col :span="4" style="width: 20px;">
							<el-tooltip content="清除选择的赠品" placement="top" effect="light">
								<el-button @click="clearInventory('editzp')" icon="el-icon-close" :disabled="editZpDisabled"></el-button>
							</el-tooltip>
						</el-col>
						<el-col :span="24" style="width: 100px;" v-show="steptype == 1 || steptype == 4">
							<el-tooltip content="赠品跟随商品" placement="top" effect="light">
								<el-button type="primary" @click="followInventory('editzp')" :disabled="editZpDisabled">跟随商品</el-button>
							</el-tooltip>
						</el-col>
					</el-row>
				</el-form-item>
				<el-form-item label="赠品数量" prop="zpcodesize">
					<el-input v-model="formData.zpcodesize" placeholder="请输入" @keyup.native="editzpnumber" :disabled="editZpDisabled"></el-input>
				</el-form-item>
				<el-form-item label="备    注" prop="">
					<el-input v-model="formData.remark" placeholder="请输入"></el-input>
				</el-form-item>
        	</el-form>
            <div slot="footer" class="dialog-footer">
              	<el-button @click="closeDialog" class="hzpbtn-close">关 闭</el-button>
                <el-button type="primary" @click="handleSubmit('formData')" v-loading="loading" class="hzpbtn-primary">保 存</el-button>
            </div>
        </el-dialog>
	</section>
</template>
<script>
	// 促销标签
	import { addProcesslabel } from '../../api/api';
	import { editProcesslabel } from '../../api/api';
	// 标签商品
	import { getProcesslabelInventoryList } from '../../api/api';
	import { addProcesslabelInventory } from '../../api/api';
	import { editProcesslabelInventory } from '../../api/api';
	import { deleteProcesslabelinventory, deleteProcesslabelinventory_new } from '../../api/api';
	import { enableProcesslabelInventory } from '../../api/api';
	import { sortBanner } from '../../api/api';
	// 商品筛选
	import { inventoryVague } from '../../api/api';
	import util from '../../common/js/util';
	import emEnum from '../../common/js/emEnum';
	// 行列拖拽
	import Sortable from 'sortablejs';
	// 层级组件
	import LayerPath from '../LayerPath';

	export default {
		components: {
			LayerPath
		},
		data() {
			return {
				sysCnName: emEnum.cnName,  // 中文名称
				sysEnName: emEnum.enName,  // 英文名称

				loginId: '',   // 登录用户

				pid: '',       // 促销活动id
				sid: '',       // 促销步骤id
				steptitle: '', // 步骤名称
				steptype: '',  // 促销类型
				plid: '',      // 促销标签id

				submitSortDisabled: true,  // 提交排序不可用
				idArr: [],        // 编号
				sequenceArr: [],  // 序号

				loading: false,
				containerHeight: 0,
				datas: [],     // 显示数据
				allDatas: [],  // 全数据
				// 促销标签
				labelCodeDisabled: false,  // 促销标签的编号是否可编辑
				steptitleDisabled: true,   // 步骤名称不可编辑
				submitDisabled: false,     // 提交按钮是否可用
				labelFormData: {
					plid: '',      // 促销标签编号
					// code: '',      // 促销标签code
					labelname:'',  // 促销标签名称
					// startime: '',  // 开始时间
					// endtime:'',    // 结束时间
					remark: '',    // 促销标签备注
				},
				labelRules: {
					// code: [
					// 	{ required: true, message: '请输入标签编号', trigger: 'blur' }
					// ],
					labelname: [
						{ required: true, message: '请输入标签名称', trigger: 'blur' }
					],
					// startime: [
					// 	{ required: true, message: '请选择开始日', trigger: 'change' }
					// ],
					// endtime: [
					// 	{ required: true, message: '请选择结束日', trigger: 'change' }
					// ]
				},

				// 添加标签商品
				addFormData: {
					icode: '',      // 商品code
					zpcode:'',      // 赠品code值
					icodesize: '',  // 商品数量
					zpcodesize:'',  // 赠品数量
					remark: '',     // 备注
				},
				addRules: {
					icode: [
						{ required: true, message: '请选择商品', trigger: 'change' }
					],
					zpcode: [
						{ required: true, message: '请选择赠品商品', trigger: 'change' }
					],
					icodesize: [
						{ required: true, message: '请输入商品数量', trigger: 'blur' }
					],
					zpcodesize: [
						{ required: true, message: '请输入赠品数量', trigger: 'blur' }
					]
				},
				vagueContentSp: '',  // 商品筛选
				spDatas: [],         // 商品集合
				vagueContentZp: '',  // 赠品筛选
				zpDatas: [],         // 赠品集合

				addZpDisabled: true,    // 新增赠品不可用
				editZpDisabled: true,   // 编辑赠品不可用
				// 编辑标签商品
				dialogTitle: '',     // 对话框标题 
				editDialogVisible: false,  // 编辑对话框
				formData: {
					Pliid: '',      // 促销标签商品关联编号
					icode: '',      // 商品code
					zpcode:'',      // 赠品code值
					icodesize: '',  // 商品数量
					zpcodesize:'',  // 赠品数量
					remark: '',     // 备注
				},
				rules: {
					icode: [
						{ required: true, message: '请选择商品', trigger: 'change' }
					],
					zpcode: [
						{ required: true, message: '请选择赠品商品', trigger: 'change' }
					],
					icodesize: [
						{ required: true, message: '请输入商品数量', trigger: 'blur' }
					],
					zpcodesize: [
						{ required: true, message: '请输入赠品数量', trigger: 'blur' }
					]
				},
				
				vagueContent: '',    // 快速搜索内容
			}
		},
		methods: {
			// 数字校验
			number(){
				this.addFormData.icodesize = this.addFormData.icodesize.replace(/[^\.\d]/g,'');
				this.addFormData.icodesize = this.addFormData.icodesize.replace('.','');
				if(!!this.addFormData.icodesize && parseInt(this.addFormData.icodesize) === 0) {  // 数量必须大于0
					this.addFormData.icodesize = 1;
				}
			},
			zpnumber(){
				this.addFormData.zpcodesize = this.addFormData.zpcodesize.replace(/[^\.\d]/g,'');
				this.addFormData.zpcodesize = this.addFormData.zpcodesize.replace('.','');
				if(!!this.addFormData.zpcodesize && parseInt(this.addFormData.zpcodesize) === 0) {  // 数量必须大于0
					this.addFormData.zpcodesize = 1;
				}
			},
			editnumber(){
				this.formData.icodesize = this.formData.icodesize.replace(/[^\.\d]/g,'');
				this.formData.icodesize = this.formData.icodesize.replace('.','');
				if(!!this.formData.icodesize && parseInt(this.formData.icodesize) === 0) {  // 数量必须大于0
					this.formData.icodesize = 1;
				}
			},
			editzpnumber(){
				this.formData.zpcodesize = this.formData.zpcodesize.replace(/[^\.\d]/g,'');
				this.formData.zpcodesize = this.formData.zpcodesize.replace('.','');
				if(!!this.formData.zpcodesize && parseInt(this.formData.zpcodesize) === 0) {  // 数量必须大于0
					this.formData.zpcodesize = 1;
				}
			},
			// 数量
			formatIcodesize: function (row, column) {
				if(!!row.icodesize && row.icodesize !== "0") return row.icodesize;
				else return '';
			},
			// 赠品显示处理
			formatZpname: function (row, column) {
				if(!!row.zpcode) return row.zpname;
				else return '';
			},
			formatZpcodesize: function (row, column) {
				if(!!row.zpcode) return row.zpcodesize;
				else return '';
			},
			// 编辑促销标签
			handleLabelSubmit: function (formName) {
				this.$refs[formName].validate((valid) => {
          			if (valid) {
						let _this = this;
						_this.$confirm('确认' + (!_this.labelFormData.plid? '添加' : '编辑') + '促销标签？', '提示', {
							type: 'warning'
						}).then(() => {
							_this.loading = true;
							if(!_this.labelFormData.plid) {  // 添加
								let para = {
									// code: _this.labelFormData.code,
									pid: _this.pid,
									sid: _this.sid,
									labelname: _this.labelFormData.labelname,
									// startime: _this.labelFormData.startime,
									// endtime: _this.labelFormData.endtime,
									remark: _this.labelFormData.remark,
								};
								addProcesslabel(para, _this)
								.then(res=>res.data)
								.then(data => {
									_this.loading = false;
									let { msg, code } = data;
									if (code !== "0") {
										_this.$message({
											message: msg,
											type: 'error'
										});
									} else {
										_this.$message({
											message: msg,
											type: 'success'
										});
										// 添加以后按钮不可用（暂），返回id后需把id给plid
										let result = data.data.list;
										_this.labelFormData.plid = result.id;
										//_this.submitDisabled = true;
									}
								});
							} else {  // 编辑数据
								let para = {
									plid: _this.labelFormData.plid,
									// code: _this.labelFormData.code,
									labelname: _this.labelFormData.labelname,
									// startime: _this.labelFormData.startime,
									// endtime: _this.labelFormData.endtime,
									remark: _this.labelFormData.remark,
								};
								editProcesslabel(para, _this)
								.then(res=>res.data)
								.then(data => {
									_this.loading = false;
									let { msg, code } = data;
									if (code !== "0") {
										_this.$message({
											message: msg,
											type: 'error'
										});
									} else {
										_this.$message({
											message: msg,
											type: 'success'
										});
									}
								});
							}
						}).catch((error) => {
							if(error!=='cancel') {
								_this.$message({
									message: error.message? error.message : error,
									type: 'error'
								});
							}
						});
					} else {
						return false;
					}
				});
			},
			// 促销标签改变
			promotionLabelChange: function (pid, sid, st, sp, plid, ln, s, e, r) {
				// 回调
				this.$refs.LayerPath.callBack();
				this.pid = pid;
				this.sid = sid;
				this.steptitle = st;
				this.steptype = sp;
				this.plid = plid;
				this.labelFormData.plid = plid;
				if(!!this.labelFormData.plid) {  // 编辑
					this.labelCodeDisabled = true;
					this.labelFormData.labelname = ln;
					// this.labelFormData.code = c;
					this.labelFormData.startime = s;
					this.labelFormData.endtime = e;
					this.labelFormData.remark = r;
				} else {
					this.labelCodeDisabled = false;
				}
				this.getData();
			},
			// 获取列表
			getData: function () {
				let _this = this;
				// 先清空，防止出错
				// _this.idArr = [];
				// _this.sequenceArr = [];
				if(!!_this.labelFormData.plid) {
					// 执行操作
					_this.loading = true;
					_this.closeDialog();  // 关闭编辑对话框
					let para = {
						plid: _this.labelFormData.plid,
					};
					getProcesslabelInventoryList(para, _this)
					.then(res=>res.data)
					.then(data => {
						_this.loading = false;
						let { code, msg } = data;
						if (code !== "0") {
							_this.$message({
								message: msg,
								type: 'error'
							});
						} else {
							data = data.data.list;
							_this.allDatas = data;
							_this.datas = _this.allDatas;
							// _this.datas.forEach(item => {
							// 	_this.sequenceArr.push(item.sequence);
							// });
						}
					});
				}
			},
			// 搜索数据
			searchSpList: function (query) {
				let _this = this;
				// 执行操作
				_this.vagueContentSp = query;
				_this.loading = true;
				let para = {
					vague: _this.vagueContentSp,
					pageNum: 1,      // 暂时增加分页，防止搜索不全，后面接口改好可去掉
					pageSize: 10000
				};
				inventoryVague(para, _this)
				.then(res=>res.data)
				.then(data => {
					_this.loading = false;
					let { code, msg } = data;
					if (code !== "0") {
						_this.$message({
							message: msg,
							type: 'error'
						});
					} else {
						data = data.data.list;
						_this.spDatas = data.filter(item => item.status === 0);
					}
				});
			},
			searchZpList: function (query) {
				let _this = this;
				// 执行操作
				_this.vagueContentZp = query;
				_this.loading = true;
				let para = {
					vague: _this.vagueContentZp,
				};
				inventoryVague(para, _this)
				.then(res=>res.data)
				.then(data => {
					_this.loading = false;
					let { code, msg } = data;
					if (code !== "0") {
						_this.$message({
							message: msg,
							type: 'error'
						});
					} else {
						data = data.data.list;
						_this.zpDatas = data.filter(item => item.status === 0);
					}
				});
			},
			// 前端搜索数据
			searchList: function () {
				let _this = this;
				// 如果有搜索数据，则模糊搜索
				if(!!_this.vagueContent) {
					_this.datas = _this.allDatas.filter(item => (!!item.name && item.name.indexOf(_this.vagueContent) >= 0) || (!!item.zpname && item.zpname.indexOf(_this.vagueContent) >= 0));
				} else {
					_this.datas = _this.allDatas;
				}
			},
			// 删除数据
			delData: function (index, row) {
				let _this = this;
				_this.$confirm('确认删除该促销标签商品？', '提示', {
					type: 'warning'
				}).then(() => {
					_this.loading = true;
					let para = {
						pliid: row.id,
						pid: _this.pid,
						uid: _this.loginId
					};
					deleteProcesslabelinventory(para, _this)
					.then(res=>res.data)
					.then(data => {
						_this.loading = false;
						let { msg, code } = data;
						if (code !== "0") {
							_this.$message({
								message: msg,
								type: 'error'
							});
						} else {
							_this.$message({
								message: msg,
								type: 'success'
							});
							_this.getData();
						}
					});
				}).catch((error) => {
					if(error!=='cancel') {
						_this.$message({
							message: error.message? error.message : error,
							type: 'error'
						});
					}
				});
			},
			// 修改数据状态（禁用、启用）
			enableData: function (index, row) {
				let _this = this;
				_this.$confirm('确认' + (row.status === 0? '禁用' : '启用') + '该促销标签商品？', '提示', {
					type: 'warning'
				}).then(() => {
					_this.loading = true;
					let para = {
						pliid: row.id,
						status: row.status === 0? '1' : '0'
					};
					enableProcesslabelInventory(para, _this)
					.then(res=>res.data)
					.then(data => {
						_this.loading = false;
						let { msg, code } = data;
						if (code !== "0") {
							_this.$message({
								message: msg,
								type: 'error'
							});
						} else {
							_this.$message({
								message: msg,
								type: 'success'
							});
							_this.getData();
						}
					});
				}).catch((error) => {
					if(error!=='cancel') {
						_this.$message({
							message: error.message? error.message : error,
							type: 'error'
						});
					}
				});
			},
			// 重置输入
			resetForm: function (formName) {
				this.$refs[formName].resetFields();
			},
			// 编辑数据
			editData: function (index, row) {
				let formName = 'formData';
				// 打开对话框
				this.editDialogVisible = true;
				// 默认值
				if(!row) {
					this.dialogTitle = "添加";
					if(this.$refs[formName]) this.$refs[formName].resetFields();
					this.formData.pliid = "";
					this.formData.icode = "";
					this.formData.zpcode = "";
					this.formData.icodesize = "";
					this.formData.zpcodesize = "";
					this.formData.remark = "";
				} else {
					this.dialogTitle = "编辑";
					this.formData.pliid = row.id;
					this.formData.icode = row.icode;
					this.formData.zpcode = row.zpcode;
					this.formData.icodesize = this.formatIcodesize(row);
					this.formData.zpcodesize = !!row.zpcode? row.zpcodesize : '';
					this.formData.remark = row.remark;
					// 如果没有则在集合中新增
					if(!!row.icode && !this.spDatas.some(a => a.code === row.icode)) {
						this.spDatas.push({code: row.icode, name: row.name});
					}
					if(!!row.zpcode && !this.zpDatas.some(a => a.code === row.zpcode)) {
						this.zpDatas.push({code: row.zpcode, name: row.zpname});
					}
					// 赠品是否可编辑
					this.codeSizeChange('edit');
					
					// this.editZpDisabled = this.steptype == 1 && !this.formData.icodesize;
				}
			},
			// 打开编辑对话框
			openDialog: function () {
				// 焦点放到获取焦点的输入框
				let ele = document.getElementById('focus-ele');
				if(!!ele) {
					ele.focus();
				}
			},
			// 关闭编辑对话框
			closeDialog: function () {
				this.editDialogVisible = false;
			},
			// 提交数据
			// 新增
			handleAddSubmit: function (formName) {
				this.$refs[formName].validate((valid) => {
          			if (valid) {
						let _this = this;
						// 判断是否已有此商品
						if(!!_this.addFormData.icode) {
							let sp = _this.allDatas.filter(item => item.icode === _this.addFormData.icode);
							if(!!sp && sp.length > 0) {
								_this.$message({
									message: '商品【' + sp[0].iname + '】已存在，不能重复添加！',
									type: 'error'
								});
								return;
							}
						}

						// _this.$confirm('确认添加促销标签商品？', '提示', {
						// 	type: 'warning'
						// }).then(() => {
						_this.loading = true;
						let para = {
							plid: _this.labelFormData.plid,
							remark: _this.addFormData.remark,
						};
						// code选择才传
						if(!!_this.addFormData.icode) para['icode'] = _this.addFormData.icode;
						if(!!_this.addFormData.zpcode) para['zpcode'] = _this.addFormData.zpcode;
						// 数量输入才传
						if(!!_this.addFormData.icodesize || _this.addFormData.icodesize === 0 || _this.addFormData.icodesize === '0') para['icodesize'] = _this.addFormData.icodesize;
						if(!!_this.addFormData.zpcodesize || _this.addFormData.zpcodesize === 0 || _this.addFormData.zpcodesize === '0') para['zpcodesize'] = _this.addFormData.zpcodesize;

						addProcesslabelInventory(para, _this)
						.then(res=>res.data)
						.then(data => {
							_this.loading = false;
							let { msg, code } = data;
							if (code !== "0") {
								_this.$message({
									message: msg,
									type: 'error'
								});
							} else {
								_this.$message({
									message: msg,
									type: 'success'
								});
								// 清空数据
								_this.addFormData.icode = '';
								_this.addFormData.zpcode = '';
								_this.addFormData.icodesize = '';
								_this.addFormData.zpcodesize = '';
								_this.addFormData.remark = '';
								_this.codeSizeChange('add');
								// 获取数据
								_this.getData();
							}
						});
						// }).catch((error) => {
						// 	if(error!=='cancel') {
						// 		_this.$message({
						// 			message: error.message? error.message : error,
						// 			type: 'error'
						// 		});
						// 	}
						// });
					} else {
						return false;
					}
				});
			},
			// 编辑
			handleSubmit: function (formName) {
				this.$refs[formName].validate((valid) => {
          			if (valid) {
						let _this = this;
						_this.$confirm('确认' + _this.dialogTitle + '促销标签商品？', '提示', {
							type: 'warning'
						}).then(() => {
							_this.loading = true;
							if(!_this.formData.pliid) {  // 添加
								let para = {
									plid: _this.labelFormData.plid,
									remark: _this.formData.remark,
								};
								// code选择才传
								if(!!_this.formData.icode) para['icode'] = _this.formData.icode;
								if(!!_this.formData.zpcode) para['zpcode'] = _this.formData.zpcode;
								// 数量输入才传
								if(!!_this.formData.icodesize || _this.formData.icodesize === 0 || _this.formData.icodesize === '0') para['icodesize'] = _this.formData.icodesize;
								if(!!_this.formData.zpcodesize || _this.formData.zpcodesize === 0 || _this.formData.zpcodesize === '0') para['zpcodesize'] = _this.formData.zpcodesize;

								addProcesslabelInventory(para, _this)
								.then(res=>res.data)
								.then(data => {
									_this.loading = false;
									let { msg, code } = data;
									if (code !== "0") {
										_this.$message({
											message: msg,
											type: 'error'
										});
									} else {
										_this.$message({
											message: msg,
											type: 'success'
										});
										_this.getData();
									}
								});
							} else {  // 编辑数据
								let para = {
									pliid: _this.formData.pliid,
									plid: _this.labelFormData.plid,
									// 按实际值传，传空也要传，涉及到修改为空的情况
									icode: _this.formData.icode,
									zpcode: _this.formData.zpcode,
									icodesize: _this.formData.icodesize,
									zpcodesize: _this.formData.zpcodesize,
									remark: _this.formData.remark,
								};
								// code选择才传
								// if(!!_this.formData.icode) para['icode'] = _this.formData.icode;
								// if(!!_this.formData.zpcode) para['zpcode'] = _this.formData.zpcode;
								// // 数量输入才传
								// if(!!_this.formData.icodesize || _this.formData.icodesize === 0 || _this.formData.icodesize === '0') para['icodesize'] = _this.formData.icodesize;
								// if(!!_this.formData.zpcodesize || _this.formData.zpcodesize === 0 || _this.formData.zpcodesize === '0') para['zpcodesize'] = _this.formData.zpcodesize;
								
								editProcesslabelInventory(para, _this)
								.then(res=>res.data)
								.then(data => {
									_this.loading = false;
									let { msg, code } = data;
									if (code !== "0") {
										_this.$message({
											message: msg,
											type: 'error'
										});
									} else {
										_this.$message({
											message: msg,
											type: 'success'
										});
										_this.getData();
									}
								});
							}
						}).catch((error) => {
							if(error!=='cancel') {
								_this.$message({
									message: error.message? error.message : error,
									type: 'error'
								});
							}
						});
					} else {
						return false;
					}
				});
			},
			// 行拖拽
			rowDrop: function () {
				const tbody = document.querySelector('.el-table__body-wrapper tbody');
				const _this = this;
				Sortable.create(tbody, {
					onEnd({ newIndex, oldIndex }) {
						if(_this.sequenceArr.length > 1) {  // 大于1行才有排序的必要
							const currRow = _this.datas.splice(oldIndex, 1)[0];
							_this.datas.splice(newIndex, 0, currRow);
							_this.submitSortDisabled = false;
							// 设置序号
							for(let i = 0; i < _this.datas.length; i++) {
								_this.datas[i].sequence = _this.sequenceArr[i];
							}
						}
					}
				});
			},
			// 提交排序
			submitSort: function () {
				let _this = this;
				// 编号
				_this.idArr = _this.datas.map(item => {
					return item.id;
				});
				// 提交数据
				if(!!_this.idArr && !!_this.sequenceArr && _this.idArr.length === _this.sequenceArr.length) {				
					_this.$confirm('确认提交排序结果？', '提示', {
						type: 'warning'
					}).then(() => {
						// _this.loading = true;
						// let para = {
						// 	bid: _this.idArr,
						// 	sequence: _this.sequenceArr
						// };
						// sortBanner(para, _this)
						// .then(res=>res.data)
						// .then(data => {
						// 	_this.loading = false;
						// 	let { msg, code } = data;
						// 	if (code !== "0") {
						// 		_this.$message({
						// 			message: msg,
						// 			type: 'error'
						// 		});
						// 	} else {
						// 		_this.$message({
						// 			message: msg,
						// 			type: 'success'
						// 		});
						// 		_this.getData();
						// 	}
						// });
					}).catch((error) => {
						if(error!=='cancel') {
							_this.$message({
								message: error.message? error.message : error,
								type: 'error'
							});
						}
					});
				} else {
					_this.$message({
						message: '排序数据出现问题，请刷新后重试！',
						type: 'error'
					});
				}
			},
			// table 行改变事件
			handleRowChange: function (row, event, column) {
				
			},
			// 上一步骤
			preStep: function () {
				this.$router.push({ path: '/processLabel', query: {pid: this.pid, sid: this.sid, st: this.steptitle, sp: this.steptype}});
			},
			// 导出Execl
			exportExcel: function () {
				let _this = this;
				if(!!_this.labelFormData.plid) {
					// 执行操作
					_this.loading = true;
					_this.closeDialog();  // 关闭编辑对话框
					let para = {
						plid: _this.labelFormData.plid,
					};
					getProcesslabelInventoryList(para, _this)
					.then(res=>res.data)
					.then(data => {
						_this.loading = false;
						let { code, msg } = data;
						if (code !== "0") {
							_this.$message({
								message: msg,
								type: 'error'
							});
						} else {
							data = data.data.list;
							_this.doExport(data);
						}
					});
				}
			},
			// 执行导出
			doExport: function (tabData) {
				let _this = this;
				// 导出数据
				if(!!tabData && tabData.length > 0) {
					let i = 1;
					tabData.forEach(item => {
						item.id = i;
						item.refsaleprice = parseFloat(item.refsaleprice);
						item.icodesize = parseFloat(item.icodesize);
						item.zpname = _this.formatZpname(item);
						item.zpcodesize = _this.formatZpcodesize(item);
						i++;
					});
					// 获取文件名
					let fileName = _this.sysCnName + "_" + util.formatDate.format(new Date(), "yyyy-MM-dd HH:mm:ss") + "_" + _this.$root.$route.name;
					// 表头和对应字段
					let tHeader = [ '序号', '商品名称', '单价', '数量', '赠品商品', '赠品数量', '备注' ];
					let filterVal = [ 'id', 'name', 'refsaleprice', 'icodesize', 'zpname', 'zpcodesize', 'remark' ];
					util.exportJsonToExcel(fileName, JSON.stringify(tabData), tHeader, filterVal);
				} else {
					this.$message({
						message: "没有需要导出的数据",
						type: 'warning'
					});
				}
			},
			// 清除选择的商品
			clearInventory: function (val) {
				if(val == 'sp') {
					this.addFormData.icode = '';
				} else if(val == 'zp') {
					this.addFormData.zpcode = '';
				} else if(val == 'editsp') {
					this.formData.icode = '';
				} else if(val == 'editzp') {
					this.formData.zpcode = '';
				} 
			},
			// 赠品跟随商品
			followInventory: function (val) {
				let _this = this;
				if(val == 'zp') {
					// 如果没有则在集合中新增
					if(!!_this.addFormData.icode && !_this.zpDatas.some(a => a.code === _this.addFormData.icode)) {
						let sp = _this.spDatas.find(item=>{
							return item.code === _this.addFormData.icode;
						});
						if(!!sp) {
							_this.zpDatas.push({code: sp.code, name: sp.name});
						}
					}
					_this.addFormData.zpcode = _this.addFormData.icode;
				} else if(val == 'editzp') {
					// 如果没有则在集合中新增
					if(!!_this.formData.icode && !_this.zpDatas.some(a => a.code === _this.formData.icode)) {
						let sp = _this.spDatas.find(item=>{
							return item.code === _this.formData.icode;
						});
						if(!!sp) {
							_this.zpDatas.push({code: sp.code, name: sp.name});
						}
					}
					_this.formData.zpcode = _this.formData.icode;
				}
			},
			// 商品数量改变事件
			codeSizeChange: function (val) {
				if(val === 'add') {
					// 根据促销类型设置可选值
					if(this.steptype == 1 || this.steptype == 4) {
						if(!this.addFormData.icodesize) {
							this.addZpDisabled = true;
							this.addRules.zpcode[0].required = false;
							this.addRules.zpcodesize[0].required = false;
							// 清空赠品数据
							this.addFormData.zpcode = '';
							this.addFormData.zpcodesize = '';
						} else {
							this.addZpDisabled = false;
							this.addRules.zpcode[0].required = true;
							this.addRules.zpcodesize[0].required = true;
						}
					} else {
						this.addZpDisabled = false;
						this.addRules.zpcode[0].required = true;
						this.addRules.zpcodesize[0].required = true;
					}
				} else if(val === 'edit') {
					// 根据促销类型设置可选值
					if(this.steptype == 1 || this.steptype == 4) {
						if(!this.formData.icodesize) {
							this.editZpDisabled = true;
							this.rules.zpcode[0].required = false;
							this.rules.zpcodesize[0].required = false;
							// 清空赠品数据
							this.formData.zpcode = '';
							this.formData.zpcodesize = '';
						} else {
							this.editZpDisabled = false;
							this.rules.zpcode[0].required = true;
							this.rules.zpcodesize[0].required = true;
						}
					} else {
						this.editZpDisabled = false;
						this.rules.zpcode[0].required = true;
						this.rules.zpcodesize[0].required = true;
					}
				}
			}
		},
		// 创建VUE实例后的钩子
		created: function () {
			// 登录ID
			this.loginId = util.userData.getLoginUserFieldValue(emEnum.loginUserFileds.uid, this);
			// 获取参数值
			let query = this.$route.query;
			this.pid = query.pid;
			this.sid = query.sid;
			this.steptitle = query.st;
			this.steptype = query.sp;
			this.plid = query.plid;
			this.labelFormData.plid = query.plid;
			if(!!this.labelFormData.plid) {  // 编辑
				this.labelCodeDisabled = true;
				this.labelFormData.labelname = query.ln;
				// this.labelFormData.code = query.c;
				this.labelFormData.startime = query.s;
				this.labelFormData.endtime = query.e;
				this.labelFormData.remark = query.r;
			} else {
				this.labelCodeDisabled = false;
			}
		},
		// 挂载到DOM后的钩子
		mounted: function () {
			let _this = this;
			_this.containerHeight = _this.$refs.processLabelInventorycontainer.offsetHeight;

			// 改变组件的值
			_this.$refs.LayerPath.changeVal(1, _this.pid);
			_this.$refs.LayerPath.changeVal(2, _this.sid);
			_this.$refs.LayerPath.changeVal(3, _this.plid);
			// 获取数据
			_this.getData();
			// 根据促销类型设置可选值
			if(_this.steptype == 1 || _this.steptype == 4) {
				_this.addRules.icode[0].required = true;
				_this.addRules.zpcode[0].required = false;
				_this.addRules.zpcodesize[0].required = false;
				_this.rules.icode[0].required = true;
				_this.rules.zpcode[0].required = false;
				_this.rules.zpcodesize[0].required = false;
			} else {
				_this.addRules.icode[0].required = false;
				_this.addRules.zpcode[0].required = true;
				_this.addRules.zpcodesize[0].required = true;
				_this.rules.icode[0].required = false;
				_this.rules.zpcode[0].required = true;
				_this.rules.zpcodesize[0].required = true;
				// 赠品可编辑
				_this.addZpDisabled = false;
				_this.editZpDisabled = false;
			}
			// 阻止默认行为
			document.body.ondrop = function (event) {
				event.preventDefault();
				event.stopPropagation();
			};
			// 行拖拽
			_this.rowDrop()
			// 绑定窗口尺寸改变事件改变数据表的高度
			window.onresize = () => {
				return (() => {
					// 为了避免频繁触发resize函数导致页面卡顿，使用定时器
					if (!this.timer && !!_this.$refs.processLabelInventorycontainer) {
						this.timer = true
						_this.containerHeight = _this.$refs.processLabelInventorycontainer.offsetHeight;
						let that = this
						setTimeout(function () {
							that.timer = false
						}, 100);
					}
				})();
			}
		}
	};
</script>

<style lang="scss">
.processLabelInventory-container {
	height: calc(100vh - 120px);
	.plTitle {
		font-size: 18px;
	}
}
</style>