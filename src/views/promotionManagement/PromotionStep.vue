<template>
	<section class="promotionstep-container common-container" ref="promotionstepcontainer">
		<!--操作-->
		<section class="data-oper">
			<el-button type="primary" @click="preStep" class="hzpbtn-primary">返回到促销管理</el-button>
			<el-button type="primary" @click="editData" class="hzpbtn-primary" v-loading="loading">添加</el-button>
			<el-button type="primary" @click="exportExcel" class="hzpbtn-primary">导出</el-button>
			<el-button type="primary" @click="submitSort" class="hzpbtn-primary" v-loading="loading" :disabled="submitSortDisabled">提交排序</el-button>
			<!--位置显示-->
			<LayerPath class="layerPath" @promotionChange='promotionChange' ref='LayerPath'></LayerPath>
		</section>
		<!--列表-->
		<el-table :data="datas" :stripe="true" :height="containerHeight - 55" highlight-current-row v-loading="loading" id="out-table" 
			class="data-content" border row-key="id" @row-click="handleRowChange">
			<el-table-column label="序号" align="center" type="index" width="60">
			</el-table-column>
			<!-- <el-table-column prop="code" label="编号" align="center" min-width="20%">
			</el-table-column> -->
			<el-table-column prop="steptitle" label="名称" align="center" min-width="20%">
			</el-table-column>
			<el-table-column prop="steptype" label="类型" :formatter="formatStepType" align="center" min-width="20%">
			</el-table-column>
			<el-table-column prop="stepamount" label="促销区间" :formatter="formatStepAmount" align="center" min-width="20%">
			</el-table-column>
			<el-table-column prop="monthoverdate" label="过期日" align="center" min-width="20%">
			</el-table-column>
			<el-table-column prop="remark" label="备注" align="center" min-width="20%">
			</el-table-column>
			<el-table-column label="操作" align="center" width="270">
				<template slot-scope="scope">
					<label class="hzpedit" @click.prevent="delData(scope.$index, scope.row)">删除</label>|
					<label class="hzpedit" @click.prevent="enableData(scope.$index, scope.row)">{{scope.row.status===0? '禁用' : '启用'}}</label>|
					<label class="hzpedit" @click.prevent="editData(scope.$index, scope.row)">编辑</label>|
					<label class="hzpedit" @click.prevent="nextStep(scope.$index, scope.row)">促销标签</label>
				</template>
			</el-table-column>
		</el-table>
		<!--编辑数据-->
        <el-dialog :title="dialogTitle + '数据'" :visible.sync="editDialogVisible" class="dialog-container" width="500px" @opened="openDialog">
			<el-form :model="formData" :rules="rules" ref="formData" label-width="100px" class="form-data" size="mini">
				<!-- <el-form-item label="编    号" prop="code">
					<el-input v-model="formData.code" placeholder="请输入" :disabled="codeDisabled" id="focus-ele"></el-input>
				</el-form-item> -->
				<el-form-item label="名    称" prop="steptitle">
					<el-input v-model="formData.steptitle" placeholder="请输入" id="focus-ele"></el-input>
				</el-form-item>
				<el-form-item label="类    型" prop="steptype">
					<el-select v-model="formData.steptype" placeholder="请选择" @change="steptypeChange">
						<el-option
							v-for="item in stepTypeOptions"
							:key="item.key"
							:label="item.value"
							:value="item.key">
						</el-option>
					</el-select>
				</el-form-item>
				<el-form-item label="促销区间" prop="minstepamount" v-show="stepamountShow">
					<el-input v-model="formData.minstepamount" placeholder="请输入区间最小值" @keyup.native="cashMin" @change="cashChange('Min')" style="width: 45%;"></el-input>
					<span>,</span>
					<el-input v-model="formData.maxstepamount" placeholder="请输入区间最大值" @keyup.native="cashMax" @change="cashChange('Max')" style="width: 45%;"></el-input>
				</el-form-item>
				<el-form-item label="过期日" prop="monthoverdate" v-show="monthoverdateShow">
					<el-select v-model="formData.monthoverdate" placeholder="请选择">
						<el-option
							v-for="item in 31"
							:key="item"
							:label="item"
							:value="item">
						</el-option>
					</el-select>
				</el-form-item>
				<el-form-item label="备    注" prop="">
					<el-input v-model="formData.remark" placeholder="请输入" type="textarea" :rows="4"></el-input>
				</el-form-item>
        	</el-form>
            <div slot="footer" class="dialog-footer">
              	<el-button @click="closeDialog" class="hzpbtn-close">关 闭</el-button>
                <el-button type="primary" @click="handleSubmit('formData')" v-loading="loading" class="hzpbtn-primary">保 存</el-button>
            </div>
        </el-dialog>
	</section>
</template>
<script>
	import { getPromotionstepList } from '../../api/api';
	import { addPromotionstep } from '../../api/api';
	import { editPromotionstep } from '../../api/api';
	import { deletePromotionstep, deletePromotionstep_new } from '../../api/api';
	import { enablePromotionstep } from '../../api/api';
	import { sortPromotionstep } from '../../api/api';
	import util from '../../common/js/util';
	import emEnum from '../../common/js/emEnum';
	// 行列拖拽
	import Sortable from 'sortablejs';
	// 层级组件
	import LayerPath from '../LayerPath';

	export default {
		components: {
			LayerPath
		},
		data() {
			return {
				sysCnName: emEnum.cnName,  // 中文名称
				sysEnName: emEnum.enName,  // 英文名称

				layer: 2,  // 层级

				loginId: '',   // 登录用户

				pid: '',             // 促销活动id

				submitSortDisabled: true,  // 提交排序不可用
				idArr: [],        // 编号
				sequenceArr: [],  // 序号

				loading: false,
				containerHeight: 0,
				datas: [],

				areaDatas: [],  // 区域数据
				codeDisabled: true,  // code是否可编辑
				roleOptions: emEnum.roleOptions, // 角色集合
				stepTypeOptions: emEnum.stepTypeOptions, // 促销类型集合
				dialogTitle: '',   // 对话框标题 
				editDialogVisible: false,  // 编辑对话框
				stepamountShow: true,      // 促销区间可见
				monthoverdateShow: false,  // 特别促销过期日
				formData: {
					psId: '',
					// code:'',
					steptitle: '',
					steptype: '',
					minstepamount: '',
					maxstepamount: '',
					remark: '',
					monthoverdate: '',
				},
				rules: {
					// code: [
					// 	{ required: true, message: '请输入步骤编号', trigger: 'blur' }
					// ],
					steptitle: [
						{ required: true, message: '请输入步骤标题', trigger: 'blur' }
					],
					steptype: [
						{ required: true, message: '请选择类型', trigger: 'change' }
					],
					minstepamount: [
						{ required: true, message: '请输入促销区间', trigger: 'blur' }
					],
					monthoverdate: [
						{ required: true, message: '请选择过期日', trigger: 'change' }
					],
				}
			}
		},
		methods: {
			// 状态显示转换
			formatStatus: function (row, column) {
				return row.status === 0? "已启用" : "已禁用"
			},
			// 开始时间显示格式转换
			formatStartime: function (row, column) {
				if(!!row.startime) {
					return util.formatDate.format(new Date(row.startime.replace(/-/g, '/')), 'yyyy-MM-dd HH:mm:ss');
				} else return row.startime;
			},
			// 结束时间显示格式转换
			formatEndtime: function (row, column) {
				if(!!row.endtime) {
					return util.formatDate.format(new Date(row.endtime.replace(/-/g, '/')), 'yyyy-MM-dd HH:mm:ss');
				} else return row.endtime;
			},
			// 促销类型显示转换
			formatStepType: function (row, column) {
				let typeOp = emEnum.stepTypeOptions.find(item=>{
					return item.key === row.steptype;
				});
				if(!typeOp) return '';
				else return typeOp.value;
			},
			// 促销区间显示转换
			formatStepAmount: function (row, column) {
				let stepAmount = row.stepamount;
				let amountArr = stepAmount.split(',');
				if(!!amountArr && amountArr.length >= 2) {
					let min = parseInt(amountArr[0]);
					let max = parseInt(amountArr[1]);
					if(min === max) {
						return min + '';
					} else if(max === -1) {
						return min + '以上';
					} else {
						return min + ' - ' + max;
					}
				} else {
					return stepAmount;
				}
			},
			// 金额校验
			cashMin: function (){
				this.formData.minstepamount = this.formData.minstepamount.replace(/[^\.\d]/g,'');   // 清除"数字"和"."以外的字符
				this.formData.minstepamount = this.formData.minstepamount.replace(/^\./g, '');      // 第一个字符不能为.
				let temp = this.formData.minstepamount.replace(/\./g,'');  // 出现多个点则只去掉最后一个点
				if(this.formData.minstepamount.length >= temp.length + 2) {
					this.formData.minstepamount = this.formData.minstepamount.substr(0, this.formData.minstepamount.length - 1);
				}
			},
			cashMax: function (){
				this.formData.maxstepamount = this.formData.maxstepamount.replace(/[^\.\d]/g,'');   // 清除"数字"和"."以外的字符
				this.formData.maxstepamount = this.formData.maxstepamount.replace(/^\./g, '');      // 第一个字符不能为.
				let temp = this.formData.maxstepamount.replace(/\./g,'');  // 出现多个点则只去掉最后一个点
				if(this.formData.maxstepamount.length >= temp.length + 2) {
					this.formData.maxstepamount = this.formData.maxstepamount.substr(0, this.formData.maxstepamount.length - 1);
				}
			},
			cashChange: function (val) {
				if(val == 'Min') {
					// 如果最小金额大于最大金额，则提示，并把最小金额置为最大金额
					if(!!this.formData.minstepamount && !!this.formData.maxstepamount) {
						if(parseFloat(this.formData.minstepamount) > parseFloat(this.formData.maxstepamount)) {
							this.$message({
								message: '促销区间最小金额不能大于最大金额！',
								type: 'warning'
							});
							this.formData.minstepamount = this.formData.maxstepamount;
						}
					}
				} else {
					// 如果最大金额小于最小金额，则提示，并把最大金额置为最小金额
					if(!!this.formData.minstepamount && !!this.formData.maxstepamount) {
						if(parseFloat(this.formData.minstepamount) > parseFloat(this.formData.maxstepamount)) {
							this.$message({
								message: '促销区间最大金额不能小于最小金额！',
								type: 'warning'
							});
							this.formData.maxstepamount = this.formData.minstepamount;
						}
					}
				}
			},
			// 促销活动改变
			promotionChange: function (pid) {
				// 回调
				this.$refs.LayerPath.callBack();
				this.pid = pid;
				this.getData();
			},
			// 获取列表
			getData: function () {
				let _this = this;
				// 执行操作
				_this.loading = true;
				_this.closeDialog();  // 关闭编辑对话框
				let para = {
					pid: _this.pid,
				};
				getPromotionstepList(para, _this)
				.then(res=>res.data)
				.then(data => {
					_this.loading = false;
					let { code, msg } = data;
					if (code !== "0") {
						_this.$message({
							message: msg,
							type: 'error'
						});
					} else {
						data = data.data.list;
						// 改变组件的值
						this.$refs.LayerPath.changeData(this.layer, data);
						this.$refs.LayerPath.changeData(this.layer + 1, '');

						_this.datas = data;
						// 先清空，防止出错
						_this.idArr = [];
						_this.sequenceArr = [];
						_this.datas.forEach(item => {
							_this.sequenceArr.push(item.sequence);
						});
					}
				});
			},
			// 删除数据
			delData: function (index, row) {
				let _this = this;
				_this.$confirm('确认删除该促销步骤？', '提示', {
					type: 'warning'
				}).then(() => {
					_this.loading = true;
					let para = {
						psid: row.id,
						pid: _this.pid,
						uid: _this.loginId
					};
					deletePromotionstep(para, _this)
					.then(res=>res.data)
					.then(data => {
						_this.loading = false;
						let { msg, code } = data;
						if (code !== "0") {
							_this.$message({
								message: msg,
								type: 'error'
							});
						} else {
							_this.$message({
								message: msg,
								type: 'success'
							});
							_this.getData();
						}
					});
				}).catch((error) => {
					if(error!=='cancel') {
						_this.$message({
							message: error.message? error.message : error,
							type: 'error'
						});
					}
				});
			},
			// 修改数据状态（禁用、启用）
			enableData: function (index, row) {
				let _this = this;
				_this.$confirm('确认' + (row.status === 0? '禁用' : '启用') + '该促销步骤？', '提示', {
					type: 'warning'
				}).then(() => {
					_this.loading = true;
					let para = {
						psid: row.id,
						status: row.status === 0? '1' : '0'
					};
					enablePromotionstep(para, _this)
					.then(res=>res.data)
					.then(data => {
						_this.loading = false;
						let { msg, code } = data;
						if (code !== "0") {
							_this.$message({
								message: msg,
								type: 'error'
							});
						} else {
							_this.$message({
								message: msg,
								type: 'success'
							});
							_this.getData();
						}
					});
				}).catch((error) => {
					if(error!=='cancel') {
						_this.$message({
							message: error.message? error.message : error,
							type: 'error'
						});
					}
				});
			},
			// 重置输入
			resetForm: function (formName) {
				this.$refs[formName].resetFields();
			},
			// 编辑数据
			editData: function (index, row) {
				let formName = 'formData';
				// 打开对话框
				this.editDialogVisible = true;
				// 默认值
				if(!row) {
					this.dialogTitle = "添加";
					this.codeDisabled = false;
					if(this.$refs[formName]) this.$refs[formName].resetFields();
					this.formData.psid = "";
					// this.formData.code = "";
					this.formData.steptitle = "";
					// 类型默认选中第一个
					this.formData.steptype = this.stepTypeOptions[0].key;
					this.formData.minstepamount = "";
					this.formData.maxstepamount = "";
					this.formData.remark = "";
					this.formData.monthoverdate = "";
				} else {
					this.dialogTitle = "编辑";
					this.codeDisabled = true;
					this.formData.psid = row.id;
					// this.formData.code = row.code;
					this.formData.steptitle = row.steptitle;
					// 类型
					this.formData.steptype = row.steptype;
					// 促销区间
					this.formData.minstepamount = '';
					this.formData.maxstepamount = '';
					if(!!row.stepamount) {
						let splitIndex = row.stepamount.indexOf(',');
						if(splitIndex > -1 && row.stepamount != ',') {
							this.formData.minstepamount = row.stepamount.substr(0, splitIndex);
							let max = row.stepamount.substr(splitIndex + 1, row.stepamount.length - splitIndex -1);
							this.formData.maxstepamount = max == '-1'? '' : max;
						}
					}
					this.formData.remark = row.remark;
					this.formData.monthoverdate = row.monthoverdate;
				}
				this.steptypeChange();
			},
			// 打开编辑对话框
			openDialog: function () {
				// 焦点放到获取焦点的输入框
				let ele = document.getElementById('focus-ele');
				if(!!ele) {
					ele.focus();
				}
			},
			// 关闭编辑对话框
			closeDialog: function () {
				this.editDialogVisible = false;
			},
			// 类型改变事件
			steptypeChange: function () {
				// 根据促销类型做不同的处理
				if(this.formData.steptype === 1 || this.formData.steptype === 4) {
					this.stepamountShow = true;
					this.rules.minstepamount[0].required = true;
					this.monthoverdateShow = false;
					this.rules.monthoverdate[0].required = false;
				} else {
					this.stepamountShow = false;
					this.rules.minstepamount[0].required = false;
					if(this.formData.steptype === 3) {
						this.monthoverdateShow = true;
						this.rules.monthoverdate[0].required = true;
					} else {
						this.monthoverdateShow = false;
						this.rules.monthoverdate[0].required = false;
					}
				}
			},
			// 提交数据
			handleSubmit: function (formName) {
				this.$refs[formName].validate((valid) => {
          			if (valid) {
						let _this = this;
						_this.$confirm('确认' + _this.dialogTitle + '促销步骤？', '提示', {
							type: 'warning'
						}).then(() => {
							_this.loading = true;
							if(!_this.formData.psid) {  // 添加
								let para = {
									// code: _this.formData.code,
									pid: _this.pid,
									remark: _this.formData.remark,
									steptitle: _this.formData.steptitle,
									steptype: _this.formData.steptype,
								};
								// 根据促销类型传不同的参数
								if(_this.formData.steptype === 1 || _this.formData.steptype === 4) {
									para['stepamount'] = _this.formData.minstepamount + ',' + (!!_this.formData.maxstepamount && _this.formData.maxstepamount != ''? _this.formData.maxstepamount : '-1');
									para['monthoverdate'] = '';
								} else if(_this.formData.steptype === 3) {
									para['stepamount'] = '';
									para['monthoverdate'] = _this.formData.monthoverdate;
								} else {
									para['stepamount'] = '';
									para['monthoverdate'] = '';
								}

								addPromotionstep(para, _this)
								.then(res=>res.data)
								.then(data => {
									_this.loading = false;
									let { msg, code } = data;
									if (code !== "0") {
										_this.$message({
											message: msg,
											type: 'error'
										});
									} else {
										_this.$message({
											message: msg,
											type: 'success'
										});
										_this.getData();
									}
								});
							} else {  // 编辑数据
								let para = {
									psid: _this.formData.psid,
									// code: _this.formData.code,
									remark: _this.formData.remark,
									// stepamount: _this.formData.minstepamount + ',' + (!!_this.formData.maxstepamount && _this.formData.maxstepamount != ''? _this.formData.maxstepamount : '-1'),
									steptitle: _this.formData.steptitle,
									steptype: _this.formData.steptype,
								};
								// 根据促销类型传不同的参数
								if(_this.formData.steptype === 1 || _this.formData.steptype === 4) {
									para['stepamount'] = _this.formData.minstepamount + ',' + (!!_this.formData.maxstepamount && _this.formData.maxstepamount != ''? _this.formData.maxstepamount : '-1');
									para['monthoverdate'] = '';
								} else if(_this.formData.steptype === 3) {
									para['stepamount'] = '';
									para['monthoverdate'] = _this.formData.monthoverdate;
								} else {
									para['stepamount'] = '';
									para['monthoverdate'] = '';
								}

								editPromotionstep(para, _this)
								.then(res=>res.data)
								.then(data => {
									_this.loading = false;
									let { msg, code } = data;
									if (code !== "0") {
										_this.$message({
											message: msg,
											type: 'error'
										});
									} else {
										_this.$message({
											message: msg,
											type: 'success'
										});
										_this.getData();
									}
								});
							}
						}).catch((error) => {
							if(error!=='cancel') {
								_this.$message({
									message: error.message? error.message : error,
									type: 'error'
								});
							}
						});
					} else {
						return false;
					}
				});
			},
			// 行拖拽
			rowDrop: function () {
				const tbody = document.querySelector('.el-table__body-wrapper tbody');
				const _this = this;
				Sortable.create(tbody, {
					onEnd({ newIndex, oldIndex }) {
						if(_this.sequenceArr.length > 1) {  // 大于1行才有排序的必要
							const currRow = _this.datas.splice(oldIndex, 1)[0];
							_this.datas.splice(newIndex, 0, currRow);
							_this.submitSortDisabled = false;
							// 设置序号
							for(let i = 0; i < _this.datas.length; i++) {
								_this.datas[i].sequence = _this.sequenceArr[i];
							}
						}
					}
				});
			},
			// 提交排序
			submitSort: function () {
				let _this = this;
				// 编号
				_this.idArr = _this.datas.map(item => {
					return item.id;
				});
				// 提交数据
				if(!!_this.idArr && !!_this.sequenceArr && _this.idArr.length === _this.sequenceArr.length) {				
					_this.$confirm('确认提交排序结果？', '提示', {
						type: 'warning'
					}).then(() => {
						_this.loading = true;
						let para = {
							psid: _this.idArr,
							sequence: _this.sequenceArr
						};
						sortPromotionstep(para, _this)
						.then(res=>res.data)
						.then(data => {
							_this.loading = false;
							let { msg, code } = data;
							if (code !== "0") {
								_this.$message({
									message: msg,
									type: 'error'
								});
							} else {
								_this.$message({
									message: msg,
									type: 'success'
								});
								_this.getData();
							}
						});
					}).catch((error) => {
						if(error!=='cancel') {
							_this.$message({
								message: error.message? error.message : error,
								type: 'error'
							});
						}
					});
				} else {
					_this.$message({
						message: '排序数据出现问题，请刷新后重试！',
						type: 'error'
					});
				}
			},
			// table 行改变事件
			handleRowChange: function (row, event, column) {

			},
			// 上一步骤
			preStep: function () {
				this.$router.push({ path: '/promotion'});
			},
			// 下一步骤
			nextStep: function (index, row) {
				this.$router.push({ path: '/processLabel', query: {pid: this.pid, sid: row.id, st: row.steptitle, sp: row.steptype}});
			},
			// 导出Execl
			exportExcel: function () {
				let _this = this;
				// 执行操作
				_this.loading = true;
				_this.closeDialog();  // 关闭编辑对话框
				let para = {
					pid: _this.pid,
				};
				getPromotionstepList(para, _this)
				.then(res=>res.data)
				.then(data => {
					_this.loading = false;
					let { code, msg } = data;
					if (code !== "0") {
						_this.$message({
							message: msg,
							type: 'error'
						});
					} else {
						data = data.data.list;
						_this.doExport(data);
					}
				});
			},
			// 执行导出
			doExport: function (tabData) {
				let _this = this;
				// 导出数据
				if(!!tabData && tabData.length > 0) {
					let i = 1;
					tabData.forEach(item => {
						item.id = i;
						item.steptype = _this.formatStepType(item);
						item.stepamount = _this.formatStepAmount(item);
						i++;
					});
					// 获取文件名
					let fileName = _this.sysCnName + "_" + util.formatDate.format(new Date(), "yyyy-MM-dd HH:mm:ss") + "_" + _this.$root.$route.name;
					// 表头和对应字段
					let tHeader = [ '序号', '名称', '类型', '促销区间', '过期日', '备注' ];
					let filterVal = [ 'id', 'steptitle', 'steptype', 'stepamount', 'monthoverdate', 'remark' ];
					util.exportJsonToExcel(fileName, JSON.stringify(tabData), tHeader, filterVal);
				} else {
					this.$message({
						message: "没有需要导出的数据",
						type: 'warning'
					});
				}
			},
		},
		// 创建VUE实例后的钩子
		created: function () {
			// 登录ID
			this.loginId = util.userData.getLoginUserFieldValue(emEnum.loginUserFileds.uid, this);
			// 获取参数值
			let query = this.$route.query;
			this.pid = query.pid;
		},
		// 挂载到DOM后的钩子
		mounted: function () {
			let _this = this;
			_this.containerHeight = _this.$refs.promotionstepcontainer.offsetHeight;
			
			// 改变组件的值
			_this.$refs.LayerPath.changeVal(_this.layer - 1, _this.pid);
			// 获取数据
			_this.getData();
			
			// 阻止默认行为
			document.body.ondrop = function (event) {
				event.preventDefault();
				event.stopPropagation();
			};
			// 行拖拽
			_this.rowDrop()
			// 绑定窗口尺寸改变事件改变数据表的高度
			window.onresize = () => {
				return (() => {
					// 为了避免频繁触发resize函数导致页面卡顿，使用定时器
					if (!this.timer && !!_this.$refs.promotionstepcontainer) {
						this.timer = true
						_this.containerHeight = _this.$refs.promotionstepcontainer.offsetHeight;
						let that = this
						setTimeout(function () {
							that.timer = false
						}, 100);
					}
				})();
			}
		}
	};
</script>

<style lang="scss">
.promotionstep-container {
	height: calc(100vh - 120px);
}
</style>