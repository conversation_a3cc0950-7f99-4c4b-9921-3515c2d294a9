<template>
	<section class="processlabel-container common-container" ref="processlabelcontainer">
		<!--操作-->
		<section class="data-oper">
			<el-button type="primary" @click="preStep" class="hzpbtn-primary">返回到促销步骤</el-button>
			<el-button type="primary" @click="editData" class="hzpbtn-primary" v-loading="loading">添加</el-button>
			<el-button type="primary" @click="exportExcel" class="hzpbtn-primary">导出</el-button>
			<el-button type="primary" @click="submitSort" class="hzpbtn-primary" v-loading="loading" :disabled="submitSortDisabled">提交排序</el-button>
			<!--位置显示-->
			<LayerPath class="layerPath" @promotionStepChange='promotionStepChange' ref='LayerPath'></LayerPath>
		</section>
		<!--列表-->
		<el-table :data="datas" :stripe="true" :height="containerHeight - 55" highlight-current-row v-loading="loading" id="out-table" 
			class="data-content" border row-key="id" @row-click="handleRowChange">
			<el-table-column label="序号" align="center" type="index" width="60">
			</el-table-column>
			<!-- <el-table-column prop="code" label="编号" align="center" min-width="20%">
			</el-table-column> -->
			<el-table-column prop="labelname" label="名称" align="center" min-width="20%">
			</el-table-column>
			<el-table-column prop="steptitle" label="父级促销步骤" align="center" min-width="20%">
			</el-table-column>
			<!-- <el-table-column prop="startime" label="开始日" :formatter="formatStartime" align="center" min-width="20%">
			</el-table-column>
			<el-table-column prop="endtime" label="结束日" :formatter="formatEndtime" align="center" min-width="20%">
			</el-table-column> -->
			<el-table-column prop="remark" label="备注" align="center" min-width="20%">
			</el-table-column>
			<el-table-column label="操作" align="center" width="200">
				<template slot-scope="scope">
					<label class="hzpedit" @click.prevent="delData(scope.$index, scope.row)">删除</label>|
					<label class="hzpedit" @click.prevent="enableData(scope.$index, scope.row)">{{scope.row.status===0? '禁用' : '启用'}}</label>|
					<label class="hzpedit" @click.prevent="editData(scope.$index, scope.row)">编辑</label>
				</template>
			</el-table-column>
		</el-table>
	</section>
</template>
<script>
	import { getProcesslabelList } from '../../api/api';
	import { deleteProcesslabel, deleteProcesslabel_new } from '../../api/api';
	import { enableProcesslabel } from '../../api/api';
	import { sortProcesslabel } from '../../api/api';
	import util from '../../common/js/util';
	import emEnum from '../../common/js/emEnum';
	// 行列拖拽
	import Sortable from 'sortablejs';
	// 层级组件
	import LayerPath from '../LayerPath';

	export default {
		components: {
			LayerPath
		},
		data() {
			return {
				sysCnName: emEnum.cnName,  // 中文名称
				sysEnName: emEnum.enName,  // 英文名称

				layer: 3,  // 层级

				loginId: '',   // 登录用户

				pid: '',       // 促销活动id
				sid: '',       // 促销步骤id
				steptitle: '', // 步骤名称
				steptype: '',  // 促销类型

				submitSortDisabled: true,  // 提交排序不可用
				idArr: [],        // 编号
				sequenceArr: [],  // 序号

				loading: false,
				containerHeight: 0,
				datas: [],
			}
		},
		methods: {
			// 开始时间显示格式转换
			formatStartime: function (row, column) {
				if(!!row.startime) {
					return util.formatDate.format(new Date(row.startime.replace(/-/g, '/')), 'yyyy-MM-dd HH:mm:ss');
				} else return row.startime;
			},
			// 结束时间显示格式转换
			formatEndtime: function (row, column) {
				if(!!row.endtime) {
					return util.formatDate.format(new Date(row.endtime.replace(/-/g, '/')), 'yyyy-MM-dd HH:mm:ss');
				} else return row.endtime;
			},
			// 促销步骤改变
			promotionStepChange: function (pid, sid, st, sp) {
				// 回调
				this.$refs.LayerPath.callBack();
				this.pid = pid;
				this.sid = sid;
				this.steptitle = st;
				this.steptype = sp;
				this.getData();
			},
			// 获取列表
			getData: function () {
				let _this = this;
				// 执行操作
				_this.loading = true;
				let para = {
					sid: _this.sid,
				};
				getProcesslabelList(para, _this)
				.then(res=>res.data)
				.then(data => {
					_this.loading = false;
					let { code, msg } = data;
					if (code !== "0") {
						_this.$message({
							message: msg,
							type: 'error'
						});
					} else {
						data = data.data.list;
						// 改变组件的值
						this.$refs.LayerPath.changeData(this.layer, data);

						_this.datas = data;
						// 先清空，防止出错
						_this.idArr = [];
						_this.sequenceArr = [];
						_this.datas.forEach(item => {
							_this.sequenceArr.push(item.sequence);
						});
					}
				});
			},
			// 删除数据
			delData: function (index, row) {
				let _this = this;
				_this.$confirm('确认删除该促销标签？', '提示', {
					type: 'warning'
				}).then(() => {
					_this.loading = true;
					let para = {
						plid: row.id,
						pid: _this.pid,
						uid: _this.loginId
					};
					deleteProcesslabel(para, _this)
					.then(res=>res.data)
					.then(data => {
						_this.loading = false;
						let { msg, code } = data;
						if (code !== "0") {
							_this.$message({
								message: msg,
								type: 'error'
							});
						} else {
							_this.$message({
								message: msg,
								type: 'success'
							});
							_this.getData();
						}
					});
				}).catch((error) => {
					if(error!=='cancel') {
						_this.$message({
							message: error.message? error.message : error,
							type: 'error'
						});
					}
				});
			},
			// 修改数据状态（禁用、启用）
			enableData: function (index, row) {
				let _this = this;
				_this.$confirm('确认' + (row.status === 0? '禁用' : '启用') + '该促销标签？', '提示', {
					type: 'warning'
				}).then(() => {
					_this.loading = true;
					let para = {
						plid: row.id,
						status: row.status === 0? '1' : '0'
					};
					enableProcesslabel(para, _this)
					.then(res=>res.data)
					.then(data => {
						_this.loading = false;
						let { msg, code } = data;
						if (code !== "0") {
							_this.$message({
								message: msg,
								type: 'error'
							});
						} else {
							_this.$message({
								message: msg,
								type: 'success'
							});
							_this.getData();
						}
					});
				}).catch((error) => {
					if(error!=='cancel') {
						_this.$message({
							message: error.message? error.message : error,
							type: 'error'
						});
					}
				});
			},
			// 编辑数据
			editData: function (index, row) {
				// 新增或编辑
				if(!row) {
					let q = {
						pid: this.pid, 
						sid: this.sid, 
						st: this.steptitle, 
						sp: this.steptype, 
						plid: ''
					};
					this.$router.push({ path: '/processLabelInventory', query: q});
				} else {
					// 参数
					let q = {
						pid: this.pid, 
						sid: this.sid, 
						st: row.steptitle,
						sp: this.steptype,
						plid: row.id, 
						ln: row.labelname, 
						// c: row.code,
						s: row.startime,
						e: row.endtime,
						r: row.remark
					};
					this.$router.push({ path: '/processLabelInventory', query: q});
				}
			},
			// 行拖拽
			rowDrop: function () {
				const tbody = document.querySelector('.el-table__body-wrapper tbody');
				const _this = this;
				Sortable.create(tbody, {
					onEnd({ newIndex, oldIndex }) {
						if(_this.sequenceArr.length > 1) {  // 大于1行才有排序的必要
							const currRow = _this.datas.splice(oldIndex, 1)[0];
							_this.datas.splice(newIndex, 0, currRow);
							_this.submitSortDisabled = false;
							// 设置序号
							for(let i = 0; i < _this.datas.length; i++) {
								_this.datas[i].sequence = _this.sequenceArr[i];
							}
						}
					}
				});
			},
			// 提交排序
			submitSort: function () {
				let _this = this;
				// 编号
				_this.idArr = _this.datas.map(item => {
					return item.id;
				});
				// 提交数据
				if(!!_this.idArr && !!_this.sequenceArr && _this.idArr.length === _this.sequenceArr.length) {				
					_this.$confirm('确认提交排序结果？', '提示', {
						type: 'warning'
					}).then(() => {
						_this.loading = true;
						let para = {
							plid: _this.idArr,
							sequence: _this.sequenceArr
						};
						sortProcesslabel(para, _this)
						.then(res=>res.data)
						.then(data => {
							_this.loading = false;
							let { msg, code } = data;
							if (code !== "0") {
								_this.$message({
									message: msg,
									type: 'error'
								});
							} else {
								_this.$message({
									message: msg,
									type: 'success'
								});
								_this.getData();
							}
						});
					}).catch((error) => {
						if(error!=='cancel') {
							_this.$message({
								message: error.message? error.message : error,
								type: 'error'
							});
						}
					});
				} else {
					_this.$message({
						message: '排序数据出现问题，请刷新后重试！',
						type: 'error'
					});
				}
			},
			// table 行改变事件
			handleRowChange: function (row, event, column) {
				
			},
			// 上一步骤
			preStep: function () {
				this.$router.push({ path: '/promotionStep', query: {pid: this.pid}});
			},
			// 导出Execl
			exportExcel: function () {
				let _this = this;
				// 执行操作
				_this.loading = true;
				let para = {
					sid: _this.sid,
				};
				getProcesslabelList(para, _this)
				.then(res=>res.data)
				.then(data => {
					_this.loading = false;
					let { code, msg } = data;
					if (code !== "0") {
						_this.$message({
							message: msg,
							type: 'error'
						});
					} else {
						data = data.data.list;
						_this.doExport(data);
					}
				});
			},
			// 执行导出
			doExport: function (tabData) {
				let _this = this;
				// 导出数据
				if(!!tabData && tabData.length > 0) {
					let i = 1;
					tabData.forEach(item => {
						item.id = i;
						i++;
					});
					// 获取文件名
					let fileName = _this.sysCnName + "_" + util.formatDate.format(new Date(), "yyyy-MM-dd HH:mm:ss") + "_" + _this.$root.$route.name;
					// 表头和对应字段
					let tHeader = [ '序号', '名称', '父级促销步骤', '备注' ];
					let filterVal = [ 'id', 'labelname', 'steptitle', 'remark' ];
					util.exportJsonToExcel(fileName, JSON.stringify(tabData), tHeader, filterVal);
				} else {
					this.$message({
						message: "没有需要导出的数据",
						type: 'warning'
					});
				}
			},
		},
		// 创建VUE实例后的钩子
		created: function () {
			// 登录ID
			this.loginId = util.userData.getLoginUserFieldValue(emEnum.loginUserFileds.uid, this);
			// 获取参数值
			let query = this.$route.query;
			this.pid = query.pid;
			this.sid = query.sid;
			this.steptitle = query.st;
			this.steptype = query.sp;
		},
		// 挂载到DOM后的钩子
		mounted: function () {
			let _this = this;
			_this.containerHeight = _this.$refs.processlabelcontainer.offsetHeight;
			
			// 改变组件的值
			_this.$refs.LayerPath.changeVal(_this.layer - 1, _this.sid);
			_this.$refs.LayerPath.changeVal(_this.layer - 2, _this.pid);
			// 获取数据
			_this.getData();
			
			// 阻止默认行为
			document.body.ondrop = function (event) {
				event.preventDefault();
				event.stopPropagation();
			};
			// 行拖拽
			_this.rowDrop()
			// 绑定窗口尺寸改变事件改变数据表的高度
			window.onresize = () => {
				return (() => {
					// 为了避免频繁触发resize函数导致页面卡顿，使用定时器
					if (!this.timer && !!_this.$refs.processlabelcontainer) {
						this.timer = true
						_this.containerHeight = _this.$refs.processlabelcontainer.offsetHeight;
						let that = this
						setTimeout(function () {
							that.timer = false
						}, 100);
					}
				})();
			}
		}
	};
</script>

<style lang="scss">
.processlabel-container {
	height: calc(100vh - 120px);
}
</style>