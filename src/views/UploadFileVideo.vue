<template>
	<el-form :inline="true">
		<el-form-item>
			<video style="width: 100px; height: 100px" :src="fileUrl" v-if="!!fileUrl && fileUrl !== addImg">
				Your browser does not support the video tag.
			</video>
			<el-image style="width: 100px; height: 100px" :src="fileUrl" fit="cover" v-else></el-image>
		</el-form-item>
		<el-form-item>
			<a href="javascript:;" class="file" v-loading="loading">{{ btnText }}
    			<input id="file" type="file" @change='uploadFile($event)' accept="video/mp4, video/rm, video/rmvb, video/mov, video/m4v, video/avi, video/flv, video/wmv" />
			</a>
			<label style="display: block; margin-left: 50px;">最大20M</label>
		</el-form-item>
	</el-form>
</template>

<script>
import COS from 'cos-js-sdk-v5';

var Bucket ='hx-new-shop-1256701284';          // 腾讯云对象储存桶名
var Region = 'ap-shanghai';                    // 对象储存你所处的地区编号

var MaxMSize = 20;                             // 图片最大M数
var cos = new COS({
	SecretId: 'AKIDiDWaOw20A4i6PSqsTotiHchW5QWMjWBc',  // 密钥ID	
	SecretKey: 'KOFtISQ8crqdFckgNq43qPimwqXzTlsl',     // 密钥的钥匙
	// getAuthorization: function (options, callback) {
	// 	var authorization = COS.getAuthorization({
	// 		SecretId: 'AKxxxxxxxxxxxxs6',          // 密钥ID	
	// 		SecretKey: 'Uxxxxxxxxxxxxxxxxxxxymb',  // 密钥的钥匙
	// 		Method: options.Method,
	// 		Key: options.Key,
	// 		Query: options.Query,
	// 		Headers: options.Headers,
	// 		Expires: 60,
	// 	});
	// 	callback(authorization);
	// }
});

export default {
	props:["videoUrl"],  //props用来接收父组件传过来的数据
	data(){
		return{
			loading: false,

			addImg: require('@/assets/images/addImg.png'),   // 新增地址
			fileUrl: require('@/assets/images/addImg.png'),  // 对象地址
			btnText: '上传视频',  // 按钮文字
		}
	},
	methods: {
		// 上传文件
		uploadFile: function (e) {
			var file = e.target.files[0];
			if (!file) return;
			
			let _this = this;
			// 限制图片最大10M
			let isLimit = file.size / 1024 / 1024 > MaxMSize;
			if(isLimit) {
				this.$message({
					message: '视频过大，请选择大小在 ' + MaxMSize + 'M 内的视频！',
					type: 'error'
				});
				return;
			}
			// 分片上传文件
			cos.putObject({
				Bucket: Bucket,
				Region: Region,
				Key: file.name,
				Body: file,
				onProgress: function (progressData, callback) {
					// logger.log(JSON.stringify(progressData));
					// console.log(progressData);
					_this.loading = true;
					if(progressData.percent === 1) {
						_this.loading = false;
						// 获取上传成功后的Url地址，通过这个地址查看、下载上传的文件	
						cos.getObjectUrl({
							Bucket: Bucket,
							Region: Region,
							Key: file.name,
							Sign: false,
						}, function (err, data) {
							_this.fileUrl = data.Url;
							// 与父组件通信
							_this.$emit("getVideoUrl", _this.fileUrl);
						});
					}
				},
			}, function (err, data) {  
				// console.log(err, data);
				_this.loading = false;
			});
		},
		// 改变文件路径
		changeVideoUrl: function (url, txt) {
			if(!url) {
				this.fileUrl = this.addImg;
			} else {
				this.fileUrl = url;
				// console.log(this.fileUrl);
			}
			this.btnText = txt;
		}
	},
	// 创建VUE实例后的钩子
	created: function () {

	},
	// 挂载到DOM后的钩子
	mounted: function () {

	}
}
</script>

<style lang="scss">
// .file {
//     display: inline-block;
//     position: relative;
//     width: 110px;
//     height: 40px;
//     line-height: 40px;
//     color: #FFF;
//     font-size: 18px;
//     text-align: center;
// 	margin-top: 20px;
//     margin-left: 20px;
//     border: 1px solid #4D5AFF;
//     border-radius: 4px;
//     background: #4D5AFF;
//     text-indent: 0;
//     text-decoration: none;
//     overflow: hidden;
// }
// .file input {
//     position: absolute;
//     font-size: 100px;
//     right: 0;
//     top: 0;
//     opacity: 0;
// }
// .file:hover {
//     background: #66b1ff;
//     border-color: #66b1ff;
//     text-decoration: none;
// }
</style>