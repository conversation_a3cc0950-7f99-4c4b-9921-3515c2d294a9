<template>
	<el-row class="container">
		<el-col :span="24" class="header">
			<el-col :span="4" class="platinfo">
				<el-dropdown trigger="hover">
					<div class="platname">
						<el-image style="margin-top: 25px; width: 150px; height: 55px" :src="logoUrl" fit="fill"></el-image>
						<!-- <div class="cn-platname">{{sysCnName}}</div>
						<div class="en-platname">{{sysEnName}}</div> -->
						<!-- <img :src="logoUrl" /> -->
					</div>
					<el-dropdown-menu slot="dropdown">
						<el-dropdown-item @click.native="logout">退出登录</el-dropdown-item>
						<el-dropdown-item @click.native="changePwd">修改密码</el-dropdown-item>
					</el-dropdown-menu>
				</el-dropdown>
			</el-col>
			<el-col :span="20" align="right">
				<div class="sysinfo">
					<div>版本号：{{version}}</div>
					<div>{{baseURL}}</div>
				</div>
			</el-col>
		</el-col>
		<el-col :span="24" class="main" ref="main">
			<aside class="menu-expanded" @click.prevent="spreadOrCollapse($event)">
				<!--导航菜单-->
				<el-menu :default-active="$route.path" class="el-menu-vertical-demo" @open="handleopen" @close="handleclose" @select="handleselect" unique-opened router
					background-color="#414141" text-color="#97a0a8" active-text-color="#fff" :collapse="isCollapse">
					<template v-for="(item,index) in $router.options.routes">
						<el-submenu :index="index+''" v-if="!item.hidden && !item.leaf && item.children.length > 0 && menuArray.some(a => a.value === item.name)" :key="index">
							<template slot="title">
								<i :class="item.iconCls"></i>
								<span slot="title">{{item.name}}</span>
							</template>
							<template v-for="child in item.children">
								<el-menu-item :index="child.path" :key="child.path" v-if="menuArray.some(a => a.value === child.name)" class="submenu-content">
									<span slot="title">{{child.name}}</span>
								</el-menu-item>
							</template>
						</el-submenu>
						<el-menu-item :key="item.children[0].name" v-if="item.leaf && item.children.length > 0 && menuArray.some(a => a.value === item.children[0].name)" :index="item.children[0].path">
							<i :class="item.iconCls"></i>
							<span slot="title">{{item.children[0].name}}</span>
						</el-menu-item>
					</template>
				</el-menu>
			</aside>
			<section class="content-container" ref="container">
				<div class="grid-content bg-purple-light">
					<el-col :span="24" class="content-wrapper">
						<transition name="fade" mode="out-in">
							<router-view></router-view>
						</transition>
					</el-col>
				</div>
			</section>
		</el-col>
		<el-dialog title="感谢使用本系统" :visible.sync="dialogVisible" class="dialog-container" width="400px">
			<div class="logOutTip">期待您的再次使用，正在退出......</div>
        </el-dialog>
	</el-row>
</template>

<script>
  	import { requestLoginOut } from '../api/api';
	import emEnum from '../common/js/emEnum';
  	import util from '../common/js/util';

	export default {
		data() {
			return {
				sysCnName: emEnum.cnName,  // 中文名称
				sysEnName: emEnum.enName,  // 英文名称
				version: emEnum.version,   // 版本
				baseURL: emEnum.url.baseURL.replace('ybn.api.hyfun', '***.api.*****').replace('shhzp', '*****'),   // 服务地址

				logoUrl: require('@/assets/images/logo4.jpg'),  // logo地址

				userName: '',  // 登录用户名
				role: '',      // 登录用户权限

				rootMenuArray: emEnum.rootMenuArray,          // 超级管理员菜单
				adminMenuArray: emEnum.adminMenuArray,        // 区域（大区）管理员菜单
				receptionMenuArray: emEnum.receptionMenuArray,// 订单管理员菜单
				logisticsMenuArray: emEnum.logisticsMenuArray,// 物流管理员菜单
				unAddMenuArray: emEnum.unAddMenuArray,        // 不需要新增按钮的菜单
				menuArray: [],  // 最终菜单
				
				dialogVisible: false,
        		isCollapse: false,  // 是否折叠
			}
		},
		// 方法
		methods: {
			// 展开或折叠左侧导航栏
			spreadOrCollapse: function (e) {
				// 只有点击的是aside元素时才执行展开或折叠处理
				if(e.srcElement.tagName === 'UL') {
					this.isCollapse = !this.isCollapse;
				}
			},
			handleopen: function() {
				//console.log('handleopen');
			},
			handleclose: function() {
				//console.log('handleclose');
			},
			// 处理选择事件
			handleselect: function (key, keyPath) {
				if(key === '/logout') {
					this.logout();
				} else {
					// 清空编辑赋值
					util.editStore.ClearEditValue();
				}
			},
			// 退出登录
			logout: function () {
				let _this = this;
				_this.$confirm('确认退出？', '提示', {
					type: 'warning'
				}).then(() => {
					_this.dialogVisible = true;
					_this.logining = true;
					// 登出参数
					let params = {};
					requestLoginOut(params, _this)
					.then(res=>res.data)
					.then(data => {
						_this.logining = false;
						let { msg, code } = data;
						if (code !== "0") {
							_this.$message({
								message: msg,
								type: 'error'
							});
							_this.dialogVisible = false;
						} else {
							util.userData.reLogin(_this, false);
							_this.dialogVisible = false;
						}
					});
				}).catch((error) => {
					if(error!=='cancel') {
						_this.$message({
							message: error.message? error.message : error,
							type: 'error'
						});
					}
				});
			},
			// 修改密码
			changePwd: function () {
				let _this = this;
				_this.$confirm('修改密码后需重新登录，是否继续修改密码？', '提示', {
					type: 'warning'
				}).then(() => {
					// 编辑赋值
					this.$router.push({ path: '/changePwd' });
				}).catch((error) => {
					if(error!=='cancel') {
						_this.$message({
							message: error.message? error.message : error,
							type: 'error'
						});
					}
				});
			}
		},
		// 挂载到DOM后的钩子
		mounted() {
			// 获取用户名
			this.userName = util.userData.getLoginUserFieldValue(emEnum.loginUserFileds.username, this);
			// 根据权限显示不同的菜单项
			this.role = util.userData.getLoginUserFieldValue(emEnum.loginUserFileds.role, this);
			if(this.role === 1) this.menuArray = this.rootMenuArray;
			else if(this.role === 2) this.menuArray = this.adminMenuArray;
			else if(this.role === 3) this.menuArray = this.receptionMenuArray;
			else if(this.role === 4) this.menuArray = this.logisticsMenuArray;
		}
	}

</script>

<style scoped lang="scss">
	.container {
		position: absolute;
		top: 0px;
		bottom: 0px;
		width: 100%;
		.header {
			height: 80px;
			line-height: 80px;
			background: #484848;
			color:#fff;
			.platinfo {
				float: left;
				width: 230px;
				height: 80px;
				cursor: pointer;
				text-align: center;
				background: #414141;
				.platname {
					width: 100%;
					// height: 50px;
					// line-height: 65px;
					height: 80px;
    				line-height: 80px;
					color: #fff;
				}
				.cn-platname {
					width: 100%;
					height: 30px;
					font-size: 19px;
				}
				.en-platname {
					width: 100%;
					height: 30px;
					font-size: 10px;
				}
			}
			.sysinfo {
				float: right;
				width: 200px;
				height: 50px;
				line-height: 30px;
				color: #484848;
				text-align: left;
				padding-top: 20px;
			}
			.title {
				display: block;
				font-size: 17px;
				color: #fff;
				text-align: left;
				margin-left: 320px;
			}
			.editBtn {
				position: absolute;
				top: 13px;
				right: 80px;
				width: 120px;
				height: 36px;
				font-size: 15px;
				padding: 0;
				line-height: 36px;
			}
		}
		.main {
			display: flex;
			position: absolute;
			top: 80px;
			bottom: 0px;
			overflow: hidden;
			.menu-expanded{
				// flex: 0 0 280px;
				// position: relative;
				// margin-left: -50px;
				text-align: center;
				background: #414141;
				.el-menu{
					height: 100%;
					padding: 15px 0 0 0;
					border: none;
				}
				.submenu-content {
					text-align: left;
					padding: 0;
					padding-left: 145px !important;
				}
			}
			.content-container {
				flex:1;
				width: calc(100vw - 350px);
				padding: 20px;
				background: #EFF3FC;
			}
		}
		.logOutTip {
			width: 100%; 
			height: 100px; 
			line-height: 100px; 
			font-size: 18px; 
			text-align: center;
		}
	}
	.el-menu-vertical-demo:not(.el-menu--collapse) {
		width: 230px;
		min-height: 400px;
	}
</style>