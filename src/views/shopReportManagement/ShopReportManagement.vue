<template>
	<section class="shopReport-container common-container" ref="shopReportcontainer">
		<!--操作-->
		<section class="data-oper">
			<el-row>
				<el-col :span="3" align="right">
					<el-input clearable style="width: 98%;" placeholder="快速搜索" v-model="keywords" @change="getData">
						<el-button slot="append" icon="el-icon-search"></el-button>
					</el-input>
				</el-col>
				<el-col :span="21" align="right">
					<el-button type="primary" @click="gotoMapManagement" class="hzpbtn-primary">地图模式</el-button>
				</el-col>
			</el-row>
		</section>
		<!--列表-->
		<el-table :data="datas" :stripe="true" :height="containerHeight - 85" highlight-current-row v-loading="loading" id="out-table" class="data-content" border style="width: 100%">
			<af-table-column label="序号" align="center" type="index" width="60">
				<template slot-scope="scope">
    				<span>{{(pageNum - 1) * pageSize + scope.$index + 1}}</span>
				</template>
			</af-table-column>
			<af-table-column prop="clientcode" label="客户编号" align="center"></af-table-column>
			<af-table-column prop="clientname" label="客户名称" align="center"></af-table-column>
			<af-table-column prop="aAgencyName" label="培训公司" align="center"></af-table-column>
			<af-table-column prop="pAgencyName" label="销售公司" align="center"></af-table-column>
			<af-table-column prop="cAgencyName" label="代理1" align="center"></af-table-column>
			<af-table-column prop="tAgencyName" label="代理2" align="center"></af-table-column>
			<af-table-column prop="consignee" label="收货人" align="center"></af-table-column>
			<af-table-column prop="shopname" label="店铺名称" align="center"></af-table-column>
			<af-table-column prop="legalpersonname" label="法人名称" align="center"></af-table-column>
			<af-table-column prop="linkphone" label="联系电话" align="center"></af-table-column>
			<af-table-column prop="shopaddress" label="店铺地址" align="center">
				<template slot-scope="scope">
					<label>{{ scope.row.province+scope.row.city+scope.row.district+scope.row.shopaddress }}</label>
				</template>
			</af-table-column>
			<af-table-column prop="remark" label="备注" align="center"></af-table-column>
			<af-table-column prop="creat_time" label="创建时间" align="center"></af-table-column>
			<af-table-column prop="status" label="审核状态" align="center" width="120">
				<template slot-scope="scope">
					<label v-if="scope.row.status == 0" class="recommend btj">未审核</label>
					<label v-if="scope.row.status == 1" class="recommend tj">审核通过</label>
					<label v-if="scope.row.status == 2" class="recommend jj">审核拒绝</label>
				</template>
			</af-table-column>
			<af-table-column label="操作" align="center" width="310" fixed="right" :key="Math.random()">
				<template slot-scope="scope">
					<label class="hzpedit" v-if="scope.row.status === 0" @click.prevent="editData(scope.$index, scope.row)">审核</label>
					<span v-if="scope.row.status === 0">|</span>
					<label class="hzpedit" @click.prevent="lookPic(scope.$index, scope.row)">查看图片</label>
					<span>|</span>
					<label class="hzpedit" @click.prevent="lookMap(scope.$index, scope.row)">查看位置</label>
					<span>|</span>
					<el-dropdown trigger="hover" style="line-height: 0;">
						<label class="edit-content edit-more">更多操作</label>
						<el-dropdown-menu slot="dropdown" >
							<!-- <div class="hzpedit" style="padding: 8px; border-bottom: #f1f1f1 1px solid" v-if="scope.row.status === 0" @click.prevent="editData(scope.$index, scope.row)">审核</div> -->
							<div class="hzpedit" style="padding: 8px; border-bottom: #f1f1f1 1px solid" @click.prevent="adminEditData(scope.$index, scope.row)">编辑</div>
							<div class="hzpedit" style="padding: 8px; border-bottom: #f1f1f1 1px solid; color: red" @click.prevent="delData(scope.$index, scope.row)">删除</div>
						</el-dropdown-menu>
					</el-dropdown>
				</template>
			</af-table-column>
		</el-table>
		<!--分页-->
		<pagination :pageSize="pageSize" :pageNum="pageNum" :totalNum="totalNum" 
			@handlePageSizeChange="handlePageSizeChange" @handlePageCurrentChange="handlePageCurrentChange">
		</pagination>
		<!--查看图片-->
        <el-dialog :title="'查看图片'" :visible.sync="picDialogVisible" class="dialog-container" width="400px">
			<div style="display: flex; flex-direction: row;justify-content: space-between;padding:20px; flex-wrap: wrap;">
				<div style="display: flex;flex-direction: column; align-items: center;">
					<!-- <div class="flex-row" style="position: relative">
						<el-image
							v-if="srcList[3]"
							style="width: 100px; height: 100px;"
							:src="srcList[3]"
							:preview-src-list="srcList">
						</el-image>
						<el-image
							v-else
							:src="btnImageUrl"
							style="width: 100px; height: 100px;"
							@click="uploadShopImage(3)"
							>
						</el-image>
						<i @click="deleteImage(3)" class="el-icon-delete" style="color: white; position: absolute; right: 0; top: 0; font-size: 20px"></i>
					</div> -->
					<UploadFileBtn :imgUrl="srcList[3]" @getFileUrl="uploadShopImage($event, 3)" @deleteImage="deleteImage(3)" />
					<span style="margin-top: 10px;">法人身份证正面</span>
				</div>
				<div style="display: flex;flex-direction: column;align-items: center; ">
					<!-- <div class="flex-row" style="position: relative">
						<el-image
							v-if="srcList[4]"
							style="width: 100px; height: 100px;"
							:src="srcList[4]"
							:preview-src-list="srcList">
						</el-image>
						<el-image
							v-else
							:src="btnImageUrl"
							style="width: 100px; height: 100px;"
							@click="uploadShopImage(4)"
							>
						</el-image>
						<i @click="deleteImage(4)" class="el-icon-delete" style="color: white; position: absolute; right: 0; top: 0; font-size: 20px"></i>
					</div> -->
					<UploadFileBtn :imgUrl="srcList[4]" @getFileUrl="uploadShopImage($event, 4)" @deleteImage="deleteImage(4)" />
					<span style="margin-top: 10px;">法人身份证反面</span>
				</div>
				<div style="display: flex;flex-direction: column;align-items: center; ">
					<!-- <div class="flex-row" style="position: relative">
						<el-image
							v-if="srcList[2]"
							style="width: 100px; height: 100px;"
							:src="srcList[2]"
							:preview-src-list="srcList">
						</el-image>
						<el-image
							v-else
							:src="btnImageUrl"
							style="width: 100px; height: 100px;"
							@click="uploadShopImage(2)"
							>
						</el-image>
						<i @click="deleteImage(2)" class="el-icon-delete" style="color: white; position: absolute; right: 0; top: 0; font-size: 20px"></i>
					</div> -->
					<UploadFileBtn :imgUrl="srcList[2]" @getFileUrl="uploadShopImage($event, 2)" @deleteImage="deleteImage(2)" />
					<span style="margin-top: 10px;">营业执照</span>
				</div>
			
				<div style="display: flex;flex-direction: column; align-items: center; margin-top: 30px;">
					<!-- <div class="flex-row" style="position: relative">
						<el-image
							v-if="srcList[0]"
							style="width: 100px; height: 100px;"
							:src="srcList[0]"
							:preview-src-list="srcList">
						</el-image>
						<el-image
							v-else
							:src="btnImageUrl"
							style="width: 100px; height: 100px;"
							@click="uploadShopImage(0)"
							>
						</el-image>
						<i @click="deleteImage(0)" class="el-icon-delete" style="color: white; position: absolute; right: 0; top: 0; font-size: 20px"></i>
					</div> -->
					<UploadFileBtn :imgUrl="srcList[0]" @getFileUrl="uploadShopImage($event, 0)" @deleteImage="deleteImage(0)" />
					<span style="margin-top: 10px;">门头照</span>
				</div>
				<div style="display: flex;flex-direction: column; align-items: center; margin-top: 30px;">
					<!-- <div class="flex-row" style="position: relative">
						<el-image
							v-if="srcList[1]"
							style="width: 100px; height: 100px;"
							:src="srcList[1]"
							:preview-src-list="srcList">
						</el-image>
						<el-image
							v-else
							:src="btnImageUrl"
							style="width: 100px; height: 100px;"
							@click="uploadShopImage(1)"
							>
						</el-image>
						<i @click="deleteImage(1)" class="el-icon-delete" style="color: white; position: absolute; right: 0; top: 0; font-size: 20px"></i>
					</div> -->
					<UploadFileBtn :imgUrl="srcList[1]" @getFileUrl="uploadShopImage($event, 1)" @deleteImage="deleteImage(1)" />
					<span style="margin-top: 10px;">货架照</span>
				</div>
			</div>
			<div slot="footer" class="dialog-footer">
              	<el-button @click="picDialogVisible = false" class="hzpbtn-close">关 闭</el-button>
                <el-button type="primary" @click="handleSubmitPhoto" v-loading="loading" class="hzpbtn-primary">保 存</el-button>
            </div>
		</el-dialog>

		<!--查看位置-->
        <el-dialog :title="'查看位置'" :visible.sync="mapDialogVisible" class="dialog-container" width="800px">
			<div style="display: flex; flex-direction: row;justify-content: space-between;padding:20px; flex-wrap: wrap;">
				<get-location 
					@setLocation="setLocation" 
					ref="getlocation"></get-location>
			</div>
		</el-dialog>

		<!--编辑数据-->
        <el-dialog :title="dialogTitle + '数据'" :visible.sync="editDialogVisible" class="dialog-container" width="500px" height="500px" @opened="openDialog">
			<el-form :model="formData" :rules="rules" ref="formData" label-width="100px" class="form-data" size="mini">
				<el-form-item label="客户编码" prop="clientcode">
					<el-input disabled v-model="formData.clientcode" placeholder="请输入"></el-input>
				</el-form-item>
				<el-form-item label="客户姓名" prop="clientname">
					<el-input disabled v-model="formData.clientname" placeholder="请输入" id="focus-ele"></el-input>
				</el-form-item>
				<el-form-item label="培训公司" prop="aAgencyName">
					<!-- <el-select v-model="formData.aagencyId" placeholder="请选择">
						<el-option
							v-for="item in agencyDatas"
							:key="item.aagencyid"
							:label="item.aagencyname"
							:value="item.aagencyid">
						</el-option>
					</el-select> -->
					<el-input disabled v-model="formData.aAgencyName" placeholder="请输入" id="focus-ele"></el-input>
				</el-form-item>
				<el-form-item label="销售公司" prop="pAgencyName">
					<el-input v-model="formData.pAgencyName" placeholder="请输入"></el-input>
				</el-form-item>
				<el-form-item label="代 理 1" prop="cAgencyName">
					<el-input v-model="formData.cAgencyName" placeholder="请输入"></el-input>
				</el-form-item>
				<el-form-item label="代 理 2" prop="tAgencyName">
					<el-input v-model="formData.tAgencyName" placeholder="请输入"></el-input>
				</el-form-item>
				<el-form-item label="收货人" prop="consignee">
					<el-input v-model="formData.consignee" placeholder="请输入" id="focus-ele"></el-input>
				</el-form-item>
				<el-form-item label="店铺名称" prop="shopname">
					<el-input v-model="formData.shopname" placeholder="请输入" id="focus-ele"></el-input>
				</el-form-item>
				<el-form-item label="法人名称" prop="legalpersonname">
					<el-input v-model="formData.legalpersonname" placeholder="请输入"></el-input>
				</el-form-item>
				<el-form-item label="联系电话" prop="linkphone">
					<el-input v-model="formData.linkphone" placeholder="请输入"></el-input>
				</el-form-item>
				<el-form-item label="省市县(区)" prop="province">
					<region-picker :placeholder="{province: '选择省份', city: '选择市', district: '选择县（区）'}" 
						:province="formData.province" :city="formData.city" :district="formData.district" @onchange="regionChange">
					</region-picker>
				</el-form-item>
				<el-form-item label="详细地址" prop="shopaddress">
					<el-input v-model="formData.shopaddress" placeholder="请输入" type="textarea" :rows="4"></el-input>
				</el-form-item>
				<el-form-item label="备注" prop="remark">
					<el-input v-model="formData.remark" placeholder="请输入" type="textarea" :rows="4"></el-input>
				</el-form-item>
        	</el-form>
            <div slot="footer" class="dialog-footer">
              	<el-button @click="closeDialog" class="hzpbtn-close">关 闭</el-button>
                <el-button type="primary" @click="handleSubmit('formData')" v-loading="loading" class="hzpbtn-primary">保 存</el-button>
            </div>
        </el-dialog>
	</section>
</template>
<script>
	import { getAdminShopReportList } from '../../api/api';
	import { areabyroleList } from '../../api/api';
	import { updateAuditShopReport, updateShopReportAddress, adminUpdateShopReport } from '../../api/api';
	import { deleteAuditShopReport } from '../../api/api';
	import util from '../../common/js/util';
	import emEnum from '../../common/js/emEnum';
	// 分页组件
	import Pagination from '../Pagination';
	// 组件
    import GetLocation from '@/components/map/MyGetLocation';
	import UploadFileBtn from '@/views/UploadFileBtn.vue'
	export default {
		components: {
			Pagination,
			GetLocation,
			UploadFileBtn
		},
		data() {
			return {
				btnImageUrl: require("@/assets/images/btn_add_img.png"),
				sysCnName: emEnum.cnName,  // 中文名称
				sysEnName: emEnum.enName,  // 英文名称
				pageName: 'client',  // 页面名称

				role: '',      // 登录用户权限
				loginId: '',   // 登录用户
				loginAreaid: '',  // 登录区域

				loading: false,
				containerHeight: 0,
				datas: [],
				keywords: '',
				// 分页
				pageSize: 100,
				pageNum: 1,
				totalNum: 0,

				fixedPos: 'right',  // 停靠位置
				picDialogVisible: false,
				mapDialogVisible: false,
				srcList:[],
				shopMap: {},
				
				dialogTitle: '',   // 对话框标题 
				editDialogVisible: false,  // 编辑对话框
				formData: {
					rid: '',         // 选中ID
					shopname: '',
					legalpersonname: '', 
					linkphone: '',
					doorphoto: '',
					shelvephoto: '',
					buslicphoto: '',
					legalpersonfrontphoto: '',
					legalpersonbackphoto: '',
					province: '',  // 省份
					city: '',      // 市
					district: '',  // 县（区）
					consignee: '', // 收货人
					
					shopaddress: '',
					shoplat: '',
					shoplon: '',

					clientcode: '',        // 客户编码
					clientname: '',  // 客户名称

					aAgencyName: '', // 培训公司
					pAgencyName: '', // 销售公司
					cAgencyName: '', // 代理1
					tAgencyName: '', // 代理2
					remark: '', // 备注
				},
				rules: {
					shopname: [
						{ required: true, message: '请输入', trigger: 'blur' }
					],
					shopaddress: [
						{ required: true, message: '请输入', trigger: 'blur' }
					],
					legalpersonname: [
						{ required: true, message: '请输入', trigger: 'blur' }
					],
					linkphone: [
						{ required: true, message: '请输入', trigger: 'blur' }
					],
					province: [
						{ required: true, message: '请选择', trigger: 'blur' }
					],
					city: [
						{ required: true, message: '请选择', trigger: 'blur' }
					],
					district: [
						{ required: true, message: '请选择', trigger: 'blur' }
					],
					consignee: [
						{ required: true, message: '请输入', trigger: 'blur' }
					],
					pAgencyName: [
						{ required: true, message: '请输入', trigger: 'blur' }
					],
					cAgencyName: [
						{ required: true, message: '请输入', trigger: 'blur' }
					],
					tAgencyName: [
						{ required: true, message: '请输入', trigger: 'blur' }
					],
				},
				allAreaDatas: [],  // 带全部的区域
				areaDatas: [],     // 区域数据
				agencyDatas: [],   // 培训公司数据
			}
		},
		methods: {
			gotoMapManagement(){
				this.$router.push({ path: '/mapShopReport', query: {}});
			},
			lookPic(index, row){
				this.srcList = [row.doorphoto,row.shelvephoto,row.buslicphoto,row.legalpersonfrontphoto,row.legalpersonbackphoto]

				this.formData.rid = row.id;
				this.formData.shopname = row.shopname;
				this.formData.legalpersonname = row.legalpersonname;

				this.formData.aAgencyName = row.aAgencyName;
				this.formData.pAgencyName = row.pAgencyName;
				this.formData.cAgencyName = row.cAgencyName;
				this.formData.tAgencyName = row.tAgencyName;

				this.formData.linkphone = row.linkphone;
				this.formData.province = row.province;
				this.formData.city = row.city;
				this.formData.district = row.district;
				this.formData.shopaddress = row.shopaddress;
				this.formData.shoplat = row.shoplat;
				this.formData.shoplon = row.shoplon;
				this.formData.consignee = row.consignee;

				this.formData.clientcode = row.clientcode;
				this.formData.clientname = row.clientname;
				
				this.formData.doorphoto = row.doorphoto;
				this.formData.shelvephoto = row.shelvephoto;
				this.formData.buslicphoto = row.buslicphoto;
				this.formData.legalpersonfrontphoto = row.legalpersonfrontphoto;
				this.formData.legalpersonbackphoto = row.legalpersonbackphoto;
				this.formData.remark = row.remark;

				this.picDialogVisible = true;
			},
			lookMap(index, row){
				this.shopMap = row;
				this.mapDialogVisible = true;

				// 延时处理
				let _this = this;
				setTimeout(function () {
						if(!!_this.$refs.getlocation.setLocation) {
								_this.$refs.getlocation.setLocation(_this.shopMap.shopaddress, _this.shopMap.shoplon, _this.shopMap.shoplat);
						}
				}, 3000);
			},
			// 删除图片
			deleteImage(index) {
				this.$set(this.srcList, index, "");
			},
			uploadShopImage(event, index) {
				this.$set(this.srcList, index, event.highimage);
			},

			handleSubmitPhoto() {
				let parma = {
					rid: this.formData.rid
				};
				parma["doorphoto"] = this.srcList[0];
				parma["shelvephoto"] = this.srcList[1];
				parma["buslicphoto"] = this.srcList[2];
				parma["legalpersonfrontphoto"] = this.srcList[3];
				parma["legalpersonbackphoto"] = this.srcList[4];

				this.adminUpdateShopReportResp(parma);
			},

			setLocation(data) {
				// this.shopMap.shopaddress = data.address;
				// this.shopMap.shoplon = data.lng;
				// this.shopMap.shoplat = data.lat;
				
				let _this = this;
				_this.$confirm('确认更新店铺位置信息吗？', '提示', {
					distinguishCancelAndClose: true,
					type: 'warning'
				}).then(() => {
					_this.loading = true;
					let para = {
						rid: _this.shopMap.id,
						uid: _this.loginId,
						shopaddress: data.address,
						shoplat: data.lat,
						shoplon: data.lng
					};
					updateShopReportAddress(para, _this).then(res => {
						_this.loading = false;
						let { msg, code } = res.data;
						if (code !== "0") {
							_this.$message({
								message: msg,
								type: 'error'
							});
						} else {
							_this.mapDialogVisible = false;
							_this.$message({
								message: "修改成功",
								type: 'success'
							});
							_this.getData();
						}
					});
				})
			},

			// 获取区域
			getAllAreas: function () {
				let _this = this;
				_this.loading = true;
				let para = {};
				// getAreaConfig(para, _this)
				areabyroleList(para, _this)
				.then(res=>res.data)
				.then(data => {
					let { code, msg } = data;
					if (code !== "0") {
						_this.$message({
							message: msg,
							type: 'error'
						});
					} else {
						let areaData = data.data.list2;
						let agencyData = data.data.list;
						// 区域
						_this.areaDatas = areaData.filter(item => item.status === 0);
						// 处理带全部的区域
						if(!!_this.loginAreaid) {
							let areas = _this.loginAreaid.split(',');
							_this.areaDatas.forEach(item => {
								areas.forEach(a => {
									if((a + '') === (item.areaId + '')) {
										_this.allAreaDatas.push({id: item.areaId, areaname: item.areaName});
									}
								});
							});
							if(!!_this.allAreaDatas && _this.allAreaDatas.length > 0) {
								_this.sortAreaId = _this.allAreaDatas[0].id;
							}
						} else {
							_this.allAreaDatas.push({id: '', areaname: '全部'});
							_this.areaDatas.forEach(item => {
								_this.allAreaDatas.push({id: item.areaId, areaname: item.areaName});
							});
						}
						// 培训公司
						_this.agencyDatas = agencyData;
						_this.loading = false;
					}
				});
			},

			// 打开编辑对话框
			openDialog: function () {
				// 焦点放到获取焦点的输入框
				let ele = document.getElementById('focus-ele');
				if(!!ele) {
					ele.focus();
				}
			},
			// 关闭编辑对话框
			closeDialog: function () {
				this.editDialogVisible = false;
			},

			// 省市县（区）改变
			regionChange: function (val) {
				this.formData.province = val.province;
				this.formData.city = val.city;
				this.formData.district = val.district;
			},

			// 编辑数据
			adminEditData: function (index, row) {
				let formName = 'formData';
				// 打开对话框
				this.editDialogVisible = true;
				// 默认值
				if(!row) {
					this.dialogTitle = "添加";
					if(this.$refs[formName]) this.$refs[formName].resetFields();
					this.formData.rid = "";
					this.formData.shopname = "";
					this.formData.legalpersonname = "";

					this.formData.pAgencyName = "";
					this.formData.cAgencyName = "";
					this.formData.tAgencyName = "";
					this.formData.linkphone = "";
					this.formData.province = "";
					this.formData.city = "";
					this.formData.district = "";
					this.formData.shopaddress = "";
					this.formData.shoplat = "";
					this.formData.shoplon = "";
					this.formData.consignee = "";

					this.formData.clientcode = "";
					this.formData.clientname = "";
					
					this.formData.doorphoto = "";
					this.formData.shelvephoto = "";
					this.formData.buslicphoto = "";
					this.formData.legalpersonfrontphoto = "";
					this.formData.legalpersonbackphoto = "";
					this.formData.remark = "";
				} else {
					this.dialogTitle = "编辑";
					this.formData.rid = row.id;

					this.formData.shopname = row.shopname;
					this.formData.legalpersonname = row.legalpersonname;

					this.formData.aAgencyName = row.aAgencyName;
					this.formData.pAgencyName = row.pAgencyName;
					this.formData.cAgencyName = row.cAgencyName;
					this.formData.tAgencyName = row.tAgencyName;

					this.formData.linkphone = row.linkphone;
					this.formData.province = row.province;
					this.formData.city = row.city;
					this.formData.district = row.district;
					this.formData.shopaddress = row.shopaddress;
					this.formData.shoplat = row.shoplat;
					this.formData.shoplon = row.shoplon;
					this.formData.consignee = row.consignee;

					this.formData.clientcode = row.clientcode;
					this.formData.clientname = row.clientname;
					
					this.formData.doorphoto = row.doorphoto;
					this.formData.shelvephoto = row.shelvephoto;
					this.formData.buslicphoto = row.buslicphoto;
					this.formData.legalpersonfrontphoto = row.legalpersonfrontphoto;
					this.formData.legalpersonbackphoto = row.legalpersonbackphoto;
					this.formData.remark = row.remark;
					
					// 校验输入
					if(this.$refs[formName]) this.$refs[formName].validate();
				}
			},

			// 提交数据
			handleSubmit: function (formName) {
				this.$refs[formName].validate((valid) => {
          			if (valid) {
						let _this = this;
						_this.$confirm('确认' + _this.dialogTitle + '店铺信息？', '提示', {
							type: 'warning'
						}).then(() => {
							_this.loading = true;
							if(!_this.formData.rid) {  // 添加
								// let para = {
									
								// };
								// adminUpdateShopReport(para, _this)
								// .then(res=>res.data)
								// .then(data => {
								// 	_this.loading = false;
								// 	let { msg, code } = data;
								// 	if (code !== "0") {
								// 		_this.$message({
								// 			message: msg,
								// 			type: 'error'
								// 		});
								// 	} else {
								// 		_this.$message({
								// 			message: msg,
								// 			type: 'success'
								// 		});
								// 		_this.getData();
								// 	}
								// });
							} else {  // 编辑数据
								
								_this.adminUpdateShopReportResp(_this.formData)
							}
						}).catch((error) => {
							if(error!=='cancel') {
								_this.$message({
									message: error.message? error.message : error,
									type: 'error'
								});
							}
						});
					} else {
						return false;
					}
				});
			},

			adminUpdateShopReportResp(parma) {
				let _this = this;
				adminUpdateShopReport({
					...parma,
					uid: this.loginId,
				}, _this)
				.then(res=>res.data)
				.then(data => {
					_this.loading = false;
					let { msg, code } = data;
					if (code !== "0") {
						_this.$message({
							message: msg,
							type: 'error'
						});
					} else {
						_this.$message({
							message: msg,
							type: 'success'
						});
						_this.getData();
					}
				});
			},

			// 用户状态显示转换
			formatStatus: function (row, column) {
				return row.status === 0? "未审核" : (row.status === 1? "审核通过" : "驳回");
			},
			// 获取列表
			getData: function () {
				let _this = this;
				// 执行操作
				_this.loading = true;
				let para = {
          			uid:_this.loginId,
					page_num: _this.pageNum,
					page_size: _this.pageSize,
					keywords: _this.keywords,
				};

				getAdminShopReportList(para, _this)
				.then(res=>res.data)
				.then(data => {
					_this.loading = false;
					let { code, msg } = data;
					if (code !== "0") {
						_this.$message({
							message: msg,
							type: 'error'
						});
					} else {
						let { page_size, page_num, total } = data.data;
						data = data.data.data;
						_this.datas = data;
						// 分页
						_this.pageSize = parseInt(page_size);
						_this.pageNum = parseInt(page_num);
						_this.totalNum = parseInt(total);
					}
				});
			},
			//删除
			delData(index, row){
				let _this = this;
				_this.$confirm('确认删除该店铺？', '提示', {
					type: 'warning'
				}).then(() => {
					_this.loading = true;
					let para = {
						rid: row.id,
						uid: _this.loginId,
					};
					deleteAuditShopReport(para, _this)
					.then(res=>res.data)
					.then(data => {
						_this.loading = false;
						let { msg, code } = data;
						if (code !== "0") {
							_this.$message({
								message: msg,
								type: 'error'
							});
						} else {
							_this.$message({
								message: msg,
								type: 'success'
							});
							_this.getData();
						}
					});
				}).catch((error) => {
					if(error!=='cancel') {
						_this.$message({
							message: error.message? error.message : error,
							type: 'error'
						});
					}
				});
			},
			editData(index, row){
				let _this = this;
				_this.$confirm('审核通过？', '提示', {
					distinguishCancelAndClose: true,
					confirmButtonText: '通过',
					cancelButtonText: '不通过',
					type: 'warning'
				}).then(() => {
					_this.loading = true;
					let para = {
						rid: row.id,
						uid: _this.loginId,
						status: 1
					};
					updateAuditShopReport(para, _this)
					.then(res=>res.data)
					.then(data => {
						_this.loading = false;
						let { msg, code } = data;
						if (code !== "0") {
							_this.$message({
								message: msg,
								type: 'error'
							});
						} else {
							_this.$message({
								message: msg,
								type: 'success'
							});
							_this.getData();
						}
					});
				}).catch((action) => {
					// 判断是 cancel (自定义的取消) 还是 close （关闭弹窗）
					if (action === 'cancel') {
						_this.loading = true;
						let para = {
							rid: row.id,
							uid: _this.loginId,
							status: 2
						};
						updateAuditShopReport(para, _this)
						.then(res=>res.data)
						.then(data => {
							_this.loading = false;
							let { msg, code } = data;
							if (code !== "0") {
								_this.$message({
									message: msg,
									type: 'error'
								});
							} else {
								_this.$message({
									message: msg,
									type: 'success'
								});
								_this.getData();
							}
						});
					}
				});
			},
			// 分页事件
			handlePageSizeChange(val) {
				this.pageSize = val;
				this.getData();
			},
			handlePageCurrentChange(val) {
				this.pageNum = val;
				this.getData();
			}
		},
		// 创建VUE实例后的钩子
		created: function () {
			// 用户权限
			this.role = util.userData.getLoginUserFieldValue(emEnum.loginUserFileds.role, this);
			// 登录ID
			this.loginId = util.userData.getLoginUserFieldValue(emEnum.loginUserFileds.uid, this);
			// 用户区域ID
			this.loginAreaid = util.userData.getLoginUserFieldValue(emEnum.loginUserFileds.areaid, this);
		},
		// 挂载到DOM后的钩子
		mounted: function () {
			let _this = this;
			_this.containerHeight = _this.$refs.shopReportcontainer.offsetHeight;
			// 获取参数值
			let query = _this.$route.query;
      		this.getData();
			// this.getAllAreas();
			// 绑定窗口尺寸改变事件改变数据表的高度
			window.onresize = () => {
				return (() => {
					// 为了避免频繁触发resize函数导致页面卡顿，使用定时器
					if (!this.timer && !!_this.$refs.shopReportcontainer) {
						this.timer = true
						_this.containerHeight = _this.$refs.shopReportcontainer.offsetHeight;
						let that = this
						setTimeout(function () {
							that.timer = false
						}, 100);
					}
				})();
			}
		}
	};
</script>

<style lang="scss">
.shopReport-container {
	height: calc(100vh - 120px);
	.region-picker select {
		color: #606266;
		font-size: inherit;
		padding: 3px 0;
		border: 1px solid #DCDFE6;
		border-radius: 4px;
		box-sizing: border-box;
		outline: 0;
	}
	.region-picker select:focus {
		    border-color: #409EFF;
	}
	.recommend {
		font-size:14px;
		padding: 2px;
		border-radius: 5px;
		color: #fff;
		width:65px;
		text-align: center;
		display: inline-block;
    }
	.tj {
		background: #00B136;
	}
	.btj {
		background: #ff9800;
	}
	.jj{
		background: red;
	}
}
.edit-content {
	color: #3ca2ff;
	margin-left: 10px;
	margin-right: 10px;
	cursor: pointer;
}
.edit-more::after {
	// content: ' ﹀';
	font-size: 12px;
	height: 12px;
	line-height: 12px;
	vertical-align: baseline;
}
.edit-more:hover::after {
	// content: ' ︿';
	font-size: 12px;
	height: 12px;
	line-height: 12px;
	vertical-align: text-top;
}
</style>