<template>
	<section class="map-container common-container" ref="mapcontainer">
		<!--列表-->
    <div class="map-container-bg" v-loading="loading">
      <div class="map-container-left">
        <div class="left-bg">
          <div v-for="(item, index) of datas" :key="index" class="left-item" :class="{'left-item-select':item.isSelect}" @click="didSelectShop(item,index)">
            <div class="title">店铺名称：{{item.shopname}}</div>
            <div class="content">法人名称：{{item.legalpersonname}}</div>
            <div class="content">联系电话：{{item.linkphone}}</div>
            <div class="content">店铺地址：{{item.city}}{{item.district}}{{item.shopaddress}}</div>
          </div>
        </div>
      </div>
      <div class="map-container-right">
        <baidu-map
					class="baidu-map-view"
					:center="map_center"
					:zoom="map_zoom"
					@ready="map_handler"
					:double-click-zoom="false"
					:scroll-wheel-zoom="true">
          <bm-control :offset="centerOffset" style="width:300px">
            <bm-auto-complete v-model="mapSearchResultString" @confirm="mapSearchResult" :sugStyle="{zIndex: 9999}">
                <el-input v-model="mapSearchResultString" placeholder="搜索地点、路名" style="100%" clearable @clear="getData"></el-input>
            </bm-auto-complete>
          </bm-control>

          <!-- 锚点控制按钮 -->
          <bm-control :offset="{width: 10, height: 60}" style="width:120px">
            <el-button
              :type="isAddingAnchor ? 'danger' : 'primary'"
              size="small"
              @click="toggleAnchorMode"
              style="margin-bottom: 5px;">
              {{ isAddingAnchor ? '取消添加' : '添加锚点' }}
            </el-button>
            <el-button
              v-if="anchorPoint"
              type="success"
              size="small"
              @click="clearAnchor">
              清除锚点
            </el-button>
          </bm-control>
          <!-- 店铺标记 -->
          <template v-for="(marker,index) of markerPoints">
						<bm-marker :position="{lng: marker.shoplon, lat: marker.shoplat}" :key="index">
              <bm-label :content="marker.shopname+'('+marker.shopaddress+')'" :labelStyle="{color: 'black', fontSize : '14px', border :'0',backgroundColor:'#fff',transform: 'translateX(-50%)',position: 'absolute', cursor: 'pointer', padding: '2px 12px',borderRadius: '4px'}" :offset="{width: 6, height: -28}"/>
            </bm-marker>
					</template>

					<!-- 锚点标记 -->
					<bm-marker v-if="anchorPoint" :position="anchorPoint" :icon="{url: '', size: {width: 32, height: 32}}">
            <bm-label
              :content="'当前位置'"
              :labelStyle="{
                color: 'white',
                fontSize: '12px',
                border: '0',
                backgroundColor: '#ff4444',
                transform: 'translateX(-50%)',
                position: 'absolute',
                cursor: 'pointer',
                padding: '4px 8px',
                borderRadius: '4px',
                fontWeight: 'bold'
              }"
              :offset="{width: 0, height: -40}"/>
            <!-- 搜索按钮 -->
            <div style="position: absolute; top: 20px; left: -25px; z-index: 1000;">
              <el-button
                type="primary"
                size="mini"
                icon="el-icon-search"
                @click="searchNearbyShops"
                style="padding: 4px 8px;">
                搜索
              </el-button>
            </div>
          </bm-marker>
				</baidu-map>
      </div>
    </div>
	</section>
</template>
<script>
	import { getAdminShopReportList,selectDistanceReportedShop } from '../../api/api';
	import util from '../../common/js/util';
	import emEnum from '../../common/js/emEnum';
  import { BaiduMap, BmScale, BmGeolocation,BmNavigation,BmAutoComplete,BmControl,BmPolyline } from 'vue-baidu-map'

	export default {
		components: {
			BaiduMap,BmScale,BmPolyline,BmGeolocation,BmNavigation,BmAutoComplete,BmControl
		},
		data() {
			return {
        loginId: '',   // 登录用户

        BMap:null,
				map:null,
				map_zoom:12,
        centerOffset: {width: 0, height: 0},
				map_center:{
					lng: 0,
					lat: 0,
				},

				loading: false,
				containerHeight: 0,
				datas: [],

        pageNum: 1,
        pageSize: 1000,
        keywords:'',

        markerPoints:[],
        mapSearchResultString:'',
        currentCity: '宿迁市', // 默认城市

        // 锚点相关状态
        isAddingAnchor: false, // 是否处于添加锚点模式
        anchorPoint: null, // 当前锚点位置
        anchorMarker: null, // 锚点标记对象
			}
		},
		methods: {
      //地址检索
      mapSearchResult(){
        // 正地址解析
        let self = this
        let myGeo = new self.BMap.Geocoder()

        // 从搜索结果中提取城市信息
        let cityName = self.currentCity
        // 尝试从搜索字符串中提取城市信息
        if (self.mapSearchResultString) {
          // 常见城市后缀
          const citySuffixes = ['市', '县', '区']
          // 尝试匹配"XX市"、"XX县"、"XX区"格式
          for (let suffix of citySuffixes) {
            let index = self.mapSearchResultString.indexOf(suffix)
            if (index > 0) {
              // 找到最近的空格或字符串开始
              let startIndex = self.mapSearchResultString.lastIndexOf(' ', index)
              if (startIndex === -1) startIndex = 0
              else startIndex += 1 // 跳过空格

              // 提取城市名称
              let possibleCity = self.mapSearchResultString.substring(startIndex, index + 1)
              if (possibleCity.length >= 2 && possibleCity.length <= 10) {
                cityName = possibleCity
                break
              }
            }
          }
        }

        //正地址解析 - 获取经纬度
        myGeo.getPoint(self.mapSearchResultString, function(point){
            console.log(self.mapSearchResultString + "地图搜索结果：", point);
            if(point){
              // 使用逆地址解析进行精确定位
              let geoc = new self.BMap.Geocoder();
              geoc.getLocation(point, function(rs){
                if (rs) {
                  // 获取更精确的位置点
                  let precisePoint = rs.point;
                  console.log(self.mapSearchResultString + "地图搜索更精确结果：", precisePoint);
                  // 更新当前城市
                  if (rs.addressComponents && rs.addressComponents.city) {
                    self.currentCity = rs.addressComponents.city;
                  }
                  // 使用精确的经纬度
                  self.getData2(precisePoint);
                } else {
                  // 如果逆解析失败，使用原始点
                  self.getData2(point);
                }
              });
            } else {
               self.$message({
                 message: '未找到该地址的位置信息，请输入更详细的地址',
                 type: 'warning'
               });
            }
        }, cityName) // 使用提取的城市名，提高解析精度
      },
      // 获取列表
			getData2: function (point) {
				let _this = this;
				// 执行操作
				_this.loading = true;
				let para = {
          uid:_this.loginId,
					shoplat: point.lat,
          shoplon: point.lng,
				};
				selectDistanceReportedShop(para, _this)
				.then(res=>res.data)
				.then(data => {
					_this.loading = false;
					let { code, msg } = data;
					if (code !== "0") {
						_this.$message({
							message: msg,
							type: 'error'
						});
					} else {
						data = data.data.list;
            _this.markerPoints = [];
            _this.datas = data;
            let allIncludePoint = [];//一次性显示所有的点 集合
            if (data.length>0) {
              data.forEach(element => {
                element.isSelect = false;
                _this.markerPoints.push(element)
                allIncludePoint.push(new BMap.Point(parseFloat(element.shoplon),parseFloat(element.shoplat)))
              });
              _this.resizeMap(allIncludePoint)
            } else {
              _this.map_center.lat = point.lat
					    _this.map_center.lng = point.lng
            }
					}
				});
			},
      //* BMap是百度地图的对象，直接new出来跟原始的百度地图API一样使用，map是地图对象，可以调用对应的地图方法，比如添加marker */
			map_handler({ BMap, map }) {
					console.log("map_handler")
					let self = this
					self.BMap = BMap
					self.map = map
					self.map_zoom = 12
					self.map_center.lng = 121.506673
					self.map_center.lat = 31.243691
					self.centerOffset = new BMap.Size(10, 10)
			},
      didSelectShop(item,tag){
        let self = this
        self.map_zoom = 16
				self.map_center.lat = item.shoplat
				self.map_center.lng = item.shoplon
        for (let index = 0; index < self.markerPoints.length; index++) {
          const element = self.markerPoints[index];
          element.isSelect = false;
          if (tag == index) {
            element.isSelect = true;
          }
        }
        self.$forceUpdate();
      },
			// 获取列表
			getData: function () {
				let _this = this;
        _this.mapSearchResultString = '';
        if (!_this.keywords) {
          _this.datas = [];
          _this.markerPoints = [];
          _this.resizeMap([])
          return
        }
				// 执行操作
				_this.loading = true;
				let para = {
          uid:_this.loginId,
					page_num: _this.pageNum,
					page_size: _this.pageSize,
					keywords: _this.keywords,
				};
				getAdminShopReportList(para, _this)
				.then(res=>res.data)
				.then(data => {
					_this.loading = false;
					let { code, msg } = data;
					if (code !== "0") {
						_this.$message({
							message: msg,
							type: 'error'
						});
					} else {
						data = data.data.data;
						_this.datas = data;
            _this.markerPoints = [];
            let allIncludePoint = [];//一次性显示所有的点 集合
            data.forEach(element => {
              element.isSelect = false;
              _this.markerPoints.push(element)
              allIncludePoint.push(new BMap.Point(parseFloat(element.shoplon),parseFloat(element.shoplat)))
            });
            _this.resizeMap(allIncludePoint)
					}
				});
			},
      //重新渲染地图  一次性显示所有的点
      resizeMap(allPoint){
        this.$nextTick(()=>{
          this.map.setViewport(allPoint);
        })
      },

      // 切换锚点添加模式
      toggleAnchorMode() {
        this.isAddingAnchor = !this.isAddingAnchor;

        if (this.isAddingAnchor) {
          // 进入添加锚点模式
          this.$message({
            message: '请在地图上点击选择锚点位置',
            type: 'info'
          });
          // 添加地图点击事件监听
          this.map.addEventListener('click', this.onMapClick);
          // 改变鼠标样式
          this.map.setDefaultCursor('crosshair');
        } else {
          // 退出添加锚点模式
          this.map.removeEventListener('click', this.onMapClick);
          this.map.setDefaultCursor('default');
        }
      },

      // 地图点击事件处理
      onMapClick(e) {
        if (this.isAddingAnchor) {
          // 设置锚点位置
          this.anchorPoint = {
            lng: e.point.lng,
            lat: e.point.lat
          };

          // 退出添加锚点模式
          this.isAddingAnchor = false;
          this.map.removeEventListener('click', this.onMapClick);
          this.map.setDefaultCursor('default');

          this.$message({
            message: '锚点添加成功！点击搜索按钮查找附近店铺',
            type: 'success'
          });
        }
      },

      // 清除锚点
      clearAnchor() {
        this.anchorPoint = null;
        this.anchorMarker = null;
        this.$message({
          message: '锚点已清除',
          type: 'info'
        });
      },

      // 搜索锚点附近的店铺
      searchNearbyShops() {
        if (this.anchorPoint) {
          // 使用锚点坐标调用 getData2 方法
          let point = new this.BMap.Point(this.anchorPoint.lng, this.anchorPoint.lat);
          this.getData2(point);
        }
      },
		},
    // 创建VUE实例后的钩子
		created: function () {
			// 登录ID
			this.loginId = util.userData.getLoginUserFieldValue(emEnum.loginUserFileds.uid, this);
		},
		// 挂载到DOM后的钩子
		mounted: function () {
			let _this = this;
			_this.containerHeight = _this.$refs.mapcontainer.offsetHeight;
			// 获取用户
			// 绑定窗口尺寸改变事件改变数据表的高度
			window.onresize = () => {
				return (() => {
					// 为了避免频繁触发resize函数导致页面卡顿，使用定时器
					if (!this.timer && !!_this.$refs.mapcontainer) {
						this.timer = true
						_this.containerHeight = _this.$refs.mapcontainer.offsetHeight;
						let that = this
						setTimeout(function () {
							that.timer = false
						}, 100);
					}
				})();
			}
		},
		// 组件销毁前清理事件监听器
		beforeDestroy() {
			if (this.map) {
				this.map.removeEventListener('click', this.onMapClick);
			}
		}
	};
</script>

<style lang="scss">
.map-container {
	height: calc(100vh - 120px);
  .map-container-bg{
    display: flex;
    height: 100%;
    .map-container-left{
      width: 400px;
      height: 100%;
      border-radius: 10px;
      padding: 10px;
      box-sizing: border-box;
      background-color: white;
      .left-bg{
        height: calc(100% - 40px);
        overflow-y: auto;
        .left-item{
          margin-top: 16px;
          border-radius: 10px;
          padding: 10px;
          box-sizing: border-box;
          background-color: #EFF3FC;
          .title{
            font-family: PingFangSC, PingFang SC;
            font-weight: 600;
            font-size: 16px;
            color: #1F2329;
            line-height: 24px;
          }
          .content{
            font-family: PingFangSC, PingFang SC;
            font-weight: 400;
            font-size: 14px;
            color: #6D6D6D;
          }
        }
        .left-item-select{
          background: #FFFFFF;
          box-shadow: 0px 4px 16px 0px rgba(42,47,47,0.12);
          border-radius: 4px;
        }
      }
    }
    .map-container-right{
      margin-left: 10px;
      border-radius: 10px;
      flex: 1;
      height: 100%;
      .baidu-map-view{
        flex: 1;
        height: 100%;
      }
    }
  }
}
</style>