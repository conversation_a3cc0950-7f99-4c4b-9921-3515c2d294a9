<template>
	<section class="voucher-container common-container" ref="vouchercontainer">
		<!--操作-->
		<section class="data-oper">
			<el-button type="primary" @click="editData" class="hzpbtn-primary" v-loading="loading">添加</el-button>
		</section>
		<!--列表-->
		<el-table :data="datas" :stripe="true" :height="containerHeight - 55" highlight-current-row v-loading="loading" id="out-table" class="data-content" border>
			<el-table-column label="序号" align="center" type="index" width="60">
				<template slot-scope="scope">
    				<span>{{scope.$index + 1}}</span>
				</template>
			</el-table-column>
			<el-table-column prop="title" label="标题" align="center" min-width="40%">
			</el-table-column>
			<el-table-column prop="status" label="是否发布" :formatter="formatStatus" align="center" min-width="20%">
			</el-table-column>
			<el-table-column prop="remark" label="备注" align="center" min-width="40%">
			</el-table-column>
			<el-table-column label="操作" align="center" width="220">
				<template slot-scope="scope">
					<label class="hzpedit" @click.prevent="enableData(scope.$index, scope.row)">{{scope.row.status==='on'? '下架' : '发布'}}</label>|
					<label class="hzpedit" @click.prevent="delData(scope.$index, scope.row)">删除</label>|
					<label class="hzpedit" @click.prevent="editData(scope.$index, scope.row)">修改</label>
				</template>
			</el-table-column>
		</el-table>
		<!--编辑数据-->
        <el-dialog :title="dialogTitle + '数据'" :visible.sync="editDialogVisible" class="dialog-container" width="500px" @opened="openDialog">
			<el-form :model="formData" :rules="rules" ref="formData" label-width="100px" class="form-data" size="mini">
				<el-form-item label="标       题" prop="title">
					<el-input v-model="formData.title" placeholder="请输入" id="focus-ele"></el-input>
				</el-form-item>
				<!-- <el-form-item label="是否发布" prop="status">
					<el-select v-model="formData.status" placeholder="请选择">
						<el-option
							v-for="item in statusOptions"
							:key="item.key"
							:label="item.value"
							:value="item.key">
						</el-option>
					</el-select>
				</el-form-item> -->
				<el-form-item label="备       注" prop="">
					<el-input v-model="formData.remark" placeholder="请输入" type="textarea" :rows="4"></el-input>
				</el-form-item>
        	</el-form>
            <div slot="footer" class="dialog-footer">
              	<el-button @click="closeDialog" class="hzpbtn-close">关 闭</el-button>
                <el-button type="primary" @click="handleSubmit('formData')" v-loading="loading" class="hzpbtn-primary">{{dialogTitle === '添加'? '发 布' : '保 存'}}</el-button>
            </div>
        </el-dialog>
	</section>
</template>
<script>
	import { getVoucherActivityList } from '../../api/api';
	import { addVoucherActivity } from '../../api/api';
	import { updateVoucherActivity } from '../../api/api';
	import { deleteVoucherActivity } from '../../api/api';
  	import { resetUserPass } from '../../api/api';
	import emEnum from '../../common/js/emEnum';

	export default {
		data() {
			return {
				sysCnName: emEnum.cnName,  // 中文名称
				sysEnName: emEnum.enName,  // 英文名称

				loading: false,
				containerHeight: 0,
				datas: [],
				
				statusOptions: emEnum.voucherStatusOptions, // 是否发布可选项

				dialogTitle: '',   // 对话框标题 
				editDialogVisible: false,  // 编辑对话框
				formData: {
					id: '',       // 选中活动ID
					title: '',    // 标题
					status: '',   // 是否发布；on：发布；off：下架
					remark: ''    // 备注
				},
				rules: {
					title: [
						{ required: true, message: '请输入标题', trigger: 'blur' }
					],
					status: [
						{ required: true, message: '请选择是否发布', trigger: 'change' }
					]
				}
			}
		},
		methods: {
			// 是否发布显示转换
			formatStatus: function (row, column) {
				let status = emEnum.voucherStatusOptions.find(item=>{
					return item.key === row.status;
				});
				if(!status) return '';
				else return status.value;
			},
			// 获取列表
			getData: function () {
				let _this = this;
				// 执行操作
				_this.loading = true;
				_this.closeDialog();  // 关闭编辑对话框
				let para = {};
				getVoucherActivityList(para, _this)
				.then(res=>res.data)
				.then(data => {
					_this.loading = false;
					let { code, msg } = data;
					if (code !== "0") {
						_this.$message({
							message: msg,
							type: 'error'
						});
					} else {
						data = data.data;
						_this.datas = data;
					}
				});
			},
			// 删除数据
			delData: function (index, row) {
				let _this = this;
				_this.$confirm('确认删除该返单券活动？', '提示', {
					type: 'warning'
				}).then(() => {
					_this.loading = true;
					let para = {
						id: row.id
					};
					deleteVoucherActivity(para, _this)
					.then(res=>res.data)
					.then(data => {
						_this.loading = false;
						let { msg, code } = data;
						if (code !== "0") {
							_this.$message({
								message: msg,
								type: 'error'
							});
						} else {
							_this.$message({
								message: msg,
								type: 'success'
							});
							_this.getData();
						}
					});
				}).catch((error) => {
					if(error!=='cancel') {
						_this.$message({
							message: error.message? error.message : error,
							type: 'error'
						});
					}
				});
			},
			// 修改数据状态（发布、下架）
			enableData: function (index, row) {
				let _this = this;
				_this.$confirm('确认' + (row.status === 'on'? '下架' : '发布') + '该返单券活动？', '提示', {
					type: 'warning'
				}).then(() => {
					_this.loading = true;
					let para = {
						id: row.id,
						title: row.title,
						status: row.status === 'on'? 'off' : 'on',
						remark: row.remark,
					};
					updateVoucherActivity(para, _this)
					.then(res=>res.data)
					.then(data => {
						_this.loading = false;
						let { msg, code } = data;
						if (code !== "0") {
							_this.$message({
								message: msg,
								type: 'error'
							});
						} else {
							_this.$message({
								message: msg,
								type: 'success'
							});
							_this.getData();
						}
					});
				}).catch((error) => {
					if(error!=='cancel') {
						_this.$message({
							message: error.message? error.message : error,
							type: 'error'
						});
					}
				});
			},
			// 编辑数据
			editData: function (index, row) {
				let formName = 'formData';
				// 打开对话框
				this.editDialogVisible = true;
				// 默认值
				if(!row) {
					this.dialogTitle = "添加";
					if(this.$refs[formName]) this.$refs[formName].resetFields();
					this.formData.id = "";
					this.formData.title = "";
					this.formData.status = "on";
					this.formData.remark = "";
				} else {
					this.dialogTitle = "编辑";
					this.formData.id = row.id;
					this.formData.title = row.title;
					this.formData.status = row.status;
					this.formData.remark = row.remark;
					
					// 校验输入
					if(this.$refs[formName]) this.$refs[formName].validate();
				}
			},
			// 打开编辑对话框
			openDialog: function () {
				// 焦点放到获取焦点的输入框
				let ele = document.getElementById('focus-ele');
				if(!!ele) {
					ele.focus();
				}
			},
			// 关闭编辑对话框
			closeDialog: function () {
				this.editDialogVisible = false;
			},
			// 提交数据
			handleSubmit: function (formName) {
				this.$refs[formName].validate((valid) => {
          			if (valid) {
						let _this = this;
						_this.$confirm('确认' + _this.dialogTitle + '返单券活动？', '提示', {
							type: 'warning'
						}).then(() => {
							_this.loading = true;
							if(!_this.formData.id) {  // 添加
								let para = {
									title: _this.formData.title,
									status: _this.formData.status,
									remark: _this.formData.remark,
								};

								addVoucherActivity(para, _this)
								.then(res=>res.data)
								.then(data => {
									_this.loading = false;
									let { msg, code } = data;
									if (code !== "0") {
										_this.$message({
											message: msg,
											type: 'error'
										});
									} else {
										_this.$message({
											message: msg,
											type: 'success'
										});
										_this.getData();
									}
								});
							} else {  // 编辑数据
								let para = {
									id: _this.formData.id,
									title: _this.formData.title,
									status: _this.formData.status,
									remark: _this.formData.remark,
								};
								updateVoucherActivity(para, _this)
								.then(res=>res.data)
								.then(data => {
									_this.loading = false;
									let { msg, code } = data;
									if (code !== "0") {
										_this.$message({
											message: msg,
											type: 'error'
										});
									} else {
										_this.$message({
											message: msg,
											type: 'success'
										});
										_this.getData();
									}
								});
							}
						}).catch((error) => {
							if(error!=='cancel') {
								_this.$message({
									message: error.message? error.message : error,
									type: 'error'
								});
							}
						});
					} else {
						return false;
					}
				});
			}
		},
		// 创建VUE实例后的钩子
		created: function () {

		},
		// 挂载到DOM后的钩子
		mounted: function () {
			let _this = this;
			_this.containerHeight = _this.$refs.vouchercontainer.offsetHeight;
			// 获取用户
			_this.getData();
			// 绑定窗口尺寸改变事件改变数据表的高度
			window.onresize = () => {
				return (() => {
					// 为了避免频繁触发resize函数导致页面卡顿，使用定时器
					if (!this.timer && !!_this.$refs.vouchercontainer) {
						this.timer = true
						_this.containerHeight = _this.$refs.vouchercontainer.offsetHeight;
						let that = this
						setTimeout(function () {
							that.timer = false
						}, 100);
					}
				})();
			}
		}
	};
</script>

<style lang="scss">
.voucher-container {
	height: calc(100vh - 120px);
}
</style>