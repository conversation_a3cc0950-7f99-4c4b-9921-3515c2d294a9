<template>
	<section class="order-container common-container" ref="ordercontainer">
		<!--操作-->
		<section class="data-oper">
			<el-row>
				<el-col :span="8" align="left">
					<el-button type="primary" @click="editData" class="hzpbtn-primary" v-loading="loading">添加</el-button>
					<el-button type="primary" @click="editCol" class="hzpbtn-primary">设置显示列</el-button>
					<el-button type="primary" @click="exportExcel" class="hzpbtn-primary">导出</el-button>
				</el-col>
				<el-col :span="3" align="left">
					<el-select v-model="aagencyid" style="width: 97%;" clearable placeholder="按培训公司搜索" @change="searchList">
						<el-option
							v-for="item in agencyDatas"
							:key="item.aagencyid"
							:label="item.aagencyname"
							:value="item.aagencyid">
						</el-option>
					</el-select>
				</el-col>
				<el-col :span="2" align="center">
					<el-date-picker v-model="voucherStartime" type="date" clearable placeholder="日期起" :picker-options="startPickerOptions" @change="voucherTimeChange('start')"> </el-date-picker>
				</el-col>
				<el-col :span="2" align="center">
					<el-date-picker v-model="voucherEndtime" type="date" clearable placeholder="日期止" :picker-options="endPickerOptions" @change="voucherTimeChange('end')"> </el-date-picker>
				</el-col>
				<el-col :span="3" align="center">
					<el-select v-model="used" style="width: 97%;" clearable placeholder="按消费筛选" @change="searchList">
						<el-option
							v-for="item in usedOptions"
							:key="item.key"
							:label="item.value"
							:value="item.key">
						</el-option>
					</el-select>
				</el-col>
				<el-col :span="2" align="right">
					<el-select v-model="fuzzyField" style="width: 97%;" clearable placeholder="搜索字段" @change="searchColumn">
						<el-option
							v-for="item in fuzzyFieldOptions"
							:key="item.key"
							:label="item.value"
							:value="item.key">
						</el-option>
					</el-select>
				</el-col>
				<el-col :span="4" align="ceter">
					<el-input v-model="vague" style="width: 97%;" clearable placeholder="搜索内容" @change="searchList">
						<el-button slot="append" icon="el-icon-search"></el-button>
					</el-input>
				</el-col>
			</el-row>
		</section>
		<!--列表-->
		<el-table :data="datas" :stripe="true" :height="containerHeight - 85" highlight-current-row v-loading="loading" id="out-table" 
			class="data-content" border row-key="id">
			<af-table-column label="序号" align="center" width="60">
				<template slot-scope="scope">
    				<span>{{(pageNum - 1) * pageSize + scope.$index + 1}}</span>
				</template>
			</af-table-column>
			<af-table-column prop="id" label="返单券ID" align="center" v-if="colUnShowKeys.indexOf('id') === -1">
			</af-table-column>
			<af-table-column prop="amount" label="返单券金额" align="center" v-if="colUnShowKeys.indexOf('amount') === -1">
			</af-table-column>
			<af-table-column prop="gmtCreate1" label=" 赠送日期" :formatter="formatGmtCreate" align="center" v-if="colUnShowKeys.indexOf('gmtCreate') === -1">
			</af-table-column>
			<af-table-column prop="gmtCreate2" label=" 赠送时间" :formatter="formatGmtCreatetime" align="center" v-if="colUnShowKeys.indexOf('gmtCreatetime') === -1">
			</af-table-column>
			<af-table-column prop="ordercareter" label="客户编码" align="center" v-if="colUnShowKeys.indexOf('ordercareter') === -1">
			</af-table-column>
			<af-table-column prop="ordercaretername" label="客户姓名" align="center" v-if="colUnShowKeys.indexOf('ordercaretername') === -1">
			</af-table-column>
			<af-table-column prop="aagencyname" label="培训公司" align="center" v-if="colUnShowKeys.indexOf('aagencyname') === -1">
			</af-table-column>
			<af-table-column prop="pagencyname" label="销售公司" align="center" v-if="colUnShowKeys.indexOf('pagencyname') === -1">
			</af-table-column>
			<af-table-column prop="cagencyname" label="代理1" align="center" v-if="colUnShowKeys.indexOf('cagencyname') === -1">
			</af-table-column>
			<af-table-column prop="tagencyname" label="代理2" align="center" v-if="colUnShowKeys.indexOf('tagencyname') === -1">
			</af-table-column>
			<af-table-column prop="consignee" label="收货人姓名" align="center" v-if="colUnShowKeys.indexOf('consignee') === -1">
			</af-table-column>
			<af-table-column prop="consigneePhone" label="收货人电话" align="center" v-if="colUnShowKeys.indexOf('consigneePhone') === -1">
			</af-table-column>
			<af-table-column prop="province" label="省" align="center" v-if="colUnShowKeys.indexOf('province') === -1">
			</af-table-column>
			<af-table-column prop="city" label="市" align="center" v-if="colUnShowKeys.indexOf('city') === -1">
			</af-table-column>
			<af-table-column prop="district" label="县（区）" align="center" v-if="colUnShowKeys.indexOf('district') === -1">
			</af-table-column>
			<af-table-column prop="address" label="收货人地址" align="left" v-if="colUnShowKeys.indexOf('address') === -1">
			</af-table-column>
			<af-table-column prop="franchisee" label="加盟商姓名" align="center" v-if="colUnShowKeys.indexOf('franchisee') === -1">
			</af-table-column>
			<af-table-column prop="used" label="是否已消费" :formatter="formatUsed" align="center" v-if="colUnShowKeys.indexOf('used') === -1">
			</af-table-column>
			<af-table-column prop="usedOrderCode" label="消费订单号" align="center" v-if="colUnShowKeys.indexOf('usedOrderCode') === -1">
			</af-table-column>
			<af-table-column prop="remark" label="备注" align="left" v-if="colUnShowKeys.indexOf('remark') === -1">
			</af-table-column>
			<af-table-column label="操作" align="center" width="130" fixed="right" :key="Math.random()">
				<template slot-scope="scope">
					<span v-if="scope.row.used === 0">
						<label class="hzpedit" @click.prevent="editData(scope.$index, scope.row)">编辑</label>|
						<label class="hzpedit" @click.prevent="delData(scope.$index, scope.row)">删除</label>
					</span>
				</template>
			</af-table-column>
		</el-table>
		<!--分页-->
		<pagination :pageSize="pageSize" :pageNum="pageNum" :totalNum="totalNum" 
			@handlePageSizeChange="handlePageSizeChange" @handlePageCurrentChange="handlePageCurrentChange">
		</pagination>
		<!--编辑数据-->
        <el-dialog title="设置显示列" :visible.sync="colDialogVisible" class="dialog-container" width="600px" @close="closeColSet">
			<el-transfer v-model="colUnShowKeys" :data="colDatas" :titles="['显示列', '不显示列']" style="width: 500px; margin: 15px auto;"></el-transfer>
        </el-dialog>
		<!--编辑数据-->
        <el-dialog :title="dialogTitle + '数据'" :visible.sync="editDialogVisible" class="dialog-container" width="800px" @opened="openDialog">
			<el-form :model="formData" :rules="rules" ref="formData" label-width="100px" class="form-data" size="mini">
				<el-row class="order-edit-row">
					<el-col :span="10">
						<el-form-item label="返单券ID" prop="id">
							<el-input v-model="formData.id" placeholder="" :disabled="true"></el-input>
						</el-form-item>
					</el-col>
					<el-col :span="10">
						<el-form-item label="订单编号" prop="orderCode">
							<el-input v-model="formData.orderCode" placeholder="请输入" :disabled="unEditEnable" @change="searchData"></el-input>
						</el-form-item>
					</el-col>
					<el-col :span="3">
						<label class="hzpedit" @click.prevent="searchData">搜索</label>
					</el-col>
				</el-row>
				<el-row class="order-edit-row">
					<el-col :span="12">
						<el-form-item label="下单人姓名" prop="ordercaretername">
							<el-input v-model="formData.ordercaretername" placeholder="请输入" :disabled="unEditEnable"></el-input>
						</el-form-item>
					</el-col>
					<el-col :span="11">
						<el-form-item label="下单人编号" prop="ordercareter">
							<el-input v-model="formData.ordercareter" placeholder="请输入" :disabled="unEditEnable"></el-input>
						</el-form-item>
					</el-col>
				</el-row>
				<el-row class="order-edit-row">
					<el-col :span="12">
						<el-form-item label="培训公司" prop="aagencyid">
							<el-select v-model="formData.aagencyid" style="width: 97%;" clearable placeholder="请选择" @change="searchAgencyname($event)">
								<el-option
									v-for="item in agencyDatas"
									:key="item.aagencyid"
									:label="item.aagencyname"
									:value="item.aagencyid">
								</el-option>
							</el-select>
						</el-form-item>
					</el-col>
					<el-col :span="11">
						<el-form-item label="销售公司" prop="pagencyname">
							<el-input v-model="formData.pagencyname" placeholder="请输入"></el-input>
						</el-form-item>
					</el-col>
				</el-row>
				<el-row class="order-edit-row">
					<el-col :span="12">
						<el-form-item label="代理1" prop="cagencyname">
							<el-input v-model="formData.cagencyname" placeholder="请输入"></el-input>
						</el-form-item>
					</el-col>
					<el-col :span="11">
						<el-form-item label="代理2" prop="tagencyname">
							<el-input v-model="formData.tagencyname" placeholder="请输入"></el-input>
						</el-form-item>
					</el-col>
				</el-row>
				<el-row class="order-edit-row">
					<el-col :span="12">
						<el-form-item label="收货人姓名" prop="consignee">
							<el-input v-model="formData.consignee" placeholder="请输入"></el-input>
						</el-form-item>
					</el-col>
					<el-col :span="11">
						<el-form-item label="返单券金额" prop="amount" id="focus-ele">
							<el-input v-model="formData.amount" placeholder="请输入"></el-input>
						</el-form-item>
					</el-col>
				</el-row>
				<el-row class="order-edit-row">
					<el-col :span="12">
						<el-form-item label="加盟商姓名" prop="franchisee">
							<el-input v-model="formData.franchisee" placeholder="请输入"></el-input>
						</el-form-item>
					</el-col>
					<el-col :span="11">
						<el-form-item label="收货人电话" prop="consigneePhone">
							<el-input v-model="formData.consigneePhone" placeholder="请输入"></el-input>
						</el-form-item>
					</el-col>
				</el-row>
				<el-row class="order-edit-row">
					<el-col :span="24">
						<el-form-item label="省市县(区)" prop="province">
							<region-picker :placeholder="{province: '选择省份', city: '选择市', district: '选择县（区）'}" 
								:province="formData.region.province" :city="formData.region.city" :district="formData.region.district" @onchange="regionChange">
							</region-picker>
						</el-form-item>
					</el-col>
				</el-row>
				<el-row class="order-edit-row">
					<el-col :span="24">
						<el-form-item label="收货人地址" prop="address">
							<el-input v-model="formData.address" placeholder="请输入"></el-input>
						</el-form-item>
					</el-col>
				</el-row>
				<el-row class="order-edit-row">
					<el-col :span="24">
						<el-form-item label="备    注" prop="remark">
							<el-input v-model="formData.remark" placeholder="请输入"></el-input>
						</el-form-item>
					</el-col>
				</el-row>
        	</el-form>
            <div slot="footer" class="dialog-footer">
              	<el-button @click="closeDialog" class="hzpbtn-close">关 闭</el-button>
                <el-button type="primary" @click="handleSubmit('formData')" v-loading="loading" class="hzpbtn-primary">保 存</el-button>
            </div>
        </el-dialog>
	</section>
</template>
<script>
	import { getVoucherList } from '../../api/api';
	import { addVoucher } from '../../api/api';
	import { updateVoucher } from '../../api/api';
	import { deleteVoucher } from '../../api/api';
	import { areabyroleList } from '../../api/api';
	// 根据订单号获取订单信息
	import { getOrderList } from '../../api/api';
	// 根据促销id获取返单券金额
	import { getPromotionAmount } from '../../api/api';
	import util from '../../common/js/util';
	import emEnum from '../../common/js/emEnum';
	// 行列拖拽
	//import Sortable from 'sortablejs';
	// 上传文件组件
	import UploadFile from '../UploadFile';
	// 分页组件
	import Pagination from '../Pagination';

	export default {
		components: {
			UploadFile,
			Pagination
		},
		data() {
			// 校验省市县
			var validatorPcd = (rule, value, callback) => {
				if (!this.formData.region.province) {
					return callback(new Error('请选择省'));
				} else if (!this.formData.region.city) {
					return callback(new Error('请选择市'));
				} else if (!this.formData.region.district) {
					return callback(new Error('请选择县（区）'));
				}
				callback();
			};
			return {
				sysCnName: emEnum.cnName,  // 中文名称
				sysEnName: emEnum.enName,  // 英文名称
				pageName: 'voucher',  // 页面名称

				loading: false,
				containerHeight: 0,
				datas: [],
				// 模糊字段可选项
				fuzzyFieldOptions: emEnum.voucherFuzzyFieldOptions,
				// 是否已消费可选项
				usedOptions: emEnum.usedOptions,

				// 分页
				pageSize: 100,
				pageNum: 1,
				totalNum: 0,

				// 模糊字段
				fuzzyField: '',  // 模糊字段
				fuzzyFieldValue: '',  // 字段值

				// 筛选
				vague: '',      // 快速搜索
				used: '',       // 是否已消费
				aagencyid: '',  // 培训公司
				voucherStartime: '', // 日期起
				voucherEndtime: '',  // 日期止
				voucherStartStamp: '',  // 开始时间戳
				voucherEndStamp: '',    // 结束时间戳

				startPickerOptions: {
					disabledDate: time => {
						if(!!this.voucherEndtime) {
							return time.getTime() > new Date(this.voucherEndtime);
						}
						return false;
					}
				},  // 日期始时间范围
				endPickerOptions: {
					disabledDate: time => {
						if(!!this.voucherStartime) {
							return time.getTime() < new Date(this.voucherStartime);
						}
						return false;
					}
				},  // 日期止时间范围

				agencyDatas: [],   // 培训公司数据

				dialogTitle: '',     // 对话框标题
				editDialogVisible: false,  // 编辑对话框
				unEditEnable: true,    // 不可编辑
				formData: {
					id: '',             // 返单券ID

					orderCode: '',      // 返优惠券的订单编号
					ordercaretername: '',// 下单人姓名
					ordercareter: '',   // 下单人编号
					aagencyid: '',      // 培训公司id
					aagencyname: '',    // 培训公司姓名
					pagencyname: '',    // 销售公司姓名
					cagencyname: '',    // 代理1姓名
					tagencyname: '',    // 代理2姓名
					consignee: '',      // 收货人姓名

					amount: '',         // 返单券金额
					franchisee: '',     // 加盟商姓名
					consigneePhone: '', // 收货人电话
					region: {
						province: '',   // 省份
						city: '',       // 市
						district: '',   // 县（区）
					},  // 省市县（区）
					address: '',        // 收货人地址
					remark: '',         // 备注
				},
				rules: {
					ordercaretername: [
						{ required: true, message: '请输入下单人姓名', trigger: 'blur' }
					],
					ordercareter: [
						{ required: true, message: '请输入下单人编号', trigger: 'blur' }
					],
					consignee: [
						{ required: true, message: '请输入收货人姓名', trigger: 'blur' }
					],
					amount: [
						{ required: true, message: '请输入返单券金额', trigger: 'blur' }
					],
					franchisee: [
						{ required: true, message: '请输入加盟商姓名', trigger: 'blur' }
					],
					consigneePhone: [
						{ required: true, message: '请输入收货人电话', trigger: 'blur' }
					],
					province: [
						{ required: true, validator: validatorPcd, trigger: 'blur'}
					],
					address: [
						{ required: true, message: '请输入收货人地址', trigger: 'blur' }
					],
				},
				fixedPos: 'right',  // 停靠位置
				// 显示列选择相关
				colDialogVisible: false,  // 设置显示列对话框
				colDatas: [{key: 'id', label: '返单券ID'},
						{key: 'amount', label: '返单券金额'},
						{key: 'gmtCreate', label: '赠送日期'},
						{key: 'gmtCreatetime', label: '赠送时间'},
						{key: 'ordercareter', label: '客户编码'},
						{key: 'ordercaretername', label: '客户姓名'},
						{key: 'aagencyname', label: '培训公司'},
						{key: 'pagencyname', label: '销售公司'},
						{key: 'cagencyname', label: '代理1'},
						{key: 'tagencyname', label: '代理2'},
						{key: 'consignee', label: '收货人姓名'},
						{key: 'consigneePhone', label: '收货人电话'},
						{key: 'province', label: '省'},
						{key: 'city', label: '市'},
						{key: 'district', label: '县（区）'},
						{key: 'address', label: '收货人地址'},
						{key: 'franchisee', label: '加盟商姓名'},
						{key: 'used', label: '是否已消费'},
						{key: 'usedOrderCode', label: '消费订单号'},
						{key: 'remark', label: '备注'}],  // 列数据
				colUnShowKeys: [],  // 不显示列的键集合
			}
		},
		methods: {
			// 赠送时间显示格式转换
			formatGmtCreate: function (row, column) {
				return util.formatDate.format(new Date(row.gmtCreate), 'yyyy-MM-dd');
			},
			formatGmtCreatetime: function (row, column) {
			　　return util.formatDate.format(new Date(row.gmtCreate), 'HH:mm:ss');
			},
			// 是否已消费；0=未使用 1=已使用
			formatUsed: function (row, column) {
				let uOp = emEnum.usedOptions.find(item=>{
					return item.key === row.used;
				});
				if(!uOp) return '';
				else return uOp.value;
			},
			// 时间改变事件
			voucherTimeChange: function (control) {
				let _this = this;
				_this.voucherStartStamp = '';
				_this.voucherEndStamp = '';
				if(!!_this.voucherStartime && !!_this.voucherEndtime) {
					let s = util.formatDate.format(new Date(_this.voucherStartime), 'yyyy-MM-dd') + ' 00:00:00';
					let e = util.formatDate.format(new Date(_this.voucherEndtime), 'yyyy-MM-dd') + ' 23:59:59';
					_this.voucherStartStamp = new Date(s).getTime();
					_this.voucherEndStamp = new Date(e).getTime();
				}
				// 获取列表
				_this.searchList();
			},
			// 搜索字段
			searchColumn: function () {
				if(!!this.vague) {
					this.getData();	
				}
			},
			// 搜索数据
			searchList: function () {
				this.pageNum = 1;
				this.getData();
			},
			// 培训公司选择后获取培训公司名称
			searchAgencyname: function ($event) {
				let aagency = this.agencyDatas.find(item=>{
					return item.aagencyid === $event;
				});
				if(!!aagency && !!aagency.aagencyname) this.formData.aagencyname = aagency.aagencyname;
			},
			// 根据订单号搜索订单信息
			searchData: function () {
				let _this = this;
				// 清除数据
				_this.formData.ordercaretername = '';
				_this.formData.ordercareter = '';
				_this.formData.aagencyid = '';
				_this.formData.aagencyname = '';
				_this.formData.pagencyname = '';
				_this.formData.cagencyname = '';
				_this.formData.tagencyname = '';
				_this.formData.consignee = '';

				_this.formData.franchisee = '';
				_this.formData.consigneePhone = '';
				_this.formData.region.province = '';
				_this.formData.region.city = '';
				_this.formData.region.district = '';
				_this.formData.address = '';
				_this.formData.amount = '';
				// 根据是否输入订单号决定是否查询数据
				if(!!_this.formData.orderCode) {
					let para = {
						code: _this.formData.orderCode
					};
					// 获取数据
					getOrderList(para, _this)
					.then(res=>res.data)
					.then(data => {
						_this.loading = false;
						let { code, msg } = data;
						if (code !== "0") {
							_this.$message({
								message: msg,
								type: 'error'
							});
						} else {
							let orderDatas = data.data.list;
							if(!!orderDatas && orderDatas.length > 0) {
								let orderData = orderDatas[0];
								// 设置值
								_this.formData.ordercaretername = orderData.ordercaretername;
								_this.formData.ordercareter = orderData.ordercareter;
								_this.formData.aagencyid = !!orderData.aagencyid? (orderData.aagencyid + '') : '';
								_this.formData.aagencyname = orderData.aagencyname;
								_this.formData.pagencyname = orderData.pagencyname;
								_this.formData.cagencyname = orderData.cagencyname;
								_this.formData.tagencyname = orderData.tagencyname;
								_this.formData.consignee = orderData.consignee;

								_this.formData.franchisee = orderData.franchisee;
								_this.formData.consigneePhone = orderData.linkphone;
								_this.formData.region.province = orderData.province;
								_this.formData.region.city = orderData.city;
								_this.formData.region.district = orderData.district;
								_this.formData.address = orderData.address;
								// 获取优惠金额
								if(!!orderData.promotionid) {
									let paraAmount = {
										id: orderData.promotionid
									};
									// 获取数据
									getPromotionAmount(paraAmount, _this)
									.then(res=>res.data)
									.then(data => {
										_this.loading = false;
										let { code, msg } = data;
										if (code !== "0") {
											_this.$message({
												message: msg,
												type: 'error'
											});
										} else {
											if(!!data.data) {
												_this.formData.amount = data.data.voucheramount;
											}											
										}
									});
								}
							}
						}
					});
				} else {
					_this.$message({
						message: '请先输入订单编号再搜索！',
						type: 'warning'
					});
				}
			},
			// 获取列表
			getData: function () {
				let _this = this;
				// 执行操作
				_this.loading = true;
				_this.closeDialog();  // 关闭编辑对话框
				let para = {
					pageNum: _this.pageNum,
					pageSize: _this.pageSize
				};
				// 培训公司
				if(!!_this.aagencyid) para['aagencyid'] = _this.aagencyid;
				// 起止时间
				if(!!_this.voucherStartStamp && !!_this.voucherEndStamp) {
					para['startime'] = _this.voucherStartStamp;
					para['endtime'] = _this.voucherEndStamp;
				}
				// 是否已消费
				if(!!_this.used || _this.used === 0) para['used'] = _this.used;
				// 是否有模糊字段
				if(!!_this.fuzzyField && !!_this.vague) {
					if(_this.fuzzyField === 'id') {
						para['id'] = _this.vague;
					} else if(_this.fuzzyField === 'ordercaretername') {
						para['ordercaretername'] = _this.vague;
					} else if(_this.fuzzyField === 'franchisee') {
						para['franchisee'] = _this.vague;
					}
				}
				
				// 获取数据
				getVoucherList(para, _this)
				.then(res=>res.data)
				.then(data => {
					_this.loading = false;
					let { code, msg } = data;
					if (code !== "0") {
						_this.$message({
							message: msg,
							type: 'error'
						});
					} else {
						let { pageSize, pageNum, totalNum } = data.data;
						data = data.data.list;
						_this.datas = data;
						// 分页
						_this.pageSize = pageSize;
						_this.pageNum = pageNum;
						_this.totalNum = totalNum;
					}
				});
			},
			// 删除数据
			delData: function (index, row) {
				let _this = this;
				_this.$confirm('确认删除该返单券？', '提示', {
					type: 'warning'
				}).then(() => {
					_this.loading = true;
					let para = {
						id: row.id
					};
					deleteVoucher(para, _this)
					.then(res=>res.data)
					.then(data => {
						_this.loading = false;
						let { msg, code } = data;
						if (code !== "0") {
							_this.$message({
								message: msg,
								type: 'error'
							});
						} else {
							_this.$message({
								message: msg,
								type: 'success'
							});
							_this.getData();
						}
					});
				}).catch((error) => {
					if(error!=='cancel') {
						_this.$message({
							message: error.message? error.message : error,
							type: 'error'
						});
					}
				});
			},
			// 编辑数据
			editData: function (index, row) {
				let formName = 'formData';
				// 打开对话框
				this.editDialogVisible = true;
				if(!row) {
					this.dialogTitle = "添加";
					this.unEditEnable = false;
					if(this.$refs[formName]) this.$refs[formName].resetFields();
					// 默认值
					this.formData.id = "";
					this.formData.orderCode = "";
					this.formData.ordercaretername = "";
					this.formData.ordercareter = "";
					this.formData.aagencyid = "";
					this.formData.aagencyname = "";
					this.formData.pagencyname = "";
					this.formData.cagencyname = "";
					this.formData.tagencyname = "";
					this.formData.consignee = "";

					this.formData.amount = "";
					this.formData.franchisee = "";
					this.formData.consigneePhone = "";

					this.formData.region.province = "";
					this.formData.region.city = "";
					this.formData.region.district = "";
					this.formData.address = "";
					this.formData.remark = "";
				} else {
					this.dialogTitle = "编辑";
					this.unEditEnable = true;
					// 默认值
					this.formData.id = row.id;

					this.formData.orderCode = row.orderCode;
					this.formData.ordercaretername = row.ordercaretername;
					this.formData.ordercareter = row.ordercareter;
					this.formData.aagencyid =  !!row.aagencyid? (row.aagencyid + '') : '';
					this.formData.aagencyname = row.aagencyname;
					this.formData.pagencyname = row.pagencyname;
					this.formData.cagencyname = row.cagencyname;
					this.formData.tagencyname = row.tagencyname;
					this.formData.consignee = row.consignee;

					this.formData.amount = row.amount;
					this.formData.franchisee = row.franchisee;
					this.formData.consigneePhone = row.consigneePhone;

					this.formData.region.province = row.province;
					this.formData.region.city = row.city;
					this.formData.region.district = row.district;
					this.formData.address = row.address;
					this.formData.remark = row.remark;
					
					// 校验输入
					if(this.$refs[formName]) this.$refs[formName].validate();
				}
			},
			// 打开编辑对话框
			openDialog: function () {
				// 焦点放到获取焦点的输入框
				let ele = document.getElementById('focus-ele');
				if(!!ele) {
					ele.focus();
				}
			},
			// 关闭编辑对话框
			closeDialog: function () {
				this.editDialogVisible = false;
			},
			// 提交数据
			handleSubmit: function (formName) {
				this.$refs[formName].validate((valid) => {
          			if (valid) {
						let _this = this;
						_this.$confirm('确认' + _this.dialogTitle + '返单券？', '提示', {
							type: 'warning'
						}).then(() => {
							_this.loading = true;
							if(!_this.formData.id) {  // 添加
								let para = {
									orderCode: _this.formData.orderCode,
									ordercaretername: _this.formData.ordercaretername,
									ordercareter: _this.formData.ordercareter,
									aagencyid: _this.formData.aagencyid,
									aagencyname: _this.formData.aagencyname,
									pagencyname: _this.formData.pagencyname,
									cagencyname: _this.formData.cagencyname,
									tagencyname: _this.formData.tagencyname,
									consignee: _this.formData.consignee,

									amount: _this.formData.amount,
									franchisee: _this.formData.franchisee,
									consigneePhone: _this.formData.consigneePhone,

									province: _this.formData.region.province,
									city: _this.formData.region.city,
									district: _this.formData.region.district,
									address: _this.formData.address,
									remark: _this.formData.remark,
								};

								addVoucher(para, _this)
								.then(res=>res.data)
								.then(data => {
									_this.loading = false;
									let { msg, code } = data;
									if (code !== "0") {
										_this.$message({
											message: msg,
											type: 'error'
										});
									} else {
										_this.$message({
											message: msg,
											type: 'success'
										});
										_this.getData();
									}
								});
							} else {  // 编辑数据
								let para = {
									id: _this.formData.id,

									aagencyid: _this.formData.aagencyid,
									aagencyname: _this.formData.aagencyname,
									pagencyname: _this.formData.pagencyname,
									cagencyname: _this.formData.cagencyname,
									tagencyname: _this.formData.tagencyname,
									consignee: _this.formData.consignee,

									amount: _this.formData.amount,
									franchisee: _this.formData.franchisee,
									consigneePhone: _this.formData.consigneePhone,

									province: _this.formData.region.province,
									city: _this.formData.region.city,
									district: _this.formData.region.district,
									address: _this.formData.address,
									remark: _this.formData.remark,
								};

								updateVoucher(para, _this)
								.then(res=>res.data)
								.then(data => {
									_this.loading = false;
									let { msg, code } = data;
									if (code !== "0") {
										_this.$message({
											message: msg,
											type: 'error'
										});
									} else {
										_this.$message({
											message: msg,
											type: 'success'
										});
										_this.getData();
									}
								});
							}
						}).catch((error) => {
							if(error!=='cancel') {
								_this.$message({
									message: error.message? error.message : error,
									type: 'error'
								});
							}
						});
					} else {
						return false;
					}
				});
			},
			// 导出Execl
			exportExcel: function () {
				let _this = this;
				// 执行操作
				_this.loading = true;
				_this.closeDialog();  // 关闭编辑对话框
				let para = {
					pageNum: 1,
					pageSize: _this.totalNum
				};
				// 培训公司
				if(!!_this.aagencyid) para['aagencyid'] = _this.aagencyid;
				// 起止时间
				if(!!_this.voucherStartStamp && !!_this.voucherEndStamp) {
					para['startime'] = _this.voucherStartStamp;
					para['endtime'] = _this.voucherEndStamp;
				}
				// 是否已消费
				if(!!_this.used || _this.used === 0) para['used'] = _this.used;
				// 是否有模糊字段
				if(!!_this.fuzzyField && !!_this.vague) {
					if(_this.fuzzyField === 'id') {
						para['id'] = _this.vague;
					} else if(_this.fuzzyField === 'ordercaretername') {
						para['ordercaretername'] = _this.vague;
					} else if(_this.fuzzyField === 'franchisee') {
						para['franchisee'] = _this.vague;
					}
				}
				
				// 获取数据
				getVoucherList(para, _this)
				.then(res=>res.data)
				.then(data => {
					_this.loading = false;
					let { code, msg } = data;
					if (code !== "0") {
						_this.$message({
							message: msg,
							type: 'error'
						});
					} else {
						data = data.data.list;
						_this.doExport(data);
					}
				});
			},
			// 执行导出
			doExport: function (tabData) {
				let _this = this;
				// 导出数据
				if(!!tabData && tabData.length > 0) {
					let i = 1;
					tabData.forEach(item => {
						item.xid = i;
						item["gmtCreate1"] = _this.formatGmtCreate(item);
						item["gmtCreate2"] = _this.formatGmtCreatetime(item);
						item["isUsed"] = _this.formatUsed(item);
						i++;
					});
					// 表头和对应字段
					let tHeader = [ '序号', '返单券ID', '返单券金额', '赠送日期', '赠送时间', '客户编码', '客户姓名', '培训公司', '销售公司', '代理1', '代理2', 
						'收货人姓名', '收货人电话', '省', '市', '县（区）', '收货人地址', '加盟商姓名', '是否已消费', '消费订单号', '备注' ];
					let filterVal = [ 'xid', 'id', 'amount', 'gmtCreate1', 'gmtCreate2', 'ordercareter', 'ordercaretername', 'aagencyname', 'pagencyname', 'cagencyname', 'tagencyname', 
						'consignee', 'consigneePhone', 'province', 'city', 'district', 'address', 'franchisee', 'isUsed', 'usedOrderCode', 'remark' ];
					// 获取文件名
					let fileName = _this.sysCnName + "_" + util.formatDate.format(new Date(), "yyyy-MM-dd HH:mm:ss") + "_" + _this.$root.$route.name;
					util.exportJsonToExcel(fileName, JSON.stringify(tabData), tHeader, filterVal);
				} else {
					this.$message({
						message: "没有需要导出的数据",
						type: 'warning'
					});
				}
			},
			// 设置显示列
			editCol: function () {
				this.colDialogVisible = true;
			},
			// 关闭设置显示列
			closeColSet: function () {
				this.fixedPos = "none";
				// 存储不显示列
				util.editStore.SetUnShowCols(this.pageName, this.colUnShowKeys);
				this.fixedPos = "right";
			},
			// 省市县（区）改变
			regionChange: function (val) {
				if(this.formData.region.province !== val.province) {
					this.formData.region.province = val.province;
					this.formData.region.city = '';
					this.formData.region.district = '';
				} else if(this.formData.region.city !== val.city) {
					this.formData.region.city = val.city;
					this.formData.region.district = '';
				} else if(this.formData.region.district !== val.district) {
					this.formData.region.district = val.district;
				}
			},
			// 获取培训公司
			getAllAgencys: function () {
				let _this = this;
				_this.loading = true;
				let para = {};
				areabyroleList(para, _this)
				.then(res=>res.data)
				.then(data => {
					let { code, msg } = data;
					if (code !== "0") {
						_this.$message({
							message: msg,
							type: 'error'
						});
					} else {
						let agencyData = data.data.list;
						let areaData = data.data.list2;
						// 培训公司
						_this.agencyDatas = agencyData;
						_this.loading = false;
						// 获取数据
						_this.getData();
					}
				});
			},
			// 分页事件
			handlePageSizeChange(val) {
				this.pageSize = val;
				this.getData();
			},
			handlePageCurrentChange(val) {
				this.pageNum = val;
				this.getData();
			}
		},
		// 创建VUE实例后的钩子
		created: function () {
			// 获取不显示列
			let unShowCols = util.editStore.GetUnShowCols(this.pageName);
			if(!!unShowCols && unShowCols !== 'none') {
				this.colUnShowKeys = unShowCols;
			}
		},
		// 挂载到DOM后的钩子
		mounted: function () {
			let _this = this;
			_this.containerHeight = _this.$refs.ordercontainer.offsetHeight;
			// 获取培训公司
			_this.getAllAgencys();
			// 绑定窗口尺寸改变事件改变数据表的高度
			window.onresize = () => {
				return (() => {
					// 为了避免频繁触发resize函数导致页面卡顿，使用定时器
					if (!this.timer && !!_this.$refs.ordercontainer) {
						this.timer = true
						_this.containerHeight = _this.$refs.ordercontainer.offsetHeight;
						let that = this
						setTimeout(function () {
							that.timer = false
						}, 100);
					}
				})();
			}
		}
	};
</script>

<style lang="scss">
.order-container {
	height: calc(100vh - 120px);
}
</style>