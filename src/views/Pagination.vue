<template>
    <div class="block pagination">
		<label class="pagin-tip">显示第 {{ pTotal === 0? 0 : ((pNum-1) * pSize + 1) }} 条至第 {{ (pNum * pSize) > pTotal? pTotal : (pNum * pSize) }} 条记录</label>
        <el-pagination @size-change="handlePageSizeChange" @current-change="handlePageCurrentChange"
        :current-page="pNum" :page-sizes="[5, 10, 20, 50, 100, 150, 200]" :page-size="pSize" layout="total, prev, pager, next, sizes, jumper" :total="pTotal = totalNum">
        </el-pagination>
    </div>
</template>

<script>
	import util from '../common/js/util';
    export default {
		props:["pageNum", "pageSize", "totalNum"],   //props用来接收父组件传过来的数据
		data(){
            return {
				pNum: 1,    // 页码
				pSize: 10,  // 条数
				pTotal: 0,  // 总数
			}
        },
        methods: {
            handlePageSizeChange: function(val) {
				this.pSize = val;
                // 与父组件通信要加上这句话
                this.$emit("handlePageSizeChange", val);
            },
            handlePageCurrentChange: function(val) {
				this.pNum = val;
                // 与父组件通信要加上这句话
                this.$emit("handlePageCurrentChange", val);
            },
			// 重置页码
			resetFilter: function() {
				// 获取页码参数
				let Obj = util.editStore.GetEditObject();
				if(!!Obj && !!Obj.filter) {
					this.pSize = Obj.filter.pageSize;
					this.pNum = Obj.filter.pageNum;
					this.$emit("handlePageChange", this.pSize, this.pNum);
				}
			}
		},
		// 创建VUE实例后的钩子
		created: function() {
			this.pSize = this.$parent.pageSize;
			this.pNum = this.$parent.pageNum;
			// 重置
			this.resetFilter();
		},
		// 挂载到DOM后的钩子
		mounted() {
			
		}
    }
</script>

<style lang="scss">
.pagination {
	position: fixed;
    right: 50px;
    bottom: 0px;
    text-align: right;
	width: calc(100% - 400px);
	.pagin-tip {
		float: left;
    	height: 30px;
    	line-height: 30px;
	}
	.el-pagination__total {
		font-weight: bold;
	}
	.el-pagination .btn-prev, .el-pagination .btn-next {
		border: none;
		min-width: 25px;
	}
	.el-pagination .btn-next {
		margin-right: 10px;
	}
	.el-pager li {
		border: none;
	}
	.el-pager li:last-child {
		border-right: none;
	}
	.el-pager li, .el-pagination .el-select .el-input input {
		min-width: 25px;
		height: 25px;
		line-height: 25px;
		border-radius: 6px;
	}
	.el-pagination button, .el-pagination span {
		height: 25px;
		line-height: 25px;
	}
	.el-pagination__editor {
		width: 50px;
		padding: 2px 4px;
		text-align: left;
		border-radius: 5px;
	}
}
</style>