<template>
	<section class="logistics-container common-container" ref="logisticscontainer">
		<!--操作-->
		<section class="data-oper">
			<el-row>
				<el-col :span="10" align="left">
					<el-button type="primary" @click="editData" class="hzpbtn-primary" v-loading="loading" v-if="loginRole !== 2">添加</el-button>
					<!-- <el-button type="primary" @click="exportExcel" class="hzpbtn-primary" v-if="loginRole !== 2">导出</el-button> -->
					<el-button type="primary" @click="batchDel" class="hzpbtn-primary" v-if="loginRole !== 2">批量删除</el-button>
				</el-col>
				<el-col :span="4" align="left">
					<el-select style="width: 98%;" v-model="sortAreaId" placeholder="按区域搜索" @change="getData">
						<el-option
							v-for="item in allAreaDatas"
							:key="item.id"
							:label="item.areaname"
							:value="item.id">
						</el-option>
					</el-select>
				</el-col>
				<el-col :span="4" align="right">
					<el-input v-model="vague" style="width: 97%;" clearable placeholder="快速搜索" @change="getData" @keyup.native="getData">
						<el-button slot="append" icon="el-icon-search"></el-button>
					</el-input>
				</el-col>
				<!-- <el-col :span="3" align="right">
					<el-button type="primary" @click="downloadTemplate" class="hzpbtn-primary" v-loading="loading" v-if="loginRole !== 2">下载导入模板</el-button>
				</el-col> -->
				<!-- <el-col :span="3" align="center">
					<a href="javascript:;" class="file importFile" v-if="loginRole !== 2">导入Excel
						<input id="file" type="file" @change='importExcel($event)' accept=".xlsx, .xls, .csv" />
					</a>
				</el-col> -->
			</el-row>
		</section>
		<!--列表-->
		<el-table :data="datas" :stripe="true" :height="containerHeight - 85" highlight-current-row v-loading="loading" id="out-table" 
			class="data-content" border row-key="id" @selection-change="handleSelectionChange">
			<af-table-column type="selection" align="center">
    		</af-table-column>
			<af-table-column label="序号" align="center" type="index" width="80">
				<template slot-scope="scope">
    				<span>{{(pageNum - 1) * pageSize + scope.$index + 1}}</span>
				</template>
			</af-table-column>
			<af-table-column prop="logisticsid" label="快递单号" align="center">
			</af-table-column>
			<af-table-column prop="ordercode" label="订单编号" align="center">
			</af-table-column>
			<af-table-column prop="u8id" label="云仓id" align="center">
			</af-table-column>
			<af-table-column prop="logisticsname" label="物流公司" align="center">
			</af-table-column>
			<af-table-column prop="u8orderid" label="云仓系统发货单号" align="center">
			</af-table-column>
			<!-- <af-table-column prop="status" label="订单状态" :formatter="formatOrderStatus" align="center">
			</af-table-column> -->
			<af-table-column prop="creattime" label="创建时间" :formatter="formatCreattime" align="center">
			</af-table-column>
			<af-table-column prop="creatuser" label="创建人" align="center">
			</af-table-column>
			<af-table-column prop="updatetime" label="最后修改时间" :formatter="formatUpdatetime" align="center">
			</af-table-column>
			<af-table-column prop="updateuser" label="修改人" align="center">
			</af-table-column>
			<af-table-column label="操作" align="center" width="220" fixed="right">
				<template slot-scope="scope">
					<label class="hzpedit" @click.prevent="delData(scope.$index, scope.row)" v-if="loginRole !== 2">删除</label>
					<span v-if="loginRole !== 2">|</span><!-- enableData {{scope.row.status==="0"? '禁用' : '启用'}} -->
					<label class="hzpedit" @click.prevent="editData(scope.$index, scope.row)" v-if="loginRole !== 2">编辑</label>
					<span v-if="loginRole !== 2">|</span>
					<label class="hzpedit" @click.prevent="clickWayBill(scope.$index, scope.row)">查看物流</label>
				</template>
			</af-table-column>
		</el-table>
		<!--分页-->
		<pagination :pageSize="pageSize" :pageNum="pageNum" :totalNum="totalNum" 
			@handlePageSizeChange="handlePageSizeChange" @handlePageCurrentChange="handlePageCurrentChange">
		</pagination>
		<!--编辑数据-->
        <el-dialog :title="dialogTitle + '数据'" :visible.sync="editDialogVisible" class="dialog-container" width="500px" @opened="openDialog">
			<el-form :model="formData" :rules="rules" ref="formData" label-width="100px" class="form-data" size="mini">
				<el-form-item label="快递单号" prop="logisticsid">
					<el-input v-model="formData.logisticsid" placeholder="请输入" id="focus-ele"></el-input>
				</el-form-item>
				<el-form-item label="订单编号" prop="ordercode">
					<el-input v-model="formData.ordercode" placeholder="请输入"></el-input>
				</el-form-item>
				<el-form-item label="云仓id" prop="u8id">
					<el-input v-model="formData.u8id" placeholder="请输入"></el-input>
				</el-form-item>
				<el-form-item label="物流公司" prop="logisticsname">
					<el-input v-model="formData.logisticsname" placeholder="请输入"></el-input>
				</el-form-item>
				<el-form-item label="云仓系统发货单号" prop="u8orderid">
					<el-input v-model="formData.u8orderid" placeholder="请输入"></el-input>
				</el-form-item>
        	</el-form>
            <div slot="footer" class="dialog-footer">
              	<el-button @click="closeDialog" class="hzpbtn-close">关 闭</el-button>
                <el-button type="primary" @click="handleSubmit('formData')" v-loading="loading" class="hzpbtn-primary">保 存</el-button>
            </div>
        </el-dialog>
		<!--物流数据数据-->
        <el-dialog :title="'物流信息'" :visible.sync="logisticsDialogVisible" class="dialog-container" width="700px" height="600px">
			<section style="margin: 10px 0;">
				<label class="billNo">运单号：{{ logisticsBillNo }}</label>
				<label class="billState">{{ logisticsStatu }}</label>
				<section class="billContent">
					<ul class="bcul">
						<li v-for="(lo, index) in logisticsDatas" v-bind:key="lo.operatetime"  class="bcli" :style="index == 0? 'color: #E3A53F;' : ''">
							<el-row>
								<el-col :span="1">
									<span class="el-icon-location"></span>
								</el-col>
								<el-col :span="8">
									<span>{{ (!!lo.date? formatTitle(lo.date, lo.time) : lo.acceptTime) }}</span>
								</el-col>
								<el-col :span="24">
									<span v-html="lo.content" class="bccon">
										{{ lo.content }}
									</span>
								</el-col>
							</el-row>
						</li>
					</ul>
				</section>
				<section>
					<label class="billState consignmentState">发货单状态：{{ consignment.define14 }}</label>
				</section>
			</section>
        </el-dialog>
	</section>
</template>
<script>
	import { getLogisticsList } from '../../api/api';
	import { editLogistics } from '../../api/api';
	import { editOrderWaybillcode } from '../../api/api';
	import { enableLogistics } from '../../api/api';
	import { findwaybill } from '../../api/api';
	import { batchDelLogistics } from '../../api/api';
	import { u8orderidInter } from '../../api/api';
	import { queryWaybill } from '../../api/api';
	import { areabyroleList } from '../../api/api';
	import util from '../../common/js/util';
	import emEnum from '../../common/js/emEnum';
	// 上传文件组件
	import UploadFile from '../UploadFile';
	// 分页组件
	import Pagination from '../Pagination';

	export default {
		components: {
			UploadFile,
			Pagination
		},
		data() {
			return {
				sysCnName: emEnum.cnName,  // 中文名称
				sysEnName: emEnum.enName,  // 英文名称
				pageName: 'logistics',  // 页面名称

				uid: '',

				loginId: '',   // 登录用户
				loginRole: '', // 登录角色
				areaid: '',    // 登陆区域
				wxaccount: '', // 微信手机号

				loading: false,
				containerHeight: 0,
				datas: [],

				// 分页
				pageSize: 100,
				pageNum: 1,
				totalNum: 0,

				allAreaDatas: [],  // 带全部的区域
				// 筛选
				vague: '',       // 快速搜索
				sortAreaId: '',  // 搜索区域

				dialogTitle: '',     // 对话框标题
				editDialogVisible: false,  // 编辑对话框
				formData: {
					lid: '',           // 物流编号，系统物流表唯一id
					ordercode:'',      // 订单编号，订单code
					u8id: '',          // 销售单号(U8系统的ID)
					logisticsid: '',   // 快递单号
					logisticsname: '', // 物流公司
					u8orderid: '',     // U8系统发货单号
				},
				rules: {
					ordercode: [
						{ required: true, message: '请输入B2B订单号', trigger: 'blur' }
					],
					u8id: [
						{ required: true, message: '请输入云仓系统订单号', trigger: 'blur' }
					],
					logisticsid: [
						{ required: true, message: '请输入快递单号', trigger: 'blur' }
					],
					logisticsname: [
						{ required: true, message: '请输入物流公司', trigger: 'blur' }
					],
					u8orderid: [
						{ required: true, message: '请输入云仓系统发货单号', trigger: 'blur' }
					],
				},

				logisticsDialogVisible: false,  // 物流对话框
				logisticsDatas: [],  // 物流数据
				logisticsBillNo: '', // 物流运单号
				logisticsStatu: '',  // 物流状态

				consignment: {},     // 发货单

				multipleSelection: [],  // 选中数据
			}
		},
		methods: {
			// 订单状态显示转换：0 待支付，1 已付款，2 已发货，3 已完成
			formatOrderStatus: function (row, column) {
				if(row.orderstatus === '0' || !row.orderstatus) return "待支付";
				else if(row.orderstatus === '1') return "已付款";
				else if(row.orderstatus === '2') return "已发货";
				else if(row.orderstatus === '3') return "已完成";
				else return "";
			},
			// 创建时间显示格式转换
			formatCreattime: function (row, column) {
				if(isNaN(row.creattime) && !isNaN(Date.parse(row.creattime))){
			　　	return util.formatDate.format(new Date(row.creattime.replace(/-/g, '/')), 'yyyy-MM-dd HH:mm:ss');
				} else return row.creattime;
			},
			// 最后修改时间显示格式转换
			formatUpdatetime: function (row, column) {
				if(isNaN(row.updatetime) && !isNaN(Date.parse(row.updatetime))){
			　　	return util.formatDate.format(new Date(row.updatetime.replace(/-/g, '/')), 'yyyy-MM-dd HH:mm:ss');
				} else return row.updatetime;
			},
			// 格式化标题
			formatTitle: function (date, time) {
				return util.formatDate.format(new Date(date), "yyyy-MM-dd") + ' ' + time;
			},
			// 下载导入模板
			downloadTemplate: function () {
				window.open(emEnum.url.logisticsTemplateURL);
			},
			// 选中项改变事件
			handleSelectionChange: function (val) {
				let _this = this;
				_this.multipleSelection = [];
				if(!!val && val.length > 0) {
					val.forEach(a=>{
						_this.multipleSelection.push(a.id);
					});
				}
			},
			// 批量删除
			batchDel: function () {
				let _this = this;
				if(!!_this.multipleSelection && _this.multipleSelection.length > 0) {
					_this.$confirm('确认删除选中的物流？', '提示', {
						type: 'warning'
					}).then(() => {
						_this.loading = true;
						let para = {
							lid: _this.multipleSelection.join(',')
						};
						batchDelLogistics(para, _this)
						.then(res=>res.data)
						.then(data => {
							_this.loading = false;
							let { msg, code } = data;
							if (code !== "0") {
								_this.$message({
									message: msg,
									type: 'error'
								});
							} else {
								_this.$message({
									message: '物流删除成功',
									type: 'success'
								});
								_this.getData();
							}
						});
					}).catch((error) => {
						if(error!=='cancel') {
							_this.$message({
								message: error.message? error.message : error,
								type: 'error'
							});
						}
					});
				} else {
					_this.$message({
						message: '请先选择要删除的物流！',
						type: 'warning'
					});
				}
			},
			// 获取列表
			getData: function () {
				let _this = this;
				// 执行操作
				_this.loading = true;
				_this.closeDialog();  // 关闭编辑对话框
				let para = {
					status: 0,        // 用禁用代替删除，故此处只查状态为0的数据，即启用的数据
					pageNum: _this.pageNum,
					pageSize: _this.pageSize
				};
				// 快速搜索
				if(!!_this.vague) para['vague'] = _this.vague;
				// 是否限制区域
				if(!!_this.sortAreaId) para['areaid'] = _this.sortAreaId;
				else if(_this.loginRole !== 1) {
					para['areaid'] = _this.areaid;
				}
				// 是否传微信手机号
				if(_this.loginRole === 2) {  // 是区域管理员
					if(!_this.vague && !_this.sortAreaId)	{
						if(!!_this.wxaccount) para['wxaccount'] = _this.wxaccount;
					}
				} else {  // 不是区域管理员
					if(!!_this.wxaccount) para['wxaccount'] = _this.wxaccount;
				}
				// 获取数据
				getLogisticsList(para, _this)
				.then(res=>res.data)
				.then(data => {
					_this.loading = false;
					let { code, msg } = data;
					if (code !== "0") {
						_this.$message({
							message: msg,
							type: 'error'
						});
					} else {
						let { pageSize, pageNum, totalNum } = data.data;
						data = data.data.list;
						_this.datas = data;
						// 分页
						_this.pageSize = pageSize;
						_this.pageNum = pageNum;
						_this.totalNum = totalNum;
					}
				});
			},
			// 删除数据
			delData: function (index, row) {
				let _this = this;
				_this.$confirm('确认删除该物流？', '提示', {
					type: 'warning'
				}).then(() => {
					_this.loading = true;
					let para = {
						lid: row.id,
						status: '1'
					};
					enableLogistics(para, _this)
					.then(res=>res.data)
					.then(data => {
						_this.loading = false;
						let { msg, code } = data;
						if (code !== "0") {
							_this.$message({
								message: msg,
								type: 'error'
							});
						} else {
							_this.$message({
								message: '物流删除成功',
								type: 'success'
							});
							_this.getData();
						}
					});
				}).catch((error) => {
					if(error!=='cancel') {
						_this.$message({
							message: error.message? error.message : error,
							type: 'error'
						});
					}
				});
			},
			// 修改数据状态（禁用、启用）
			enableData: function (index, row) {
				let _this = this;
				_this.$confirm('确认' + (row.status === '0'? '禁用' : '启用') + '该物流？', '提示', {
					type: 'warning'
				}).then(() => {
					_this.loading = true;
					let para = {
						lid: row.id,
						status: row.status === "0"? '1' : '0'
					};
					enableLogistics(para, _this)
					.then(res=>res.data)
					.then(data => {
						_this.loading = false;
						let { msg, code } = data;
						if (code !== "0") {
							_this.$message({
								message: msg,
								type: 'error'
							});
						} else {
							_this.$message({
								message: msg,
								type: 'success'
							});
							_this.getData();
						}
					});
				}).catch((error) => {
					if(error!=='cancel') {
						_this.$message({
							message: error.message? error.message : error,
							type: 'error'
						});
					}
				});
			},
			// 重置输入
			resetForm: function (formName) {
				this.$refs[formName].resetFields();
			},
			// 编辑数据
			editData: function (index, row) {
				let formName = 'formData';
				// 打开对话框
				this.editDialogVisible = true;
				// 默认值
				if(!row) {
					this.dialogTitle = "添加";
					this.formData.lid = "";
					this.formData.logisticsid = "";
					this.formData.ordercode = "";
					this.formData.u8id = "";
					this.formData.logisticsname = "";
					this.formData.u8orderid = "";
				} else {
					this.dialogTitle = "编辑";
					this.formData.lid = row.id;
					this.formData.logisticsid = row.logisticsid;
					this.formData.ordercode = row.ordercode;
					this.formData.u8id = row.u8id;
					this.formData.logisticsname = row.logisticsname;
					this.formData.u8orderid = row.u8orderid;
					// 校验输入
					if(this.$refs[formName]) this.$refs[formName].validate();
				}
			},
			// 打开编辑对话框
			openDialog: function () {
				// 焦点放到获取焦点的输入框
				let ele = document.getElementById('focus-ele');
				if(!!ele) {
					ele.focus();
				}
			},
			// 关闭编辑对话框
			closeDialog: function () {
				this.editDialogVisible = false;
			},
			// 提交数据
			handleSubmit: function (formName) {
				this.$refs[formName].validate((valid) => {
          			if (valid) {
						let _this = this;
						_this.$confirm('确认' + _this.dialogTitle + '物流？', '提示', {
							type: 'warning'
						}).then(() => {
							_this.loading = true;
							let para = {
								logisticsid: _this.formData.logisticsid,
								ordercode: _this.formData.ordercode,
								u8id: _this.formData.u8id,
								logisticsname: _this.formData.logisticsname,
								u8orderid: _this.formData.u8orderid,
							};
							if(!_this.formData.lid) {  // 添加
								editLogistics(para, _this)
								.then(res=>res.data)
								.then(data => {
									_this.loading = false;
									let { msg, code } = data;
									if (code !== "0") {
										_this.$message({
											message: msg,
											type: 'error'
										});
									} else {
										_this.$message({
											message: msg,
											type: 'success'
										});
										_this.getData();
									}
								});
							} else {  // 编辑数据
								para["lid"] = _this.formData.lid;
								editLogistics(para, _this)
								.then(res=>res.data)
								.then(data => {
									_this.loading = false;
									let { msg, code } = data;
									if (code !== "0") {
										_this.$message({
											message: msg,
											type: 'error'
										});
									} else {
										_this.$message({
											message: msg,
											type: 'success'
										});
										_this.getData();
									}
								});
							}
						}).catch((error) => {
							if(error!=='cancel') {
								_this.$message({
									message: error.message? error.message : error,
									type: 'error'
								});
							}
						});
					} else {
						return false;
					}
				});
			},
			// 导入Excel
			importExcel: function (e) {
				let file = e.target.files[0];
				// 判断文件
				if (!file) {
					this.$message({
						message: '请选择导入的文件！',
						type: 'warning'
					});
					return;
				}
				// 判断格式
				const types = file.name.split('.')[1];
				const fileType = ['xlsx', 'xls', 'csv'].some(item => item === types);
				if (!fileType) {
					this.$message({
						message: '格式错误，请重新选择！',
						type: 'warning'
					});
					return;
				} else {
					let _this = this;
					// 处理数据
					let xlsxJson;
					// 提交数据
					let dataArray = [];
					let nullArray = [];  // 运单号为null的数据
					util.file2Xce(file).then(tabJson => {
						// 清空选中文件
						let f = document.getElementById('file');
						f.value = '';
						// 处理数据
						if (tabJson && tabJson.length > 0) {
							xlsxJson = tabJson;
							// xlsxJson就是解析出来的json数据,数据格式如下
							// [
							//   {
							//     sheetName: sheet1
							//     sheet: sheetData
							//   }
							// ]
							// 处理要提交的数据
							if(!!xlsxJson && xlsxJson.length > 0) {
								xlsxJson.forEach(sheetItem => {
									if(!!sheetItem && !!sheetItem.sheet && sheetItem.sheet.length > 0) {
										sheetItem.sheet.forEach(item => {
											let logisticsData = {};
											let i = 0;
											Object.keys(item).forEach(col => {
												if(i === 1) logisticsData['ordercode'] = item[col];
												else if(i === 2) logisticsData['u8id'] = item[col];
												else if(i === 3) logisticsData['logisticsid'] = item[col];
												else if(i === 4) logisticsData['logisticsname'] = item[col];
												else if(i === 5) logisticsData['u8orderid'] = item[col];
												// 序号加1
												i++;
											});
											// 处理数据
											if(!!logisticsData.ordercode) {
												if(!!logisticsData.logisticsid) {
													dataArray.push(logisticsData);
												} else {
													nullArray.push(logisticsData.ordercode);
												}
											}
										});
									}
								});
								if(!!dataArray && dataArray.length > 0) {
									// 执行操作
									_this.loading = true;
									let para = {
										data: dataArray
									};
									editOrderWaybillcode(para, _this)
									.then(res=>res.data)
									.then(data => {
										_this.loading = false;
										let { code, msg } = data;
										if (code !== "0") {
											_this.$message({
												message: msg,
												type: 'error'
											});
										} else {
											if(!!nullArray && nullArray.length > 0) {
												_this.$message({
													message: nullArray.join(',') + ' 等订单对应运单号为空，请确认后重新导入！',
													type: 'warning'
												});
											}
											_this.getData();
										}
									});
								} else {
									if(!!nullArray && nullArray.length > 0) {
										_this.$message({
											message: nullArray.join(',') + ' 等订单对应运单号为空，请确认后重新导入！',
											type: 'warning'
										});
									} else {
										_this.$message({
											message: '未读取到有效数据，请确认数据格式后重新导入！',
											type: 'warning'
										});
									}
								}
							}
						} else {
							_this.$message({
								message: '导入文件的数据为空！',
								type: 'warning'
							});
						}
					});
				}
			},
			// 导出Execl
			exportExcel: function () {
				let _this = this;
				// 执行操作
				_this.loading = true;
				_this.closeDialog();  // 关闭编辑对话框
				let para = {
					status: 0,        // 用禁用代替删除，故此处只查状态为0的数据，即启用的数据
					pageNum: 1,
					pageSize: _this.totalNum
				};
				// 快速搜索
				if(!!_this.vague) para['vague'] = _this.vague;
				// 是否限制区域
				if(!!_this.sortAreaId) para['areaid'] = _this.sortAreaId;
				else if(_this.loginRole !== 1){
					para['areaid'] = _this.areaid;
				}
				// 是否传微信手机号
				if(_this.loginRole === 2) {  // 是区域管理员
					if(!_this.vague && !_this.sortAreaId)	{
						if(!!_this.wxaccount) para['wxaccount'] = _this.wxaccount;
					}		
				} else {  // 不是区域管理员
					if(!!_this.wxaccount) para['wxaccount'] = _this.wxaccount;
				}
				// 获取数据
				getLogisticsList(para, _this)
				.then(res=>res.data)
				.then(data => {
					_this.loading = false;
					let { code, msg } = data;
					if (code !== "0") {
						_this.$message({
							message: msg,
							type: 'error'
						});
					} else {
						data = data.data.list;
						_this.doExport(data);
					}
				});
			},
			// 执行导出
			doExport: function (tabData) {
				let _this = this;
				// 导出数据
				if(!!tabData && tabData.length > 0) {
					let i = 1;
					tabData.forEach(item => {
						item.id = i;
						i++;
					});
					// 获取文件名
					let fileName = _this.sysCnName + "_" + util.formatDate.format(new Date(), "yyyy-MM-dd HH:mm:ss") + "_" + _this.$root.$route.name;
					// 表头和对应字段
					let tHeader = [ '序号', '云仓系统订单号', '快递单号', '物流公司', '云仓系统发货单号' ];
					let filterVal = ['id', 'u8id', 'logisticsid', 'logisticsname', 'u8orderid'];
					util.exportJsonToExcel(fileName, JSON.stringify(tabData), tHeader, filterVal);
				} else {
					this.$message({
						message: "没有需要导出的数据",
						type: 'warning'
					});
				}
			},
			// 订单单击事件
			clickWayBill: function (index, row) {
				let logisticsid = row.logisticsid;
				if(logisticsid.indexOf('SF') == 0) {
					this.checkSFWaybill(logisticsid);
				} else {
					this.checkLogistics(logisticsid);
				}
				this.checkU8idFun(row.u8orderid);
			},
			// 查看物流
			checkLogistics: function (wayid) {
				// 获取物流信息
				let _this = this;
				_this.logisticsDatas = [];
				_this.logisticsBillNo = '';
				_this.logisticsStatu = '';
				if(!!wayid) {
					// 执行操作
					_this.loading = true;
					let para = {
						wid: wayid,
					};
					findwaybill(para, _this)
					.then(res=>res.data)
					.then(data => {
						_this.loading = false;
						let { code, msg } = data;
						if (code !== "0") {
							_this.$message({
								message: msg,
								type: 'error'
							});
						} else {
							if(!!data.data && !!data.data.list && !!data.data.list.result) {
								let result = data.data.list.result;
								_this.logisticsBillNo = result.billno;
								_this.logisticsStatu = result.billnostate;
								if(!!result.tracks) {
									_this.logisticsDatas = data.data.list.result.tracks;
								}
							} else {
								_this.logisticsBillNo = wayid;
								_this.logisticsStatu = "暂无物流信息";
							}
							// 打开对话框
							_this.logisticsDialogVisible = true;
						}
					});
				}
			},
			// 查看顺丰物流信息
			checkSFWaybill: function (wayid) {
				// 获取物流信息
				let _this = this;
				_this.logisticsDatas = [];
				_this.logisticsBillNo = '';
				_this.logisticsStatu = '';
				if(!!wayid) {
					// 执行操作
					_this.loading = true;
					let para = {
						uid: _this.uid,
						wid: wayid
					};
					queryWaybill(para, _this)
					.then(res=>res.data)
					.then(data => {
						_this.loading = false;
						let { code, msg } = data;
						if (code !== "0") {
							_this.$message({
								message: msg,
								type: 'error'
							});
						} else {
							if(!!data.data) {
								let result = data.data;
								_this.logisticsBillNo = result.billno;
								_this.logisticsStatu = result.billnostate;
								if(!!result.tracks) {
									_this.logisticsDatas = result.tracks;
								}
							} else {
								_this.logisticsBillNo = wayid;
								_this.logisticsStatu = "暂无物流信息";
							}
							// 打开对话框
							_this.logisticsDialogVisible = true;
						}
					});
				}
			},
			// 查看U8id
			checkU8idFun: function (u8orderid) {
				// 获取物流信息
				let _this = this;
				if(!!u8orderid) {
					// 执行操作
					_this.loading = true;
					let para = {
						u8orderid: u8orderid
					};
					u8orderidInter(para, _this)
					.then(res=>res.data)
					.then(data => {
						_this.loading = false;
						let { code, msg } = data;
						if (code !== "0") {
							_this.$message({
								message: msg,
								type: 'error'
							});
						} else {
							_this.consignment =JSON.parse(data.data).consignment;
						}
					});
				}
			},
			// 关闭编辑对话框
			closeLogisticsDialog: function () {
				this.logisticsDialogVisible = false;
			},
			// 获取培训公司
			getAllAgencys: function () {
				let _this = this;
				_this.loading = true;
				let para = {};
				areabyroleList(para, _this)
				.then(res=>res.data)
				.then(data => {
					let { code, msg } = data;
					if (code !== "0") {
						_this.$message({
							message: msg,
							type: 'error'
						});
					} else {
						let agencyData = data.data.list;
						let areaData = data.data.list2;
						// 培训公司
						_this.agencyDatas = agencyData;
						// 区域
						let areaDatas = areaData.filter(item => item.status === 0);
						// 处理可选区域
						_this.allAreaDatas.push({id: '', areaname: '全部'});
						if(!!_this.areaid) {
							let areas = _this.areaid.split(',');
							areaDatas.forEach(item => {
								areas.forEach(a => {
									if((a + '') === (item.areaId + '')) {
										_this.allAreaDatas.push({id: item.areaId, areaname: item.areaName});
									}
								});
							});
							// if(!!_this.allAreaDatas && _this.allAreaDatas.length > 0) {
							// 	_this.sortAreaId = _this.allAreaDatas[0].id;
							// }
						} else {
							areaDatas.forEach(item => {
								// _this.allAreaDatas.push({id: item.id, areaname: item.areaname});
								_this.allAreaDatas.push({id: item.areaId, areaname: item.areaName});
							});
						}
						_this.loading = false;
						// 获取数据
						_this.getData();
					}
				});
			},
			// 分页事件
			handlePageSizeChange(val) {
				this.pageSize = val;
				this.getData();
			},
			handlePageCurrentChange(val) {
				this.pageNum = val;
				this.getData();
			}
		},
		// 创建VUE实例后的钩子
		created: function () {
			// 登录ID
			this.loginId = util.userData.getLoginUserFieldValue(emEnum.loginUserFileds.uid, this);
			// 根据权限做不同的操作
			let role = util.userData.getLoginUserFieldValue(emEnum.loginUserFileds.role, this);
			this.loginRole = role;
			// 用户区域ID
			if(role === 1 || role === 3 || role === 4) this.areaid = '';
			else this.areaid = util.userData.getLoginUserFieldValue(emEnum.loginUserFileds.areaid, this);
			// 微信手机号
			this.wxaccount = util.userData.getLoginUserFieldValue(emEnum.loginUserFileds.wxaccount, this);
		},
		// 挂载到DOM后的钩子
		mounted: function () {
			let _this = this;
			_this.containerHeight = _this.$refs.logisticscontainer.offsetHeight;
			// 获取用户id
			_this.uid = util.userData.getLoginUserFieldValue(emEnum.loginUserFileds.uid, this);
			// 获取培训公司
			_this.getAllAgencys();
			// 绑定窗口尺寸改变事件改变数据表的高度
			window.onresize = () => {
				return (() => {
					// 为了避免频繁触发resize函数导致页面卡顿，使用定时器
					if (!this.timer && !!_this.$refs.logisticscontainer) {
						this.timer = true
						_this.containerHeight = _this.$refs.logisticscontainer.offsetHeight;
						let that = this
						setTimeout(function () {
							that.timer = false
						}, 100);
					}
				})();
			}
		}
	};
</script>

<style lang="scss">
.logistics-container {
	height: calc(100vh - 120px);
	.wbsec {
		margin: 30px 20px;
		.wbtag {
			font-size: 18px;
			border-radius: 50px;
			margin: 10px;
			cursor: pointer;
		}
		.wbtag:hover {
			color: #409eff;
		}
	}
	.billNo {
		float: right;
		font-size: 18px;
		margin-right: 30px;
	}
	.billState {
    	display: inline-block;
		font-size: 18px;
    	color: #E3A53F;
		background: #FCE4BD;
    	padding: 5px 20px;
		margin-left: 30px;
		margin-bottom: 10px;
		border-radius: 30px;
	}
	.billContent {
		max-height: 500px;
		font-size: 15px;
		border-top: 1px solid #c0c4cc;
		overflow-y: auto;
		.bcul {
			list-style: none;
			.bcli {
				margin-bottom: 10px;
				.bccon {
					padding-left: 20px;
					border-left: 1px #c0c4cc dashed;
    				margin-left: 6px;
				}
			}
		}
	}
}
</style>