/*
 * @Author: huaze.fan
 * @LastEditTime: 2025-08-02 21:01:14
 * @LastEditors: <PERSON><PERSON> <EMAIL>
 * @Description: Good Good Study！Day Day Up
 */
import 'babel-polyfill';
// 框架库
import Vue from 'vue';
import Vuex from 'vuex';
import VueRouter from 'vue-router';
import VueClipboard from 'vue-clipboard2';
import RegionPicker from 'vue-region-picker';
import REGION_DATA from 'china-area-data';
// 调用Baidu地图组件
import BaiduMap from 'vue-baidu-map';
// 自实现js
import routes from './routes';
import store from './vuex/store';
import util from './common/js/util';
import emEnum from './common/js/emEnum';
// vue文件
import App from './App';
import Test from './Test';  // 测试用
// 布局库
import ElementUI from 'element-ui';
import 'element-ui/lib/theme-chalk/index.css';
// 需要按需引入，先引入vue并引入element-ui
import AFTableColumn from 'af-table-column'
// 样式
import './assets/theme/element-ui/lib/theme-default/index.css';
//import './assets/theme/theme-darkblue/index.css';
import 'font-awesome/css/font-awesome.min.css';
import './assets/iconfont/iconfont.css';
import './styles/css/loading.css';
import './styles/css/global.css';
// 注册组件
Vue.use(ElementUI)
Vue.use(Vuex)
Vue.use(VueRouter)
Vue.use(VueClipboard)
Vue.use(RegionPicker, {
    region: REGION_DATA,
    vueVersion: 2
})
Vue.use(BaiduMap, {
    // ak 是在百度地图开发者平台申请的密钥 详见 http://lbsyun.baidu.com/apiconsole/key */
    ak: 'OGCiU48G6hQL4FrAGvSTyFoi0DtGnCKW',
    type: 'WebGL'
});
Vue.use(AFTableColumn)

// 解决 NavigationDuplicated 报错
const originalPush = VueRouter.prototype.push
VueRouter.prototype.push = function push(location) {
    return originalPush.call(this, location).catch(err => err);
};
// 加载路由
const router = new VueRouter({
    /*存在三种模式：
  　* Hash: 使用URL的hash值来作为路由。支持所有浏览器。
  　* History: 依赖 HTML5 History API 和服务器配置。参考官网中HTML5 History模式
  　* Abstract： 支持所有javascript运行模式。如果发现没有浏览器的API，路由会自动强制进入这个模式。
    * 
    * 默认使用的是hash模式（前端路由，单页应用标配），URL中带有#号。修改成history模式，URL中的#号就被去除了。
    * 
    * 通过history api，丢掉了丑陋的#，但是它也有个毛病：
  　* 不怕前进，不怕后退，就怕刷新，f5，（如果后端没有准备的话），因为刷新是实实在在地去请求服务器的。
  　* 在hash模式下，前端路由修改的是#中的信息，而浏览器请求时是不带它的，所以没有问题。但是在history下，可以自由的修改path，当刷新时，如果服务器中没有相应的响应或者资源，会刷出一个404。
    */
    mode: 'history',
    routes
})
// 每次跳转之前执行
router.beforeEach((to, from, next) => {
    // 取消未完成的请求
    if (store.state.httpRequestList.length > 0) {
        store.state.httpRequestList.forEach((item) => {
            item();
        });
        store.state.httpRequestList = []
    }
    // 
    if (to.path == '/login') {
        util.userData.clearSessionStorage();
    }
    let user = JSON.parse(sessionStorage.getItem('user'));
    if (!user && to.path != '/login') {
        next({ path: '/login' })
    } else {
        next()
    }
});
// Vue对象
new Vue({
    //el: '#app',
    //template: '<App/>',
    router,
    store,
    //components: { App }
    render: h => h(App)
    //render: h => h(Test)
}).$mount('#app');