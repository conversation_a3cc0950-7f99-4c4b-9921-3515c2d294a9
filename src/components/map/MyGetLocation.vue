<template>
    <baidu-map
        style="display: flex; flex-direction: column-reverse; height: 500px" 
        id="allmap"
        @ready="mapReady"
        @click="getLocation"
        :scroll-wheel-zoom="true"
        @set-localtion = "setLocation">
        <div style="display: flex; align-items: center; margin-bottom: 5px; margin-bottom: 10px">
            <span style="margin-right: 10px">{{ labelTitle }}</span>
            <bm-auto-complete 
                v-if="isEdit" style="width: calc(100% - 150px);"
                v-model="address" 
                :sugStyle="{zIndex: bmAutoZindex}"
                @confirm="bmCompleteConfirm"
            >
                <el-input 
                    v-model="address"
                    style="width: 100%; margin-right: 15px" 
                    size="mini" 
                    :placeholder="`请输入${labelTitle}...`" 
                    @keyup.enter.native="getBaiduMapPoint"></el-input>
            </bm-auto-complete>
            <el-input 
                v-else 
                v-model="address" 
                :disabled="true" 
                style="width: calc(100% - 170px); margin-right: 15px"
                size="mini" 
                :placeholder="`请输入${labelTitle}...`" />
            <el-button @click="postBtnClick" style="margin-left: 10px" type="primary">{{ !isEdit ? '编辑' : '保存' }}</el-button>
        </div>
        <bm-map-type :map-types="['BMAP_NORMAL_MAP', 'BMAP_HYBRID_MAP']" anchor="BMAP_ANCHOR_TOP_LEFT"></bm-map-type>
        <bm-marker v-if="infoWindowShow" :position="{lng: longitude, lat: latitude}">
            <bm-label content="" :labelStyle="{color: 'red', fontSize : '24px'}" :offset="{width: 8, height: 30}"/>
        </bm-marker>
    </baidu-map>
</template>

<script>

export default {
    props: {
        labelTitle: {
            type: String,
            default: '店铺地址'
        }
    },
    data() {
        return {
            address: '',
            infoWindowShow: false,
            longitude: '',
            latitude: '',
            point: '',
            bmAutoZindex: 999999,
            isEdit: false,
        }
    },
    methods: {
        // 地图初始化
        mapReady({ BMap, map }) {
            // 选择一个经纬度作为中心点
            this.point = new BMap.Point(121.506673, 31.243691);
            map.centerAndZoom(this.point, 12);
            this.BMap = BMap;
            this.map = map;
            this.myGeo = new this.BMap.Geocoder();
            this.$emit('mapReady');
        },
        // 点击获取经纬度
        getLocation: function(e) {
            let that = this
            that.longitude = e.point.lng;
            that.latitude = e.point.lat;
            that.infoWindowShow = true;
        },
        // 设置经纬度
        setLocation: function(address, lng, lat) {
            this.address = address;
            this.isEdit = address ? false : true;
            this.longitude = lng;
            this.latitude = lat;
            if(!!lng && !!lat) {
                this.infoWindowShow = true;
                // 设置位置
                this.point = new BMap.Point(lng, lat);
                this.map.centerAndZoom(this.point, 12);
            } else {
                this.infoWindowShow = false;
                this.point = new BMap.Point(121.506673, 31.243691);
                this.map.centerAndZoom(this.point, 12);
            }
        },
        getBaiduMapPoint: function() {
            let that = this;
            const local = new this.BMap.LocalSearch(this.map, {
                onSearchComplete: function(results){
                    if (local.getStatus() == BMAP_STATUS_SUCCESS){
                        //查询成功，具体操作
                        if (results.Rr && results.Rr.length > 0) {
                            const point = results.Rr[0].point;
                            that.map.centerAndZoom(point,12);
                            that.longitude = point.lng;
                            that.latitude = point.lat;
                            that.infoWindowShow = true;
                        }
                    }
                }
            });
            local.setPageCapacity(5);  
            local.search(this.address);
        },
        bmCompleteConfirm(event) {
            this.address = event.item.value.business;
            this.getBaiduMapPoint();
        },
        // 信息窗口关闭
        infoWindowClose: function() {
            this.latitude = '';
            this.longitude = '';
            this.address = '';
            this.infoWindowShow = false;
        },

        postBtnClick() {
            if (this.isEdit) {
                this.$emit("setLocation", {address: this.address, lng: this.longitude, lat: this.latitude});
            }
            this.isEdit = !this.isEdit;
        }
    },
    // 创建VUE实例后的钩子
    created: function() {

    },
}
</script>

<style lang="scss" scoped>
#allmap{
    height: 300px;
    width: 100%;
    // margin: 10px 0;
}
</style>