{"name": "hm.hzpmanage.sites", "version": "1.0.0", "description": "a cosmetics manage site", "author": "LQ", "license": "MIT", "scripts": {"dev": "node build/dev-server.js", "start": "node build/dev-server.js", "build": "node build/build.js", "build:dev": "node build/buildDev.js"}, "dependencies": {"axios": "^0.19.0", "babel-runtime": "^6.26.0", "china-area-data": "^4.0.0", "cos-js-sdk-v5": "^0.5.17", "echarts": "^4.2.1", "element-ui": "^2.10.1", "file-saver": "^2.0.2", "font-awesome": "^4.7.0", "nprogress": "^0.2.0", "qrcode": "^1.3.3", "sortablejs": "^1.10.0-rc3", "vue": "^2.6.10", "vue-baidu-map": "^0.21.22", "vue-clipboard2": "^0.3.0", "vue-region-picker": "^3.0.1", "vue-router": "^3.0.6", "vue2-baidu-map": "^0.0.4", "vuex": "^3.1.1", "xlsx": "^0.14.3", "xlsx-style": "^0.8.13", "xmldom": "^0.1.27"}, "devDependencies": {"af-table-column": "^1.0.3", "autoprefixer": "^6.7.2", "axios-mock-adapter": "^1.7.1", "babel-core": "^6.22.1", "babel-loader": "^6.2.10", "babel-plugin-transform-runtime": "^6.22.0", "babel-polyfill": "^6.16.0", "babel-preset-env": "^1.2.1", "babel-preset-es2015": "^6.0.0", "babel-preset-stage-2": "^6.22.0", "babel-register": "^6.22.0", "chalk": "^1.1.3", "compression-webpack-plugin": "^1.1.11", "connect-history-api-fallback": "^1.3.0", "copy-webpack-plugin": "^4.0.1", "css-loader": "^0.26.1", "eventsource-polyfill": "^0.9.6", "express": "^4.14.1", "extract-text-webpack-plugin": "^2.0.0", "file-loader": "^0.10.0", "friendly-errors-webpack-plugin": "^1.1.3", "function-bind": "^1.0.2", "html-webpack-plugin": "^2.28.0", "http-proxy-middleware": "^0.17.3", "json-loader": "^0.5.4", "less": "^3.0.4", "less-loader": "^5.0.0", "mockjs": "^1.0.1-beta3", "opn": "^4.0.2", "optimize-css-assets-webpack-plugin": "^1.3.0", "ora": "^1.0.0", "rimraf": "^2.6.0", "sass": "^1.54.8", "sass-loader": "7.3.1", "semver": "^5.3.0", "shelljs": "^0.7.6", "url-loader": "^0.5.8", "vue-hot-reload-api": "^2.3.4", "vue-loader": "^11.1.4", "vue-style-loader": "^2.0.0", "vue-template-compiler": "^2.2.4", "webpack": "^2.2.1", "webpack-bundle-analyzer": "^2.2.1", "webpack-dev-middleware": "^1.10.0", "webpack-hot-middleware": "^2.16.1", "webpack-merge": "^2.6.1"}, "engines": {"node": ">= 4.0.0", "npm": ">= 3.0.0"}, "browserslist": ["> 1%", "last 2 versions", "not ie <= 8"]}