/*
 * @Author: <EMAIL> <EMAIL>
 * @Date: 2024-01-21 23:39:58
 * @LastEditors: <PERSON><PERSON> <EMAIL>
 * @LastEditTime: 2025-05-06 23:18:37
 * @FilePath: /shhzp-admin/config/index.js
 * @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
 */

// see http://vuejs-templates.github.io/webpack for documentation.
var path = require('path')

module.exports = {
  build: {
    env: require('./prod.env'),
    index: path.resolve(__dirname, '../dist/index.html'),
    assetsRoot: path.resolve(__dirname, '../dist'),
    assetsSubDirectory: 'static',
    assetsPublicPath: './',
    productionSourceMap: true,
    // Gzip off by default as many popular static hosts such as
    // Surge or Netlify already gzip all static assets for you.
    // Before setting to `true`, make sure to:
    // npm install --save-dev compression-webpack-plugin
    productionGzip: true,
    productionGzipExtensions: ['js', 'css'],
    // Run the build command with an extra argument to
    // View the bundle analyzer report after build finishes:
    // `npm run build --report`
    // Set to `true` or `false` to always turn it on or off
    bundleAnalyzerReport: process.env.npm_config_report
  },
  dev: {
    env: require('./dev.env'),
    port: 8081,
    autoOpenBrowser: true,
    assetsSubDirectory: 'static',
    assetsPublicPath: '/',
    proxyTable: {
      '/api2': {
        // target: 'http://*************:8000/api/v2',  // 测试
        target: 'https://ybnv2.api.hyfun.net/api/v2',  // 正式
        // target: 'http://127.0.0.1:8000/api/v2',  // 测试
        changeOrigin: true,
        pathRewrite: {
            '^/api2': '',  // 路径重写,
        }
      },
      '/api': {
        // target: 'http://*************:2222/shhzp',  // 测试
        target: 'https://ybn.api.hyfun.net',  // 正式
        changeOrigin: true,
        pathRewrite: {
          '^/api': '',  // 路径重写,
        }
      }
    },
    // CSS Sourcemaps off by default because relative paths are "buggy"
    // with this option, according to the CSS-Loader README
    // (https://github.com/webpack/css-loader#sourcemaps)
    // In our experience, they generally work as expected,
    // just be aware of this issue when enabling this option.
    cssSourceMap: false
  }
}
